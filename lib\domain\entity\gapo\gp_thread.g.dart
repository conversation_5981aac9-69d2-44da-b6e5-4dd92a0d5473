// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'gp_thread.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GPThread _$GPThreadFromJson(Map<String, dynamic> json) => GPThread(
      userId: (json['user_id'] as num?)?.toInt(),
      threadId: json['thread_id'] as String?,
      name: json['name'] as String?,
      participantIds: (json['participant_ids'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      type: $enumDecodeNullable(_$GPThreadTypeEnumMap, json['type']),
      isRead: (json['is_read'] as num?)?.toInt(),
    );

Map<String, dynamic> _$GPThreadToJson(GPThread instance) => <String, dynamic>{
      'user_id': instance.userId,
      'thread_id': instance.threadId,
      'name': instance.name,
      'participant_ids': instance.participantIds,
      'type': _$GPThreadTypeEnumMap[instance.type],
      'is_read': instance.isRead,
    };

const _$GPThreadTypeEnumMap = {
  GPThreadType.direct: 'direct',
  GPThreadType.group: 'group',
};
