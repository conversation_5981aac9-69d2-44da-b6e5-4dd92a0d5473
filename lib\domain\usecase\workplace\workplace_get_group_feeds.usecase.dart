/*
 * Created Date: Friday, 21st June 2024, 21:31:21
 * Author: To<PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Sunday, 25th August 2024 09:46:11
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:developer';

import 'package:gp_core/core.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';
import 'package:retry/retry.dart';

// ignore_for_file: public_member_api_docs

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class WorkPlaceGroupFeedsUseCase extends GPBaseFutureUseCase<
        WorkPlaceGroupFeedsInput, WorkPlaceListReponse<WorkPlaceFeedsResponse>>
    with WorkPlaceFetchAllDataMixin {
  WorkPlaceGroupFeedsUseCase(
    @Named('kWorkPlaceRepository') this._worplaceRepository,
  );

  final WorkPlaceRepository _worplaceRepository;

  @override
  Future<WorkPlaceListReponse<WorkPlaceFeedsResponse>> buildUseCase(
    WorkPlaceGroupFeedsInput input,
  ) async {
    return await retry(
      () async {
        final params = WorkPlaceBaseParams(
          id: input.groupId,
          fields: input.fields,
          limit: '10',
          since: input.since,
          nextQueries: input.nextQuery,
        );

        return await fetchAllData<WorkPlaceFeedsResponse>(
          params: params,
          loadFunction: _worplaceRepository.groupFeeds,
          saveData: input.saveData,
        );
      },
      maxAttempts: 2,
      retryIf: (e) {
        if (e is DioException && e.response?.statusCode == 400) {
          return false;
        }

        return true;
      },
      onRetry: (e) {
        log('ERROR: WorkPlaceGroupFeedsUseCase error -> $e');
      },
    );
  }
}

class WorkPlaceGroupFeedsInput extends GPBaseInput {
  const WorkPlaceGroupFeedsInput({
    required this.groupId,
    this.fields =
        'message,updated_time,from{id,email,name},attachments,to,formatting,created_time', //,comments{attachment,id,message,can_like,can_comment,can_remove,can_hide,can_reply_privately,comment_count,created_time,from,like_count,message_tags,object,parent,permalink_url,private_reply_conversation,user_likes}
    required this.saveData,
    this.since,
    this.nextQuery,
  });

  final String groupId;
  final String? fields;
  final int? since;
  final Future Function(List<WorkPlaceFeedsResponse>, Map<String, String>?)
      saveData;
  final Map<String, String>? nextQuery;
}
