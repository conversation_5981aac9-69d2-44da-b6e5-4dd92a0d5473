/*
 * Created Date: Saturday, 1st June 2024, 12:40:24
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 12th September 2024 22:54:38
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'dart:io';

import 'package:gp_fbwp_crawler/app/constant/constant.dart';
import 'package:gp_fbwp_crawler/domain/entity/entity.dart';
import 'package:gp_fbwp_crawler/app/app.dart';
import 'package:injectable/injectable.dart';
import 'package:isar/isar.dart';
import 'package:path_provider/path_provider.dart';

@module
abstract class DatabaseModule {
  @preResolve
  @Named('kIsar')
  Future<Isar> get kIsar async {
    final dir = await getApplicationDocumentsDirectory();

    final dirPath = '${dir.path}/multi-client/${AppConstants.databaseName}'; //vietin-bank-new-test
    final Directory directory = Directory(dirPath);
    if (!directory.existsSync()) {
      directory.createSync(recursive: true);
    }
    final isar = await Isar.open(
      AppConstants.dbSchemas,
      directory: dirPath,
      maxSizeMiB: Isar.defaultMaxSizeMiB * 10,
    );

    return isar;
  }
}
