// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'gp_group_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$GPGroupParams {
  String get name => throw _privateConstructorUsedError;
  String get cover => throw _privateConstructorUsedError;
  @JsonKey(name: 'link_chat')
  bool get linkChat => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  @JsonKey(toJson: _gpPrivacyToJson)
  GPGroupPrivacy get privacy => throw _privateConstructorUsedError;
  @JsonKey(toJson: _gpDiscoverabilityToJson)
  GPGroupDiscoverability get discoverability =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GPGroupParamsCopyWith<GPGroupParams> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GPGroupParamsCopyWith<$Res> {
  factory $GPGroupParamsCopyWith(
          GPGroupParams value, $Res Function(GPGroupParams) then) =
      _$GPGroupParamsCopyWithImpl<$Res, GPGroupParams>;
  @useResult
  $Res call(
      {String name,
      String cover,
      @JsonKey(name: 'link_chat') bool linkChat,
      String? description,
      @JsonKey(toJson: _gpPrivacyToJson) GPGroupPrivacy privacy,
      @JsonKey(toJson: _gpDiscoverabilityToJson)
      GPGroupDiscoverability discoverability});
}

/// @nodoc
class _$GPGroupParamsCopyWithImpl<$Res, $Val extends GPGroupParams>
    implements $GPGroupParamsCopyWith<$Res> {
  _$GPGroupParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? cover = null,
    Object? linkChat = null,
    Object? description = freezed,
    Object? privacy = null,
    Object? discoverability = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      cover: null == cover
          ? _value.cover
          : cover // ignore: cast_nullable_to_non_nullable
              as String,
      linkChat: null == linkChat
          ? _value.linkChat
          : linkChat // ignore: cast_nullable_to_non_nullable
              as bool,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      privacy: null == privacy
          ? _value.privacy
          : privacy // ignore: cast_nullable_to_non_nullable
              as GPGroupPrivacy,
      discoverability: null == discoverability
          ? _value.discoverability
          : discoverability // ignore: cast_nullable_to_non_nullable
              as GPGroupDiscoverability,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GPGroupParamsImplCopyWith<$Res>
    implements $GPGroupParamsCopyWith<$Res> {
  factory _$$GPGroupParamsImplCopyWith(
          _$GPGroupParamsImpl value, $Res Function(_$GPGroupParamsImpl) then) =
      __$$GPGroupParamsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String name,
      String cover,
      @JsonKey(name: 'link_chat') bool linkChat,
      String? description,
      @JsonKey(toJson: _gpPrivacyToJson) GPGroupPrivacy privacy,
      @JsonKey(toJson: _gpDiscoverabilityToJson)
      GPGroupDiscoverability discoverability});
}

/// @nodoc
class __$$GPGroupParamsImplCopyWithImpl<$Res>
    extends _$GPGroupParamsCopyWithImpl<$Res, _$GPGroupParamsImpl>
    implements _$$GPGroupParamsImplCopyWith<$Res> {
  __$$GPGroupParamsImplCopyWithImpl(
      _$GPGroupParamsImpl _value, $Res Function(_$GPGroupParamsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? cover = null,
    Object? linkChat = null,
    Object? description = freezed,
    Object? privacy = null,
    Object? discoverability = null,
  }) {
    return _then(_$GPGroupParamsImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      cover: null == cover
          ? _value.cover
          : cover // ignore: cast_nullable_to_non_nullable
              as String,
      linkChat: null == linkChat
          ? _value.linkChat
          : linkChat // ignore: cast_nullable_to_non_nullable
              as bool,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      privacy: null == privacy
          ? _value.privacy
          : privacy // ignore: cast_nullable_to_non_nullable
              as GPGroupPrivacy,
      discoverability: null == discoverability
          ? _value.discoverability
          : discoverability // ignore: cast_nullable_to_non_nullable
              as GPGroupDiscoverability,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _$GPGroupParamsImpl implements _GPGroupParams {
  const _$GPGroupParamsImpl(
      {required this.name,
      this.cover = '',
      @JsonKey(name: 'link_chat') this.linkChat = false,
      this.description,
      @JsonKey(toJson: _gpPrivacyToJson) this.privacy = GPGroupPrivacy.public,
      @JsonKey(toJson: _gpDiscoverabilityToJson)
      this.discoverability = GPGroupDiscoverability.visible});

  @override
  final String name;
  @override
  @JsonKey()
  final String cover;
  @override
  @JsonKey(name: 'link_chat')
  final bool linkChat;
  @override
  final String? description;
  @override
  @JsonKey(toJson: _gpPrivacyToJson)
  final GPGroupPrivacy privacy;
  @override
  @JsonKey(toJson: _gpDiscoverabilityToJson)
  final GPGroupDiscoverability discoverability;

  @override
  String toString() {
    return 'GPGroupParams(name: $name, cover: $cover, linkChat: $linkChat, description: $description, privacy: $privacy, discoverability: $discoverability)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GPGroupParamsImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.cover, cover) || other.cover == cover) &&
            (identical(other.linkChat, linkChat) ||
                other.linkChat == linkChat) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.privacy, privacy) || other.privacy == privacy) &&
            (identical(other.discoverability, discoverability) ||
                other.discoverability == discoverability));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, name, cover, linkChat,
      description, privacy, discoverability);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GPGroupParamsImplCopyWith<_$GPGroupParamsImpl> get copyWith =>
      __$$GPGroupParamsImplCopyWithImpl<_$GPGroupParamsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GPGroupParamsImplToJson(
      this,
    );
  }
}

abstract class _GPGroupParams implements GPGroupParams {
  const factory _GPGroupParams(
      {required final String name,
      final String cover,
      @JsonKey(name: 'link_chat') final bool linkChat,
      final String? description,
      @JsonKey(toJson: _gpPrivacyToJson) final GPGroupPrivacy privacy,
      @JsonKey(toJson: _gpDiscoverabilityToJson)
      final GPGroupDiscoverability discoverability}) = _$GPGroupParamsImpl;

  @override
  String get name;
  @override
  String get cover;
  @override
  @JsonKey(name: 'link_chat')
  bool get linkChat;
  @override
  String? get description;
  @override
  @JsonKey(toJson: _gpPrivacyToJson)
  GPGroupPrivacy get privacy;
  @override
  @JsonKey(toJson: _gpDiscoverabilityToJson)
  GPGroupDiscoverability get discoverability;
  @override
  @JsonKey(ignore: true)
  _$$GPGroupParamsImplCopyWith<_$GPGroupParamsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
