// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workplace_feed.entity.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetWorkPlaceFeedEntityCollection on Isar {
  IsarCollection<WorkPlaceFeedEntity> get workPlaceFeedEntitys =>
      this.collection();
}

const WorkPlaceFeedEntitySchema = CollectionSchema(
  name: r'WorkPlaceFeedEntity',
  id: -5480210398522061734,
  properties: {
    r'crawlType': PropertySchema(
      id: 0,
      name: r'crawlType',
      type: IsarType.byte,
      enumMap: _WorkPlaceFeedEntitycrawlTypeEnumValueMap,
    ),
    r'createdTime': PropertySchema(
      id: 1,
      name: r'createdTime',
      type: IsarType.dateTime,
    ),
    r'formatting': PropertySchema(
      id: 2,
      name: r'formatting',
      type: IsarType.string,
      enumMap: _WorkPlaceFeedEntityformattingEnumValueMap,
    ),
    r'gpUserId': PropertySchema(
      id: 3,
      name: r'gpUserId',
      type: IsarType.long,
    ),
    r'id': PropertySchema(
      id: 4,
      name: r'id',
      type: IsarType.string,
    ),
    r'insertedAt': PropertySchema(
      id: 5,
      name: r'insertedAt',
      type: IsarType.dateTime,
    ),
    r'isUserFeed': PropertySchema(
      id: 6,
      name: r'isUserFeed',
      type: IsarType.bool,
    ),
    r'message': PropertySchema(
      id: 7,
      name: r'message',
      type: IsarType.string,
    ),
    r'reactions': PropertySchema(
      id: 8,
      name: r'reactions',
      type: IsarType.objectList,
      target: r'WorkPlaceReactionEntity',
    ),
    r'updatedTime': PropertySchema(
      id: 9,
      name: r'updatedTime',
      type: IsarType.dateTime,
    )
  },
  estimateSize: _workPlaceFeedEntityEstimateSize,
  serialize: _workPlaceFeedEntitySerialize,
  deserialize: _workPlaceFeedEntityDeserialize,
  deserializeProp: _workPlaceFeedEntityDeserializeProp,
  idName: r'dbId',
  indexes: {},
  links: {
    r'comments': LinkSchema(
      id: 8902036175676540421,
      name: r'comments',
      target: r'WorkPlaceCommentEntity',
      single: false,
    ),
    r'attachments': LinkSchema(
      id: -6378952380334805593,
      name: r'attachments',
      target: r'WorkPlaceAttachmentEntity',
      single: false,
    ),
    r'to': LinkSchema(
      id: 8523899714210134466,
      name: r'to',
      target: r'WorkPlaceCommunityMemberEntity',
      single: false,
    ),
    r'from': LinkSchema(
      id: -2521183196215706613,
      name: r'from',
      target: r'WorkPlaceCommunityMemberEntity',
      single: true,
    ),
    r'group': LinkSchema(
      id: 7278285186729295996,
      name: r'group',
      target: r'WorkPlaceGroupEntity',
      single: true,
    ),
    r'seen': LinkSchema(
      id: 4809982062091525558,
      name: r'seen',
      target: r'WorkPlaceCommunityMemberEntity',
      single: false,
    )
  },
  embeddedSchemas: {r'WorkPlaceReactionEntity': WorkPlaceReactionEntitySchema},
  getId: _workPlaceFeedEntityGetId,
  getLinks: _workPlaceFeedEntityGetLinks,
  attach: _workPlaceFeedEntityAttach,
  version: '3.1.0+1',
);

int _workPlaceFeedEntityEstimateSize(
  WorkPlaceFeedEntity object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.formatting;
    if (value != null) {
      bytesCount += 3 + value.name.length * 3;
    }
  }
  bytesCount += 3 + object.id.length * 3;
  {
    final value = object.message;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final list = object.reactions;
    if (list != null) {
      bytesCount += 3 + list.length * 3;
      {
        final offsets = allOffsets[WorkPlaceReactionEntity]!;
        for (var i = 0; i < list.length; i++) {
          final value = list[i];
          bytesCount += WorkPlaceReactionEntitySchema.estimateSize(
              value, offsets, allOffsets);
        }
      }
    }
  }
  return bytesCount;
}

void _workPlaceFeedEntitySerialize(
  WorkPlaceFeedEntity object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeByte(offsets[0], object.crawlType.index);
  writer.writeDateTime(offsets[1], object.createdTime);
  writer.writeString(offsets[2], object.formatting?.name);
  writer.writeLong(offsets[3], object.gpUserId);
  writer.writeString(offsets[4], object.id);
  writer.writeDateTime(offsets[5], object.insertedAt);
  writer.writeBool(offsets[6], object.isUserFeed);
  writer.writeString(offsets[7], object.message);
  writer.writeObjectList<WorkPlaceReactionEntity>(
    offsets[8],
    allOffsets,
    WorkPlaceReactionEntitySchema.serialize,
    object.reactions,
  );
  writer.writeDateTime(offsets[9], object.updatedTime);
}

WorkPlaceFeedEntity _workPlaceFeedEntityDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = WorkPlaceFeedEntity(
    crawlType: _WorkPlaceFeedEntitycrawlTypeValueEnumMap[
            reader.readByteOrNull(offsets[0])] ??
        GPBaseCrawlType.feed,
    createdTime: reader.readDateTimeOrNull(offsets[1]),
    formatting: _WorkPlaceFeedEntityformattingValueEnumMap[
        reader.readStringOrNull(offsets[2])],
    id: reader.readString(offsets[4]),
    insertedAt: reader.readDateTimeOrNull(offsets[5]),
    isUserFeed: reader.readBoolOrNull(offsets[6]) ?? false,
    message: reader.readStringOrNull(offsets[7]),
    reactions: reader.readObjectList<WorkPlaceReactionEntity>(
      offsets[8],
      WorkPlaceReactionEntitySchema.deserialize,
      allOffsets,
      WorkPlaceReactionEntity(),
    ),
    updatedTime: reader.readDateTimeOrNull(offsets[9]),
  );
  object.gpUserId = reader.readLongOrNull(offsets[3]);
  return object;
}

P _workPlaceFeedEntityDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (_WorkPlaceFeedEntitycrawlTypeValueEnumMap[
              reader.readByteOrNull(offset)] ??
          GPBaseCrawlType.feed) as P;
    case 1:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 2:
      return (_WorkPlaceFeedEntityformattingValueEnumMap[
          reader.readStringOrNull(offset)]) as P;
    case 3:
      return (reader.readLongOrNull(offset)) as P;
    case 4:
      return (reader.readString(offset)) as P;
    case 5:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 6:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 7:
      return (reader.readStringOrNull(offset)) as P;
    case 8:
      return (reader.readObjectList<WorkPlaceReactionEntity>(
        offset,
        WorkPlaceReactionEntitySchema.deserialize,
        allOffsets,
        WorkPlaceReactionEntity(),
      )) as P;
    case 9:
      return (reader.readDateTimeOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _WorkPlaceFeedEntitycrawlTypeEnumValueMap = {
  'user': 0,
  'group': 1,
  'thread': 2,
  'message': 3,
  'feed': 4,
  'userFeed': 5,
  'comment': 6,
  'attachment': 7,
  'community': 8,
  'sticker': 9,
  'reaction': 10,
  'commentReaction': 11,
  'groupMember': 12,
  'postSeen': 13,
  'messageAttachment': 14,
};
const _WorkPlaceFeedEntitycrawlTypeValueEnumMap = {
  0: GPBaseCrawlType.user,
  1: GPBaseCrawlType.group,
  2: GPBaseCrawlType.thread,
  3: GPBaseCrawlType.message,
  4: GPBaseCrawlType.feed,
  5: GPBaseCrawlType.userFeed,
  6: GPBaseCrawlType.comment,
  7: GPBaseCrawlType.attachment,
  8: GPBaseCrawlType.community,
  9: GPBaseCrawlType.sticker,
  10: GPBaseCrawlType.reaction,
  11: GPBaseCrawlType.commentReaction,
  12: GPBaseCrawlType.groupMember,
  13: GPBaseCrawlType.postSeen,
  14: GPBaseCrawlType.messageAttachment,
};
const _WorkPlaceFeedEntityformattingEnumValueMap = {
  r'markdown': r'markdown',
  r'plainText': r'plainText',
};
const _WorkPlaceFeedEntityformattingValueEnumMap = {
  r'markdown': WorkPlaceFormatting.markdown,
  r'plainText': WorkPlaceFormatting.plainText,
};

Id _workPlaceFeedEntityGetId(WorkPlaceFeedEntity object) {
  return object.dbId ?? Isar.autoIncrement;
}

List<IsarLinkBase<dynamic>> _workPlaceFeedEntityGetLinks(
    WorkPlaceFeedEntity object) {
  return [
    object.comments,
    object.attachments,
    object.to,
    object.from,
    object.group,
    object.seen
  ];
}

void _workPlaceFeedEntityAttach(
    IsarCollection<dynamic> col, Id id, WorkPlaceFeedEntity object) {
  object.comments.attach(
      col, col.isar.collection<WorkPlaceCommentEntity>(), r'comments', id);
  object.attachments.attach(col,
      col.isar.collection<WorkPlaceAttachmentEntity>(), r'attachments', id);
  object.to.attach(
      col, col.isar.collection<WorkPlaceCommunityMemberEntity>(), r'to', id);
  object.from.attach(
      col, col.isar.collection<WorkPlaceCommunityMemberEntity>(), r'from', id);
  object.group
      .attach(col, col.isar.collection<WorkPlaceGroupEntity>(), r'group', id);
  object.seen.attach(
      col, col.isar.collection<WorkPlaceCommunityMemberEntity>(), r'seen', id);
}

extension WorkPlaceFeedEntityQueryWhereSort
    on QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QWhere> {
  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterWhere>
      anyDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension WorkPlaceFeedEntityQueryWhere
    on QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QWhereClause> {
  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterWhereClause>
      dbIdEqualTo(Id dbId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: dbId,
        upper: dbId,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterWhereClause>
      dbIdNotEqualTo(Id dbId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: dbId, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: dbId, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: dbId, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: dbId, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterWhereClause>
      dbIdGreaterThan(Id dbId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: dbId, includeLower: include),
      );
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterWhereClause>
      dbIdLessThan(Id dbId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: dbId, includeUpper: include),
      );
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterWhereClause>
      dbIdBetween(
    Id lowerDbId,
    Id upperDbId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerDbId,
        includeLower: includeLower,
        upper: upperDbId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension WorkPlaceFeedEntityQueryFilter on QueryBuilder<WorkPlaceFeedEntity,
    WorkPlaceFeedEntity, QFilterCondition> {
  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      crawlTypeEqualTo(GPBaseCrawlType value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      crawlTypeGreaterThan(
    GPBaseCrawlType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      crawlTypeLessThan(
    GPBaseCrawlType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      crawlTypeBetween(
    GPBaseCrawlType lower,
    GPBaseCrawlType upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'crawlType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      createdTimeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdTime',
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      createdTimeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdTime',
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      createdTimeEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdTime',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      createdTimeGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdTime',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      createdTimeLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdTime',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      createdTimeBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdTime',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      dbIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'dbId',
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      dbIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'dbId',
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      dbIdEqualTo(Id? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      dbIdGreaterThan(
    Id? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      dbIdLessThan(
    Id? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      dbIdBetween(
    Id? lower,
    Id? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'dbId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      formattingIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'formatting',
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      formattingIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'formatting',
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      formattingEqualTo(
    WorkPlaceFormatting? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'formatting',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      formattingGreaterThan(
    WorkPlaceFormatting? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'formatting',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      formattingLessThan(
    WorkPlaceFormatting? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'formatting',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      formattingBetween(
    WorkPlaceFormatting? lower,
    WorkPlaceFormatting? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'formatting',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      formattingStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'formatting',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      formattingEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'formatting',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      formattingContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'formatting',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      formattingMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'formatting',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      formattingIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'formatting',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      formattingIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'formatting',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      gpUserIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'gpUserId',
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      gpUserIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'gpUserId',
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      gpUserIdEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'gpUserId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      gpUserIdGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'gpUserId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      gpUserIdLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'gpUserId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      gpUserIdBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'gpUserId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      idEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      idGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      idLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      idBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      idStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      idEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      idContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      idMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'id',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      idIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      idIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      insertedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'insertedAt',
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      insertedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'insertedAt',
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      insertedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'insertedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      insertedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'insertedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      insertedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'insertedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      insertedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'insertedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      isUserFeedEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isUserFeed',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      messageIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'message',
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      messageIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'message',
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      messageEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      messageGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      messageLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      messageBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'message',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      messageStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      messageEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      messageContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      messageMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'message',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      messageIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'message',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      messageIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'message',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      reactionsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'reactions',
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      reactionsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'reactions',
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      reactionsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'reactions',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      reactionsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'reactions',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      reactionsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'reactions',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      reactionsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'reactions',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      reactionsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'reactions',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      reactionsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'reactions',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      updatedTimeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedTime',
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      updatedTimeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedTime',
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      updatedTimeEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedTime',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      updatedTimeGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedTime',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      updatedTimeLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedTime',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      updatedTimeBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedTime',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension WorkPlaceFeedEntityQueryObject on QueryBuilder<WorkPlaceFeedEntity,
    WorkPlaceFeedEntity, QFilterCondition> {
  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      reactionsElement(FilterQuery<WorkPlaceReactionEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'reactions');
    });
  }
}

extension WorkPlaceFeedEntityQueryLinks on QueryBuilder<WorkPlaceFeedEntity,
    WorkPlaceFeedEntity, QFilterCondition> {
  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      comments(FilterQuery<WorkPlaceCommentEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'comments');
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      commentsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'comments', length, true, length, true);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      commentsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'comments', 0, true, 0, true);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      commentsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'comments', 0, false, 999999, true);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      commentsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'comments', 0, true, length, include);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      commentsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'comments', length, include, 999999, true);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      commentsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(
          r'comments', lower, includeLower, upper, includeUpper);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      attachments(FilterQuery<WorkPlaceAttachmentEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'attachments');
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      attachmentsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'attachments', length, true, length, true);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      attachmentsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'attachments', 0, true, 0, true);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      attachmentsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'attachments', 0, false, 999999, true);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      attachmentsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'attachments', 0, true, length, include);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      attachmentsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'attachments', length, include, 999999, true);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      attachmentsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(
          r'attachments', lower, includeLower, upper, includeUpper);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      to(FilterQuery<WorkPlaceCommunityMemberEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'to');
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      toLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'to', length, true, length, true);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      toIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'to', 0, true, 0, true);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      toIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'to', 0, false, 999999, true);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      toLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'to', 0, true, length, include);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      toLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'to', length, include, 999999, true);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      toLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'to', lower, includeLower, upper, includeUpper);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      from(FilterQuery<WorkPlaceCommunityMemberEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'from');
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      fromIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'from', 0, true, 0, true);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      group(FilterQuery<WorkPlaceGroupEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'group');
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      groupIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'group', 0, true, 0, true);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      seen(FilterQuery<WorkPlaceCommunityMemberEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'seen');
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      seenLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'seen', length, true, length, true);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      seenIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'seen', 0, true, 0, true);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      seenIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'seen', 0, false, 999999, true);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      seenLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'seen', 0, true, length, include);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      seenLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'seen', length, include, 999999, true);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterFilterCondition>
      seenLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(
          r'seen', lower, includeLower, upper, includeUpper);
    });
  }
}

extension WorkPlaceFeedEntityQuerySortBy
    on QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QSortBy> {
  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      sortByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      sortByCrawlTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      sortByCreatedTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdTime', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      sortByCreatedTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdTime', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      sortByFormatting() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'formatting', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      sortByFormattingDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'formatting', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      sortByGpUserId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpUserId', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      sortByGpUserIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpUserId', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      sortById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      sortByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      sortByInsertedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'insertedAt', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      sortByInsertedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'insertedAt', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      sortByIsUserFeed() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isUserFeed', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      sortByIsUserFeedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isUserFeed', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      sortByMessage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'message', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      sortByMessageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'message', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      sortByUpdatedTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedTime', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      sortByUpdatedTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedTime', Sort.desc);
    });
  }
}

extension WorkPlaceFeedEntityQuerySortThenBy
    on QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QSortThenBy> {
  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      thenByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      thenByCrawlTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      thenByCreatedTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdTime', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      thenByCreatedTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdTime', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      thenByDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dbId', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      thenByDbIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dbId', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      thenByFormatting() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'formatting', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      thenByFormattingDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'formatting', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      thenByGpUserId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpUserId', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      thenByGpUserIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpUserId', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      thenByInsertedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'insertedAt', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      thenByInsertedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'insertedAt', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      thenByIsUserFeed() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isUserFeed', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      thenByIsUserFeedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isUserFeed', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      thenByMessage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'message', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      thenByMessageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'message', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      thenByUpdatedTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedTime', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QAfterSortBy>
      thenByUpdatedTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedTime', Sort.desc);
    });
  }
}

extension WorkPlaceFeedEntityQueryWhereDistinct
    on QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QDistinct> {
  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QDistinct>
      distinctByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'crawlType');
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QDistinct>
      distinctByCreatedTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdTime');
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QDistinct>
      distinctByFormatting({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'formatting', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QDistinct>
      distinctByGpUserId() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'gpUserId');
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QDistinct>
      distinctById({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'id', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QDistinct>
      distinctByInsertedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'insertedAt');
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QDistinct>
      distinctByIsUserFeed() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isUserFeed');
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QDistinct>
      distinctByMessage({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'message', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QDistinct>
      distinctByUpdatedTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedTime');
    });
  }
}

extension WorkPlaceFeedEntityQueryProperty
    on QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFeedEntity, QQueryProperty> {
  QueryBuilder<WorkPlaceFeedEntity, int, QQueryOperations> dbIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'dbId');
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, GPBaseCrawlType, QQueryOperations>
      crawlTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'crawlType');
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, DateTime?, QQueryOperations>
      createdTimeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdTime');
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, WorkPlaceFormatting?, QQueryOperations>
      formattingProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'formatting');
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, int?, QQueryOperations> gpUserIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'gpUserId');
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, String, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, DateTime?, QQueryOperations>
      insertedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'insertedAt');
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, bool, QQueryOperations>
      isUserFeedProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isUserFeed');
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, String?, QQueryOperations>
      messageProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'message');
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, List<WorkPlaceReactionEntity>?,
      QQueryOperations> reactionsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'reactions');
    });
  }

  QueryBuilder<WorkPlaceFeedEntity, DateTime?, QQueryOperations>
      updatedTimeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedTime');
    });
  }
}
