import 'package:gp_fbwp_crawler/domain/entity/entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'gp_group.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class GPGroup {
  GPGroup({
    this.name,
    this.description,
    this.privacy,
    this.discoverability,
    this.wpGroupId,
    this.cover,
    this.createdAt,
    this.previewMembers,
    this.owner,
  });
  final String? wpGroupId;
  final String? name;
  final String? description;
  final String? cover;
  final GPGroupPrivacy? privacy;
  final GPGroupDiscoverability? discoverability;

  final String? createdAt;

  @J<PERSON><PERSON>ey(toJson: _previewMembersToJson)
  final List<GPUser>? previewMembers;

  @J<PERSON><PERSON>ey(toJson: _ownerToJson)
  final GPUser? owner;

  factory GPGroup.fromJson(Map<String, dynamic> json) =>
      _$GPGroupFromJson(json);

  Map<String, dynamic> toJson() => _$GPGroupToJson(this);

  static List<int?>? _previewMembersToJson(List<GPUser>? input) {
    if (input == null) return null;

    return input.map((e) => e.id).toList();
  }

  static int? _ownerToJson(GPUser? owner) {
    return owner?.id;
  }
}
