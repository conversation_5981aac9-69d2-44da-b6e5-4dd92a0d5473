// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workplace_reactions.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkPlaceReaction _$WorkPlaceReactionFromJson(Map<String, dynamic> json) =>
    WorkPlaceReaction(
      userId: json['id'] as String,
      username: json['name'] as String,
      type: $enumDecodeNullable(_$WorkPlaceReactionTypeEnumMap, json['type']) ??
          WorkPlaceReactionType.none,
    );

Map<String, dynamic> _$WorkPlaceReactionToJson(WorkPlaceReaction instance) =>
    <String, dynamic>{
      'id': instance.userId,
      'name': instance.username,
      'type': _$WorkPlaceReactionTypeEnumMap[instance.type]!,
    };

const _$WorkPlaceReactionTypeEnumMap = {
  WorkPlaceReactionType.like: 'LIKE',
  WorkPlaceReactionType.love: 'LOVE',
  WorkPlaceReactionType.angry: 'ANGRY',
  WorkPlaceReactionType.wow: 'WOW',
  WorkPlaceReactionType.sad: 'SAD',
  WorkPlaceReactionType.haha: 'HAHA',
  WorkPlaceReactionType.care: 'CARE',
  WorkPlaceReactionType.none: '',
};
