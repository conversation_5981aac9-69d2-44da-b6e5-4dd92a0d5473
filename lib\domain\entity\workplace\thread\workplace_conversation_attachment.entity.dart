/*
 * Created Date: Friday, 21st June 2024, 09:59:32
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Wednesday, 11th September 2024 19:42:37
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_fbwp_crawler/domain/entity/entity.dart';
import 'package:isar/isar.dart';

part 'workplace_conversation_attachment.entity.g.dart';

@Collection()
class WorkPlaceConversationAttachmentsEntity extends BaseCrawlEntity {
  WorkPlaceConversationAttachmentsEntity({
    required super.id,
    super.crawlType = GPBaseCrawlType.attachment,
    this.mimeType,
    this.name,
    this.size,
    this.imageData,
    this.fileUrl,
    this.videoData,
  });

  final String? mimeType;
  final String? name;
  final int? size;

  final AttachmentImageDataEntity? imageData;
  final AttachmentVideoDataEntity? videoData;

  final String? fileUrl;
  String? gpLink;
  String? localFilePath;
  
  UploadResponseEntity? uploadResponse;

  String? get src {
    if (mimeType?.contains('image') ?? false) {
      return imageData?.url;
    } else if (mimeType?.contains('video') ?? false) {
      return videoData?.url;
    } else {
      return fileUrl;
    }
  }

  late final Id? dbId = id.hashCode;
}

@embedded
class AttachmentImageDataEntity {
  AttachmentImageDataEntity({
    this.height,
    this.width,
    this.url,
    this.imageType,
  });

  final int? height;
  final int? width;
  final String? url;
  final int? imageType;
}

@embedded
class AttachmentVideoDataEntity {
  AttachmentVideoDataEntity({
    this.height,
    this.width,
    this.url,
    this.length,
    this.videoType,
  });

  final int? height;
  final int? width;
  final String? url;
  final int? length;
  final int? videoType;
}
