import 'dart:convert';
import 'dart:developer';

import 'package:csv/csv.dart';
import 'package:strings/strings.dart';

class CsvHelper {
  const CsvHelper._();

  static String toCSV(
    List<Map<String, dynamic>> list, {
    List<String>? ignoreFields,
    bool isEmpty = false,
  }) {
    List<Map<String, dynamic>> filtedList = list;
    List<List<dynamic>> rows = [];

    if (list.isEmpty) return '';

    filtedList = list.map<Map<String, dynamic>>((Map<String, dynamic> e) {
      final Map<String, dynamic> newMap;
      if (ignoreFields != null) {
        newMap = e..removeWhere((key, value) => ignoreFields.contains(key));
      } else {
        newMap = e;
      }

      return newMap.map((key, value) {
        dynamic escaped;
        if (value is Map) {
          escaped = Strings.toEscaped(value.toString());
        } else if (value is String) {
          escaped = Strings.toEscaped(value).replaceAll(';', ',');
        } else if (value is List<Map>) {
          escaped = '\\${Strings.toEscaped(jsonEncode(value))}\\';
        } else if (value is List<int>) {
          escaped = '\\${jsonEncode(value)}\\';
        } else if (value is List) {
          final List<dynamic> listData = value;
          escaped =
              '\\${listData.map((e) => Strings.toEscaped('\"$e\"')).toList()}\\';
        } else {
          escaped = value;
        }

        return MapEntry(
          key,
          escaped,
        );
      });
    }).toList();

    final header = filtedList.first.keys.toList();
    rows.add(header);

    if (isEmpty == false) {
      for (var map in filtedList) {
        rows.add(map.values.toList());
      }
    }

    String csv = const ListToCsvConverter(
            fieldDelimiter: ';',
            textDelimiter: '\'',
            textEndDelimiter: '\'',
            eol: '\n',
            convertNullTo: '',
            delimitAllFields: true)
        .convert(rows);

    // log(csv);

    return csv;
  }
}
