import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'workplace_group_response.g.dart';

@JsonSerializable(createToJson: false, fieldRename: FieldRename.snake)
@DateTimeConverter()
class WorkPlaceGroupResponse {
  WorkPlaceGroupResponse({
    required this.id,
    this.name,
    this.privacy,
    this.createdTime,
    this.updatedTime,
    this.archived,
    this.postRequiresAdminApproval,
    this.cover,
    this.icon,
    this.owner,
    this.admins,
    this.members,
    this.description,
  });

  final String id;

  final String? name;
  final String? description;
  final WorkPlaceGroupPrivacy? privacy;
  final DateTime? createdTime;
  final DateTime? updatedTime;
  final bool? archived;
  final bool? postRequiresAdminApproval;

  final GroupCover? cover;
  final String? icon;
  final WorkPlaceUser? owner;

  final WorkPlaceListReponse<WorkPlaceUser>? admins;
  final WorkPlaceListReponse<WorkPlaceUser>? members;

  factory WorkPlaceGroupResponse.fromJson(Map<String, dynamic> json) =>
      _$WorkPlaceGroupResponseFromJson(json);
}

@JsonSerializable(createToJson: false, fieldRename: FieldRename.snake)
class GroupCover {
  GroupCover({this.id, this.source, this.coverId, this.offsetX, this.offsetY});

  String? id;
  String? source;
  String? coverId;
  int? offsetX;
  int? offsetY;

  factory GroupCover.fromJson(Map<String, dynamic> json) =>
      _$GroupCoverFromJson(json);
}
