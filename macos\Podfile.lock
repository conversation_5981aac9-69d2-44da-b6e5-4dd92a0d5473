PODS:
  - connectivity_plus (0.0.1):
    - FlutterMacOS
    - ReachabilitySwift
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - emoji_picker_flutter (0.0.1):
    - FlutterMacOS
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - flutter_secure_storage_macos (6.1.1):
    - FlutterMacOS
  - flutter_udid (0.0.1):
    - FlutterMacOS
    - SAMKeychain
  - FlutterMacOS (1.0.0)
  - isar_flutter_libs (1.0.0):
    - FlutterMacOS
  - package_info (0.0.1):
    - FlutterMacOS
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - pasteboard (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - ReachabilitySwift (5.0.0)
  - SAMKeychain (1.5.3)
  - Sentry/HybridSDK (8.15.2):
    - SentryPrivate (= 8.15.2)
  - sentry_flutter (0.0.1):
    - Flutter
    - FlutterMacOS
    - Sentry/HybridSDK (= 8.15.2)
  - SentryPrivate (8.15.2)
  - share_plus (0.0.1):
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - url_launcher_macos (0.0.1):
    - FlutterMacOS

DEPENDENCIES:
  - connectivity_plus (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos`)
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - emoji_picker_flutter (from `Flutter/ephemeral/.symlinks/plugins/emoji_picker_flutter/macos`)
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - flutter_secure_storage_macos (from `Flutter/ephemeral/.symlinks/plugins/flutter_secure_storage_macos/macos`)
  - flutter_udid (from `Flutter/ephemeral/.symlinks/plugins/flutter_udid/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - isar_flutter_libs (from `Flutter/ephemeral/.symlinks/plugins/isar_flutter_libs/macos`)
  - package_info (from `Flutter/ephemeral/.symlinks/plugins/package_info/macos`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - pasteboard (from `Flutter/ephemeral/.symlinks/plugins/pasteboard/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - sentry_flutter (from `Flutter/ephemeral/.symlinks/plugins/sentry_flutter/macos`)
  - share_plus (from `Flutter/ephemeral/.symlinks/plugins/share_plus/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `Flutter/ephemeral/.symlinks/plugins/sqflite/darwin`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)

SPEC REPOS:
  trunk:
    - ReachabilitySwift
    - SAMKeychain
    - Sentry
    - SentryPrivate

EXTERNAL SOURCES:
  connectivity_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  emoji_picker_flutter:
    :path: Flutter/ephemeral/.symlinks/plugins/emoji_picker_flutter/macos
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  flutter_secure_storage_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_secure_storage_macos/macos
  flutter_udid:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_udid/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  isar_flutter_libs:
    :path: Flutter/ephemeral/.symlinks/plugins/isar_flutter_libs/macos
  package_info:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info/macos
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  pasteboard:
    :path: Flutter/ephemeral/.symlinks/plugins/pasteboard/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  sentry_flutter:
    :path: Flutter/ephemeral/.symlinks/plugins/sentry_flutter/macos
  share_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/share_plus/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  sqflite:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite/darwin
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos

SPEC CHECKSUMS:
  connectivity_plus: 18d3c32514c886e046de60e9c13895109866c747
  device_info_plus: 5401765fde0b8d062a2f8eb65510fb17e77cf07f
  emoji_picker_flutter: 533634326b1c5de9a181ba14b9758e6dfe967a20
  file_selector_macos: 54fdab7caa3ac3fc43c9fac4d7d8d231277f8cf2
  flutter_secure_storage_macos: 59459653abe1adb92abbc8ea747d79f8d19866c9
  flutter_udid: 6b2b89780c3dfeecf0047bdf93f622d6416b1c07
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  isar_flutter_libs: 43385c99864c168fadba7c9adeddc5d38838ca6a
  package_info: 6eba2fd8d3371dda2d85c8db6fe97488f24b74b2
  package_info_plus: 02d7a575e80f194102bef286361c6c326e4c29ce
  pasteboard: 9b69dba6fedbb04866be632205d532fe2f6b1d99
  path_provider_foundation: 3784922295ac71e43754bd15e0653ccfd36a147c
  ReachabilitySwift: 985039c6f7b23a1da463388634119492ff86c825
  SAMKeychain: 483e1c9f32984d50ca961e26818a534283b4cd5c
  Sentry: 6f5742b4c47c17c9adcf265f6f328cf4a0ed1923
  sentry_flutter: 2c309a1d4b45e59d02cfa15795705687f1e2081b
  SentryPrivate: b2f7996f37781080f04a946eb4e377ff63c64195
  share_plus: 76dd39142738f7a68dd57b05093b5e8193f220f7
  shared_preferences_foundation: b4c3b4cddf1c21f02770737f147a3f5da9d39695
  sqflite: 673a0e54cc04b7d6dba8d24fb8095b31c3a99eec
  url_launcher_macos: 5f437abeda8c85500ceb03f5c1938a8c5a705399

PODFILE CHECKSUM: e6941affa76272697b93766f7a7df72c8e5d2cb8

COCOAPODS: 1.13.0
