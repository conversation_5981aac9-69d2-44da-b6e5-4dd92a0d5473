// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'gp_group_adjust_member_role_params.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Map<String, dynamic> _$$GPGroupAdjustMemberRoleParamsImplToJson(
        _$GPGroupAdjustMemberRoleParamsImpl instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'role': _$GPGroupRoleEnumMap[instance.role]!,
    };

const _$GPGroupRoleEnumMap = {
  GPGroupRole.owner: 1,
  GPGroupRole.admin: 2,
  GPGroupRole.moderator: 3,
  GPGroupRole.member: 4,
};
