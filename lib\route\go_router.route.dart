/*
 * Created Date: Saturday, 1st June 2024, 10:59:09
 * Author: ToanNM
 * -----
 * Last Modified: Saturday, 7th September 2024 21:08:11
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:gp_fbwp_crawler/app/app.dart';

part 'go_router.route.g.dart';

const kSplash = '/';
const kHome = '/home';
const kCrawl = '/crawl';
const kexportCSV = '/export-csv';

// ---------- Init main routes here ---------- \\
@TypedGoRoute<SplashRouteData>(
  path: kSplash,
  routes: [],
)
class SplashRouteData extends GoRouteData {
  const SplashRouteData();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const GPSplashScreen();
  }
}

@TypedGoRoute<HomeRouteData>(
  path: kHome,
  routes: [],
)
class HomeRouteData extends GoRouteData {
  const HomeRouteData();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const HomePage();
  }
}

@TypedGoRoute<CrawlRouteData>(
  path: kCrawl,
  routes: [],
)
class CrawlRouteData extends GoRouteData {
  const CrawlRouteData();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const CrawlSyncPage();
  }
}

@TypedGoRoute<ExportCSVRouteData>(
  path: kexportCSV,
  routes: [],
)
class ExportCSVRouteData extends GoRouteData {
  const ExportCSVRouteData();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const ExportCSVPage();
  }
}

class ExportCSVPage extends StatelessWidget {
  const ExportCSVPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const Placeholder();
  }
}
