#!/bin/bash

# File to store the environment variables
ENV_FILE=".env"

# File to store the computer ids
FILE="data/computer_ids.txt"

generate_random_string() {
    LC_ALL=C < /dev/urandom tr -dc 'A-Za-z0-9' | head -c 16
}

update_computer_ids_file() {
    local rand_str=$1

    # Check if file exists, if not create it
    if [ ! -f "$FILE" ]; then
        mkdir "data"
        touch "$FILE"
        echo "File created: $FILE"
    fi

    # Append the random string to the file, separated by commas
    if [ -s "$FILE" ]; then
        # If file is not empty, append with a comma
        printf ",$rand_str" >> "$FILE"
    else
        # If file is empty, just write the string
        printf "$rand_str" >> "$FILE"
    fi

    echo "Computer Id added: $rand_str"
}

# Function to add or update a key-value pair in the .env file
update_env() {
    local key=$1
    local value=$2

    # Generate a random string if value is "RANDOM"
    if [ "$value" = "" ]; then
        value=$(generate_random_string)
        update_computer_ids_file "$value"
    fi

    # Check if the key already exists in the file
    if grep -q "^${key}=" "$ENV_FILE"; then
        # If it exists, update the value
        sed -i.bak "s/^${key}=.*/${key}=${value}/" "$ENV_FILE" && rm "${ENV_FILE}.bak"
    else
        # If it doesn't exist, append it to the file
        printf "${key}=${value}" >> "$ENV_FILE"
    fi

}

# Check if .env file exists, if not create it
if [ ! -f "$ENV_FILE" ]; then
    touch "$ENV_FILE"
    echo ".env file created."
else
    echo ".env file already exists."
    exit 1
fi

git pull

# Update the .env file
update_env "COMPUTER_ID" ""
printf "\n" >> "$ENV_FILE"
update_env "MAIN_COMPUTER" "false"

echo ".env file has been updated successfully."

# Git commands to push the file to the repository
git add "$FILE"
git commit -m "script: update computer ids file"
git push

echo "computer ids file has been pushed to the Git repository."