import 'package:gp_fbwp_crawler/domain/entity/entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'gp_migrate.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class GPMigrate {
  GPMigrate({
    this.ordinal,
    this.groupName,
    this.firstPostTime, // Thời gian bài đăng đầu tiên
    this.lastPostTime, // Thời gian bài đăng cuối cùng
    this.totalPeople, // Tổng số thành viên
    this.totalPosts, // Tổng số bài viết
    this.totalComments, // Tổng số bình luận
    this.totalReactions, // Tổng số lượt thả cảm xúc
    this.totalMedia, // Tổng số media
    this.totalViews, // Tổng số lượt xem
  });

  final int? ordinal;
  final List<GPGroupOne>? groupName;
  final String? firstPostTime;
  final String? lastPostTime;
  final int? totalPeople;
  final int? totalPosts;
  final int? totalComments;
  final int? totalReactions;
  final int? totalMedia;
  final int? totalViews;

  factory GPMigrate.fromJson(Map<String, dynamic> json) =>
      _$GPMigrateFromJson(json);

  Map<String, dynamic> toJson() => _$GPMigrateToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class GPGroupOne {
  GPGroupOne({
    this.name,
    this.description,
    this.privacy,
    this.discoverability,
    this.wpGroupId,
    this.cover,
    this.createdAt,
    this.previewMembers,
    this.owner,
  });
  final String? wpGroupId;
  final String? name;
  final String? description;
  final String? cover;
  final GPGroupPrivacy? privacy;
  final GPGroupDiscoverability? discoverability;

  final String? createdAt;

  @JsonKey(toJson: _previewMembersToJson)
  final List<GPUser>? previewMembers;

  @JsonKey(toJson: _ownerToJson)
  final GPUser? owner;

  factory GPGroupOne.fromJson(Map<String, dynamic> json) =>
      _$GPGroupOneFromJson(json);

  Map<String, dynamic> toJson() => _$GPGroupOneToJson(this);

  static List<int?>? _previewMembersToJson(List<GPUser>? input) {
    if (input == null) return null;

    return input.map((e) => e.id).toList();
  }

  static int? _ownerToJson(GPUser? owner) {
    return owner?.id;
  }
}




class GroupSummary {
  DateTime firstPostTime;    // Thời gian bài đăng đầu tiên
  DateTime lastPostTime;     // Thời gian bài đăng cuối cùng
  Set<String> totalPeople;   // Danh sách người tham gia duy nhất (Set để tránh trùng)
  int totalPosts;            // Tổng số bài viết
  int totalComments;         // Tổng số bình luận
  int totalReactions;        // Tổng số lượt cảm xúc
  int totalMedia;            // Tổng số media
  int totalViews;            // Tổng số lượt xem

  GroupSummary({
    required this.firstPostTime,
    required this.lastPostTime,
    required this.totalPeople,
    required this.totalPosts,
    required this.totalComments,
    required this.totalReactions,
    required this.totalMedia,
    required this.totalViews,
  });
}
