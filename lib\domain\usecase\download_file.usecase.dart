/*
 * Created Date: Sunday, 11th August 2024, 14:12:26
 * Author: To<PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 12th September 2024 08:58:44
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_core_v2/base/base.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/repository/repository.dart';
import 'package:injectable/injectable.dart';
import 'package:path/path.dart';

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class DownloadFileUseCase
    extends GPBaseFutureUseCase<DownloadFileInput, DownloadFileOutput> {
  const DownloadFileUseCase(
    @Named('kDownloadRepository') this._downloadRepository,
  );

  final DownloadRepository _downloadRepository;

  @override
  Future<DownloadFileOutput> buildUseCase(
    DownloadFileInput input,
  ) async {
    final downloadResult = await _downloadRepository.downloadFile(
      input.params,
    );

    return DownloadFileOutput(
      isFileDownloaded: downloadResult,
      localFilePath: basename(input.params.savedFilePath),
    );
  }
}

class DownloadFileInput extends GPBaseInput {
  const DownloadFileInput({
    required this.params,
  });

  final GPDownloadParams params;
}

class DownloadFileOutput extends GPBaseOutput {
  DownloadFileOutput({
    required this.isFileDownloaded,
    required this.localFilePath,
  });

  final bool isFileDownloaded;
  String localFilePath;
}
