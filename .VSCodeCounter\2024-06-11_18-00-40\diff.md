# Diff Summary

Date : 2024-06-11 18:00:40

Directory /Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler

Total : 93 files,  5342 codes, 207 comments, 545 blanks, all 6094 lines

[Summary](results.md) / [Details](details.md) / Diff Summary / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| Dart | 92 | 5,335 | 207 | 545 | 6,087 |
| YAML | 1 | 7 | 0 | 0 | 7 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 93 | 5,342 | 207 | 545 | 6,094 |
| . (Files) | 1 | 7 | 0 | 0 | 7 |
| lib | 92 | 5,335 | 207 | 545 | 6,087 |
| lib/app | 10 | 327 | 47 | 95 | 469 |
| lib/app (Files) | 2 | 111 | 0 | 37 | 148 |
| lib/app/base | 4 | 149 | 39 | 45 | 233 |
| lib/app/base/networking | 4 | 149 | 39 | 45 | 233 |
| lib/app/base/networking (Files) | 1 | 1 | 0 | 0 | 1 |
| lib/app/base/networking/gapo | 3 | 148 | 39 | 45 | 232 |
| lib/app/constant | 2 | 8 | 2 | 1 | 11 |
| lib/app/features | 2 | 59 | 6 | 12 | 77 |
| lib/app/features/crawler | 2 | 59 | 6 | 12 | 77 |
| lib/app/features/crawler (Files) | 1 | 4 | 0 | 2 | 6 |
| lib/app/features/crawler/bloc | 1 | 55 | 6 | 10 | 71 |
| lib/data | 45 | 129 | 66 | 49 | 244 |
| lib/data/data_source | 9 | 312 | 21 | 31 | 364 |
| lib/data/data_source/remote | 9 | 312 | 21 | 31 | 364 |
| lib/data/data_source/remote (Files) | 4 | -18 | -14 | -14 | -46 |
| lib/data/data_source/remote/gapo | 5 | 330 | 35 | 45 | 410 |
| lib/data/model | 33 | -247 | 35 | 7 | -205 |
| lib/data/model/gpw | 13 | 337 | 46 | 64 | 447 |
| lib/data/model/gpw (Files) | 3 | 16 | 0 | 4 | 20 |
| lib/data/model/gpw/auth | 10 | 321 | 46 | 60 | 427 |
| lib/data/model/gpw/auth (Files) | 1 | 2 | 0 | 1 | 3 |
| lib/data/model/gpw/auth/request | 6 | 285 | 33 | 45 | 363 |
| lib/data/model/gpw/auth/response | 3 | 34 | 13 | 14 | 61 |
| lib/data/model/workplace | 20 | -584 | -11 | -57 | -652 |
| lib/data/model/workplace/base | 1 | 2 | 0 | 0 | 2 |
| lib/data/model/workplace/community | 3 | -46 | -7 | -14 | -67 |
| lib/data/model/workplace/conversation | 5 | 154 | 8 | 26 | 188 |
| lib/data/model/workplace/enums | 1 | 2 | 0 | 0 | 2 |
| lib/data/model/workplace/group | 9 | -697 | -12 | -69 | -778 |
| lib/data/model/workplace/post | 1 | 1 | 0 | 0 | 1 |
| lib/data/repository | 3 | 64 | 10 | 11 | 85 |
| lib/di | 4 | 83 | 3 | 6 | 92 |
| lib/di/component | 1 | 59 | 0 | 0 | 59 |
| lib/di/modules | 3 | 24 | 3 | 6 | 33 |
| lib/domain | 29 | 4,432 | 55 | 367 | 4,854 |
| lib/domain/entity | 18 | 4,279 | 25 | 327 | 4,631 |
| lib/domain/entity/base | 1 | 2 | 0 | 0 | 2 |
| lib/domain/entity/base/log | 1 | 2 | 0 | 0 | 2 |
| lib/domain/entity/enums | 1 | 1 | 1 | 1 | 3 |
| lib/domain/entity/gapo | 4 | -229 | -20 | -31 | -280 |
| lib/domain/entity/workplace | 12 | 4,505 | 44 | 357 | 4,906 |
| lib/domain/repository | 2 | -1 | -1 | -3 | -5 |
| lib/domain/usecase | 9 | 154 | 31 | 43 | 228 |
| lib/domain/usecase (Files) | 5 | 53 | 2 | 14 | 69 |
| lib/domain/usecase/gapo | 4 | 101 | 29 | 29 | 159 |
| lib/helpers | 2 | 59 | 31 | 11 | 101 |
| lib/mapper | 2 | 305 | 5 | 17 | 327 |
| lib/mapper/entity | 2 | 305 | 5 | 17 | 327 |

[Summary](results.md) / [Details](details.md) / Diff Summary / [Diff Details](diff-details.md)