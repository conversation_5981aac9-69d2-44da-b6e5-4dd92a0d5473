import 'package:gp_fbwp_crawler/data/model/datetime_converter.dart';
import 'package:gp_fbwp_crawler/data/model/workplace/enums/workplace_enums.dart';
import 'package:json_annotation/json_annotation.dart';

part 'workplace_post_attachments_response.g.dart';

@JsonSerializable(createToJson: false, fieldRename: FieldRename.snake)
@DateTimeConverter()
class WorkPlacePostAttachmentsResponse {
  WorkPlacePostAttachmentsResponse({
    this.target,
    this.title,
    this.type,
    this.url,
    this.subAttachments,
    this.media,
  });
  final AttachmentTarget? target;
  final String? title;
  @JsonKey(unknownEnumValue: AttachmentType.unknow)
  final AttachmentType? type;
  final String? url;
  @JsonKey(name: 'subattachments')
  final SubAttachments? subAttachments;
  final AttachmentMedia? media;

  factory WorkPlacePostAttachmentsResponse.fromJson(
          Map<String, dynamic> json) =>
      _$WorkPlacePostAttachmentsResponseFromJson(json);

  bool get hasSubAttachment =>
      subAttachments != null &&
      (subAttachments?.data != null &&
          subAttachments?.data?.isNotEmpty == true);
}

@JsonSerializable(createToJson: false)
class AttachmentTarget {
  AttachmentTarget({this.id, this.url});
  final String? id;
  final String? url;

  factory AttachmentTarget.fromJson(Map<String, dynamic> json) =>
      _$AttachmentTargetFromJson(json);
}

@JsonSerializable(createToJson: false)
class SubAttachments {
  SubAttachments({this.data, this.type});
  final List<WorkPlacePostAttachmentsResponse>? data;
  final AttachmentType? type;

  factory SubAttachments.fromJson(Map<String, dynamic> json) =>
      _$SubAttachmentsFromJson(json);
}

@JsonSerializable(createToJson: false, fieldRename: FieldRename.snake)
class AttachmentMedia {
  AttachmentMedia({this.image, this.source});
  final AttachmentMediaImage? image;
  final String? source;

  factory AttachmentMedia.fromJson(Map<String, dynamic> json) =>
      _$AttachmentMediaFromJson(json);
}

@JsonSerializable(createToJson: false, fieldRename: FieldRename.snake)
class AttachmentMediaImage {
  AttachmentMediaImage({this.height, this.width, this.src});
  int? height;
  int? width;
  String? src;

  factory AttachmentMediaImage.fromJson(Map<String, dynamic> json) =>
      _$AttachmentMediaImageFromJson(json);
}
