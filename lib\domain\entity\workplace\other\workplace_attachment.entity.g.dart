// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workplace_attachment.entity.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetWorkPlaceAttachmentEntityCollection on Isar {
  IsarCollection<WorkPlaceAttachmentEntity> get workPlaceAttachmentEntitys =>
      this.collection();
}

const WorkPlaceAttachmentEntitySchema = CollectionSchema(
  name: r'WorkPlaceAttachmentEntity',
  id: -2993604554845389404,
  properties: {
    r'crawlType': PropertySchema(
      id: 0,
      name: r'crawlType',
      type: IsarType.byte,
      enumMap: _WorkPlaceAttachmentEntitycrawlTypeEnumValueMap,
    ),
    r'gpId': PropertySchema(
      id: 1,
      name: r'gpId',
      type: IsarType.string,
    ),
    r'gpLink': PropertySchema(
      id: 2,
      name: r'gpLink',
      type: IsarType.string,
    ),
    r'id': PropertySchema(
      id: 3,
      name: r'id',
      type: IsarType.string,
    ),
    r'localFilePath': PropertySchema(
      id: 4,
      name: r'localFilePath',
      type: IsarType.string,
    ),
    r'media': PropertySchema(
      id: 5,
      name: r'media',
      type: IsarType.object,
      target: r'AttachmentMediaEntity',
    ),
    r'target': PropertySchema(
      id: 6,
      name: r'target',
      type: IsarType.object,
      target: r'AttachmentTargetEntity',
    ),
    r'title': PropertySchema(
      id: 7,
      name: r'title',
      type: IsarType.string,
    ),
    r'type': PropertySchema(
      id: 8,
      name: r'type',
      type: IsarType.string,
      enumMap: _WorkPlaceAttachmentEntitytypeEnumValueMap,
    ),
    r'uploadResponse': PropertySchema(
      id: 9,
      name: r'uploadResponse',
      type: IsarType.object,
      target: r'UploadResponseEntity',
    ),
    r'url': PropertySchema(
      id: 10,
      name: r'url',
      type: IsarType.string,
    )
  },
  estimateSize: _workPlaceAttachmentEntityEstimateSize,
  serialize: _workPlaceAttachmentEntitySerialize,
  deserialize: _workPlaceAttachmentEntityDeserialize,
  deserializeProp: _workPlaceAttachmentEntityDeserializeProp,
  idName: r'dbId',
  indexes: {},
  links: {},
  embeddedSchemas: {
    r'AttachmentMediaEntity': AttachmentMediaEntitySchema,
    r'AttachmentMediaImageEntity': AttachmentMediaImageEntitySchema,
    r'AttachmentTargetEntity': AttachmentTargetEntitySchema,
    r'UploadResponseEntity': UploadResponseEntitySchema,
    r'UploadFileURLResponseEntity': UploadFileURLResponseEntitySchema
  },
  getId: _workPlaceAttachmentEntityGetId,
  getLinks: _workPlaceAttachmentEntityGetLinks,
  attach: _workPlaceAttachmentEntityAttach,
  version: '3.1.0+1',
);

int _workPlaceAttachmentEntityEstimateSize(
  WorkPlaceAttachmentEntity object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.gpId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.gpLink;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  bytesCount += 3 + object.id.length * 3;
  {
    final value = object.localFilePath;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.media;
    if (value != null) {
      bytesCount += 3 +
          AttachmentMediaEntitySchema.estimateSize(
              value, allOffsets[AttachmentMediaEntity]!, allOffsets);
    }
  }
  {
    final value = object.target;
    if (value != null) {
      bytesCount += 3 +
          AttachmentTargetEntitySchema.estimateSize(
              value, allOffsets[AttachmentTargetEntity]!, allOffsets);
    }
  }
  {
    final value = object.title;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.type;
    if (value != null) {
      bytesCount += 3 + value.name.length * 3;
    }
  }
  {
    final value = object.uploadResponse;
    if (value != null) {
      bytesCount += 3 +
          UploadResponseEntitySchema.estimateSize(
              value, allOffsets[UploadResponseEntity]!, allOffsets);
    }
  }
  {
    final value = object.url;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _workPlaceAttachmentEntitySerialize(
  WorkPlaceAttachmentEntity object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeByte(offsets[0], object.crawlType.index);
  writer.writeString(offsets[1], object.gpId);
  writer.writeString(offsets[2], object.gpLink);
  writer.writeString(offsets[3], object.id);
  writer.writeString(offsets[4], object.localFilePath);
  writer.writeObject<AttachmentMediaEntity>(
    offsets[5],
    allOffsets,
    AttachmentMediaEntitySchema.serialize,
    object.media,
  );
  writer.writeObject<AttachmentTargetEntity>(
    offsets[6],
    allOffsets,
    AttachmentTargetEntitySchema.serialize,
    object.target,
  );
  writer.writeString(offsets[7], object.title);
  writer.writeString(offsets[8], object.type?.name);
  writer.writeObject<UploadResponseEntity>(
    offsets[9],
    allOffsets,
    UploadResponseEntitySchema.serialize,
    object.uploadResponse,
  );
  writer.writeString(offsets[10], object.url);
}

WorkPlaceAttachmentEntity _workPlaceAttachmentEntityDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = WorkPlaceAttachmentEntity(
    crawlType: _WorkPlaceAttachmentEntitycrawlTypeValueEnumMap[
            reader.readByteOrNull(offsets[0])] ??
        GPBaseCrawlType.attachment,
    id: reader.readStringOrNull(offsets[3]) ?? '',
    media: reader.readObjectOrNull<AttachmentMediaEntity>(
      offsets[5],
      AttachmentMediaEntitySchema.deserialize,
      allOffsets,
    ),
    target: reader.readObjectOrNull<AttachmentTargetEntity>(
      offsets[6],
      AttachmentTargetEntitySchema.deserialize,
      allOffsets,
    ),
    title: reader.readStringOrNull(offsets[7]),
    type: _WorkPlaceAttachmentEntitytypeValueEnumMap[
        reader.readStringOrNull(offsets[8])],
    url: reader.readStringOrNull(offsets[10]),
  );
  object.gpId = reader.readStringOrNull(offsets[1]);
  object.gpLink = reader.readStringOrNull(offsets[2]);
  object.localFilePath = reader.readStringOrNull(offsets[4]);
  object.uploadResponse = reader.readObjectOrNull<UploadResponseEntity>(
    offsets[9],
    UploadResponseEntitySchema.deserialize,
    allOffsets,
  );
  return object;
}

P _workPlaceAttachmentEntityDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (_WorkPlaceAttachmentEntitycrawlTypeValueEnumMap[
              reader.readByteOrNull(offset)] ??
          GPBaseCrawlType.attachment) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    case 3:
      return (reader.readStringOrNull(offset) ?? '') as P;
    case 4:
      return (reader.readStringOrNull(offset)) as P;
    case 5:
      return (reader.readObjectOrNull<AttachmentMediaEntity>(
        offset,
        AttachmentMediaEntitySchema.deserialize,
        allOffsets,
      )) as P;
    case 6:
      return (reader.readObjectOrNull<AttachmentTargetEntity>(
        offset,
        AttachmentTargetEntitySchema.deserialize,
        allOffsets,
      )) as P;
    case 7:
      return (reader.readStringOrNull(offset)) as P;
    case 8:
      return (_WorkPlaceAttachmentEntitytypeValueEnumMap[
          reader.readStringOrNull(offset)]) as P;
    case 9:
      return (reader.readObjectOrNull<UploadResponseEntity>(
        offset,
        UploadResponseEntitySchema.deserialize,
        allOffsets,
      )) as P;
    case 10:
      return (reader.readStringOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _WorkPlaceAttachmentEntitycrawlTypeEnumValueMap = {
  'user': 0,
  'group': 1,
  'thread': 2,
  'message': 3,
  'feed': 4,
  'userFeed': 5,
  'comment': 6,
  'attachment': 7,
  'community': 8,
  'sticker': 9,
  'reaction': 10,
  'commentReaction': 11,
  'groupMember': 12,
  'postSeen': 13,
  'messageAttachment': 14,
};
const _WorkPlaceAttachmentEntitycrawlTypeValueEnumMap = {
  0: GPBaseCrawlType.user,
  1: GPBaseCrawlType.group,
  2: GPBaseCrawlType.thread,
  3: GPBaseCrawlType.message,
  4: GPBaseCrawlType.feed,
  5: GPBaseCrawlType.userFeed,
  6: GPBaseCrawlType.comment,
  7: GPBaseCrawlType.attachment,
  8: GPBaseCrawlType.community,
  9: GPBaseCrawlType.sticker,
  10: GPBaseCrawlType.reaction,
  11: GPBaseCrawlType.commentReaction,
  12: GPBaseCrawlType.groupMember,
  13: GPBaseCrawlType.postSeen,
  14: GPBaseCrawlType.messageAttachment,
};
const _WorkPlaceAttachmentEntitytypeEnumValueMap = {
  r'album': r'album',
  r'fileUpload': r'fileUpload',
  r'photo': r'photo',
  r'video': r'video',
  r'workContentAttachment': r'workContentAttachment',
  r'nativeTemplates': r'nativeTemplates',
  r'question': r'question',
  r'option': r'option',
  r'sticker': r'sticker',
  r'animateImageShare': r'animateImageShare',
  r'animateImageVideo': r'animateImageVideo',
  r'videoOnline': r'videoOnline',
  r'knowledgeNote': r'knowledgeNote',
  r'share': r'share',
  r'coverPhoto': r'coverPhoto',
  r'event': r'event',
  r'unknow': r'unknow',
};
const _WorkPlaceAttachmentEntitytypeValueEnumMap = {
  r'album': AttachmentType.album,
  r'fileUpload': AttachmentType.fileUpload,
  r'photo': AttachmentType.photo,
  r'video': AttachmentType.video,
  r'workContentAttachment': AttachmentType.workContentAttachment,
  r'nativeTemplates': AttachmentType.nativeTemplates,
  r'question': AttachmentType.question,
  r'option': AttachmentType.option,
  r'sticker': AttachmentType.sticker,
  r'animateImageShare': AttachmentType.animateImageShare,
  r'animateImageVideo': AttachmentType.animateImageVideo,
  r'videoOnline': AttachmentType.videoOnline,
  r'knowledgeNote': AttachmentType.knowledgeNote,
  r'share': AttachmentType.share,
  r'coverPhoto': AttachmentType.coverPhoto,
  r'event': AttachmentType.event,
  r'unknow': AttachmentType.unknow,
};

Id _workPlaceAttachmentEntityGetId(WorkPlaceAttachmentEntity object) {
  return object.dbId ?? Isar.autoIncrement;
}

List<IsarLinkBase<dynamic>> _workPlaceAttachmentEntityGetLinks(
    WorkPlaceAttachmentEntity object) {
  return [];
}

void _workPlaceAttachmentEntityAttach(
    IsarCollection<dynamic> col, Id id, WorkPlaceAttachmentEntity object) {}

extension WorkPlaceAttachmentEntityQueryWhereSort on QueryBuilder<
    WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity, QWhere> {
  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterWhere> anyDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension WorkPlaceAttachmentEntityQueryWhere on QueryBuilder<
    WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity, QWhereClause> {
  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterWhereClause> dbIdEqualTo(Id dbId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: dbId,
        upper: dbId,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterWhereClause> dbIdNotEqualTo(Id dbId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: dbId, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: dbId, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: dbId, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: dbId, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterWhereClause> dbIdGreaterThan(Id dbId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: dbId, includeLower: include),
      );
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterWhereClause> dbIdLessThan(Id dbId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: dbId, includeUpper: include),
      );
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterWhereClause> dbIdBetween(
    Id lowerDbId,
    Id upperDbId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerDbId,
        includeLower: includeLower,
        upper: upperDbId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension WorkPlaceAttachmentEntityQueryFilter on QueryBuilder<
    WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity, QFilterCondition> {
  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> crawlTypeEqualTo(GPBaseCrawlType value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> crawlTypeGreaterThan(
    GPBaseCrawlType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> crawlTypeLessThan(
    GPBaseCrawlType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> crawlTypeBetween(
    GPBaseCrawlType lower,
    GPBaseCrawlType upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'crawlType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> dbIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'dbId',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> dbIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'dbId',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> dbIdEqualTo(Id? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> dbIdGreaterThan(
    Id? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> dbIdLessThan(
    Id? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> dbIdBetween(
    Id? lower,
    Id? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'dbId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> gpIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'gpId',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> gpIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'gpId',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> gpIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'gpId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> gpIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'gpId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> gpIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'gpId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> gpIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'gpId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> gpIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'gpId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> gpIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'gpId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
          QAfterFilterCondition>
      gpIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'gpId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
          QAfterFilterCondition>
      gpIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'gpId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> gpIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'gpId',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> gpIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'gpId',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> gpLinkIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'gpLink',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> gpLinkIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'gpLink',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> gpLinkEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'gpLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> gpLinkGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'gpLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> gpLinkLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'gpLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> gpLinkBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'gpLink',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> gpLinkStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'gpLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> gpLinkEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'gpLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
          QAfterFilterCondition>
      gpLinkContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'gpLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
          QAfterFilterCondition>
      gpLinkMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'gpLink',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> gpLinkIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'gpLink',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> gpLinkIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'gpLink',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> idEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> idGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> idLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> idBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> idStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> idEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
          QAfterFilterCondition>
      idContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
          QAfterFilterCondition>
      idMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'id',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> idIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> idIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> localFilePathIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'localFilePath',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> localFilePathIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'localFilePath',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> localFilePathEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localFilePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> localFilePathGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'localFilePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> localFilePathLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'localFilePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> localFilePathBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'localFilePath',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> localFilePathStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'localFilePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> localFilePathEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'localFilePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
          QAfterFilterCondition>
      localFilePathContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'localFilePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
          QAfterFilterCondition>
      localFilePathMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'localFilePath',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> localFilePathIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localFilePath',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> localFilePathIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'localFilePath',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> mediaIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'media',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> mediaIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'media',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> targetIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'target',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> targetIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'target',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> titleIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'title',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> titleIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'title',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> titleEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> titleGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> titleLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> titleBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'title',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> titleStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> titleEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
          QAfterFilterCondition>
      titleContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
          QAfterFilterCondition>
      titleMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'title',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> titleIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'title',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> titleIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'title',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> typeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'type',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> typeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'type',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> typeEqualTo(
    AttachmentType? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'type',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> typeGreaterThan(
    AttachmentType? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'type',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> typeLessThan(
    AttachmentType? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'type',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> typeBetween(
    AttachmentType? lower,
    AttachmentType? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'type',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> typeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'type',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> typeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'type',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
          QAfterFilterCondition>
      typeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'type',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
          QAfterFilterCondition>
      typeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'type',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> typeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'type',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> typeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'type',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> uploadResponseIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'uploadResponse',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> uploadResponseIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'uploadResponse',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> urlIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'url',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> urlIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'url',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> urlEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> urlGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> urlLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> urlBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'url',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> urlStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> urlEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
          QAfterFilterCondition>
      urlContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
          QAfterFilterCondition>
      urlMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'url',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> urlIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'url',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> urlIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'url',
        value: '',
      ));
    });
  }
}

extension WorkPlaceAttachmentEntityQueryObject on QueryBuilder<
    WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity, QFilterCondition> {
  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> media(FilterQuery<AttachmentMediaEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'media');
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterFilterCondition> target(FilterQuery<AttachmentTargetEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'target');
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
          QAfterFilterCondition>
      uploadResponse(FilterQuery<UploadResponseEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'uploadResponse');
    });
  }
}

extension WorkPlaceAttachmentEntityQueryLinks on QueryBuilder<
    WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity, QFilterCondition> {}

extension WorkPlaceAttachmentEntityQuerySortBy on QueryBuilder<
    WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity, QSortBy> {
  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> sortByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> sortByCrawlTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> sortByGpId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpId', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> sortByGpIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpId', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> sortByGpLink() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpLink', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> sortByGpLinkDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpLink', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> sortById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> sortByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> sortByLocalFilePath() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localFilePath', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> sortByLocalFilePathDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localFilePath', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> sortByTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> sortByTitleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> sortByType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'type', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> sortByTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'type', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> sortByUrl() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'url', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> sortByUrlDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'url', Sort.desc);
    });
  }
}

extension WorkPlaceAttachmentEntityQuerySortThenBy on QueryBuilder<
    WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity, QSortThenBy> {
  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> thenByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> thenByCrawlTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> thenByDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dbId', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> thenByDbIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dbId', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> thenByGpId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpId', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> thenByGpIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpId', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> thenByGpLink() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpLink', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> thenByGpLinkDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpLink', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> thenByLocalFilePath() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localFilePath', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> thenByLocalFilePathDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localFilePath', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> thenByTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> thenByTitleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> thenByType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'type', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> thenByTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'type', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> thenByUrl() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'url', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity,
      QAfterSortBy> thenByUrlDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'url', Sort.desc);
    });
  }
}

extension WorkPlaceAttachmentEntityQueryWhereDistinct on QueryBuilder<
    WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity, QDistinct> {
  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity, QDistinct>
      distinctByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'crawlType');
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity, QDistinct>
      distinctByGpId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'gpId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity, QDistinct>
      distinctByGpLink({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'gpLink', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity, QDistinct>
      distinctById({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'id', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity, QDistinct>
      distinctByLocalFilePath({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'localFilePath',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity, QDistinct>
      distinctByTitle({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'title', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity, QDistinct>
      distinctByType({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'type', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity, QDistinct>
      distinctByUrl({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'url', caseSensitive: caseSensitive);
    });
  }
}

extension WorkPlaceAttachmentEntityQueryProperty on QueryBuilder<
    WorkPlaceAttachmentEntity, WorkPlaceAttachmentEntity, QQueryProperty> {
  QueryBuilder<WorkPlaceAttachmentEntity, int, QQueryOperations>
      dbIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'dbId');
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, GPBaseCrawlType, QQueryOperations>
      crawlTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'crawlType');
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, String?, QQueryOperations>
      gpIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'gpId');
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, String?, QQueryOperations>
      gpLinkProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'gpLink');
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, String, QQueryOperations>
      idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, String?, QQueryOperations>
      localFilePathProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'localFilePath');
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, AttachmentMediaEntity?,
      QQueryOperations> mediaProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'media');
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, AttachmentTargetEntity?,
      QQueryOperations> targetProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'target');
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, String?, QQueryOperations>
      titleProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'title');
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, AttachmentType?, QQueryOperations>
      typeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'type');
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, UploadResponseEntity?,
      QQueryOperations> uploadResponseProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'uploadResponse');
    });
  }

  QueryBuilder<WorkPlaceAttachmentEntity, String?, QQueryOperations>
      urlProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'url');
    });
  }
}

// **************************************************************************
// IsarEmbeddedGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

const AttachmentTargetEntitySchema = Schema(
  name: r'AttachmentTargetEntity',
  id: -9148893078992791121,
  properties: {
    r'id': PropertySchema(
      id: 0,
      name: r'id',
      type: IsarType.string,
    ),
    r'url': PropertySchema(
      id: 1,
      name: r'url',
      type: IsarType.string,
    )
  },
  estimateSize: _attachmentTargetEntityEstimateSize,
  serialize: _attachmentTargetEntitySerialize,
  deserialize: _attachmentTargetEntityDeserialize,
  deserializeProp: _attachmentTargetEntityDeserializeProp,
);

int _attachmentTargetEntityEstimateSize(
  AttachmentTargetEntity object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.id;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.url;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _attachmentTargetEntitySerialize(
  AttachmentTargetEntity object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.id);
  writer.writeString(offsets[1], object.url);
}

AttachmentTargetEntity _attachmentTargetEntityDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = AttachmentTargetEntity(
    id: reader.readStringOrNull(offsets[0]),
    url: reader.readStringOrNull(offsets[1]),
  );
  return object;
}

P _attachmentTargetEntityDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

extension AttachmentTargetEntityQueryFilter on QueryBuilder<
    AttachmentTargetEntity, AttachmentTargetEntity, QFilterCondition> {
  QueryBuilder<AttachmentTargetEntity, AttachmentTargetEntity,
      QAfterFilterCondition> idIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'id',
      ));
    });
  }

  QueryBuilder<AttachmentTargetEntity, AttachmentTargetEntity,
      QAfterFilterCondition> idIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'id',
      ));
    });
  }

  QueryBuilder<AttachmentTargetEntity, AttachmentTargetEntity,
      QAfterFilterCondition> idEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentTargetEntity, AttachmentTargetEntity,
      QAfterFilterCondition> idGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentTargetEntity, AttachmentTargetEntity,
      QAfterFilterCondition> idLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentTargetEntity, AttachmentTargetEntity,
      QAfterFilterCondition> idBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentTargetEntity, AttachmentTargetEntity,
      QAfterFilterCondition> idStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentTargetEntity, AttachmentTargetEntity,
      QAfterFilterCondition> idEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentTargetEntity, AttachmentTargetEntity,
          QAfterFilterCondition>
      idContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentTargetEntity, AttachmentTargetEntity,
          QAfterFilterCondition>
      idMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'id',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentTargetEntity, AttachmentTargetEntity,
      QAfterFilterCondition> idIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<AttachmentTargetEntity, AttachmentTargetEntity,
      QAfterFilterCondition> idIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<AttachmentTargetEntity, AttachmentTargetEntity,
      QAfterFilterCondition> urlIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'url',
      ));
    });
  }

  QueryBuilder<AttachmentTargetEntity, AttachmentTargetEntity,
      QAfterFilterCondition> urlIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'url',
      ));
    });
  }

  QueryBuilder<AttachmentTargetEntity, AttachmentTargetEntity,
      QAfterFilterCondition> urlEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentTargetEntity, AttachmentTargetEntity,
      QAfterFilterCondition> urlGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentTargetEntity, AttachmentTargetEntity,
      QAfterFilterCondition> urlLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentTargetEntity, AttachmentTargetEntity,
      QAfterFilterCondition> urlBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'url',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentTargetEntity, AttachmentTargetEntity,
      QAfterFilterCondition> urlStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentTargetEntity, AttachmentTargetEntity,
      QAfterFilterCondition> urlEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentTargetEntity, AttachmentTargetEntity,
          QAfterFilterCondition>
      urlContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentTargetEntity, AttachmentTargetEntity,
          QAfterFilterCondition>
      urlMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'url',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentTargetEntity, AttachmentTargetEntity,
      QAfterFilterCondition> urlIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'url',
        value: '',
      ));
    });
  }

  QueryBuilder<AttachmentTargetEntity, AttachmentTargetEntity,
      QAfterFilterCondition> urlIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'url',
        value: '',
      ));
    });
  }
}

extension AttachmentTargetEntityQueryObject on QueryBuilder<
    AttachmentTargetEntity, AttachmentTargetEntity, QFilterCondition> {}

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

const AttachmentMediaEntitySchema = Schema(
  name: r'AttachmentMediaEntity',
  id: 7855658338007582189,
  properties: {
    r'image': PropertySchema(
      id: 0,
      name: r'image',
      type: IsarType.object,
      target: r'AttachmentMediaImageEntity',
    ),
    r'source': PropertySchema(
      id: 1,
      name: r'source',
      type: IsarType.string,
    )
  },
  estimateSize: _attachmentMediaEntityEstimateSize,
  serialize: _attachmentMediaEntitySerialize,
  deserialize: _attachmentMediaEntityDeserialize,
  deserializeProp: _attachmentMediaEntityDeserializeProp,
);

int _attachmentMediaEntityEstimateSize(
  AttachmentMediaEntity object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.image;
    if (value != null) {
      bytesCount += 3 +
          AttachmentMediaImageEntitySchema.estimateSize(
              value, allOffsets[AttachmentMediaImageEntity]!, allOffsets);
    }
  }
  {
    final value = object.source;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _attachmentMediaEntitySerialize(
  AttachmentMediaEntity object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeObject<AttachmentMediaImageEntity>(
    offsets[0],
    allOffsets,
    AttachmentMediaImageEntitySchema.serialize,
    object.image,
  );
  writer.writeString(offsets[1], object.source);
}

AttachmentMediaEntity _attachmentMediaEntityDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = AttachmentMediaEntity(
    image: reader.readObjectOrNull<AttachmentMediaImageEntity>(
      offsets[0],
      AttachmentMediaImageEntitySchema.deserialize,
      allOffsets,
    ),
    source: reader.readStringOrNull(offsets[1]),
  );
  return object;
}

P _attachmentMediaEntityDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readObjectOrNull<AttachmentMediaImageEntity>(
        offset,
        AttachmentMediaImageEntitySchema.deserialize,
        allOffsets,
      )) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

extension AttachmentMediaEntityQueryFilter on QueryBuilder<
    AttachmentMediaEntity, AttachmentMediaEntity, QFilterCondition> {
  QueryBuilder<AttachmentMediaEntity, AttachmentMediaEntity,
      QAfterFilterCondition> imageIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'image',
      ));
    });
  }

  QueryBuilder<AttachmentMediaEntity, AttachmentMediaEntity,
      QAfterFilterCondition> imageIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'image',
      ));
    });
  }

  QueryBuilder<AttachmentMediaEntity, AttachmentMediaEntity,
      QAfterFilterCondition> sourceIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'source',
      ));
    });
  }

  QueryBuilder<AttachmentMediaEntity, AttachmentMediaEntity,
      QAfterFilterCondition> sourceIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'source',
      ));
    });
  }

  QueryBuilder<AttachmentMediaEntity, AttachmentMediaEntity,
      QAfterFilterCondition> sourceEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'source',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentMediaEntity, AttachmentMediaEntity,
      QAfterFilterCondition> sourceGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'source',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentMediaEntity, AttachmentMediaEntity,
      QAfterFilterCondition> sourceLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'source',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentMediaEntity, AttachmentMediaEntity,
      QAfterFilterCondition> sourceBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'source',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentMediaEntity, AttachmentMediaEntity,
      QAfterFilterCondition> sourceStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'source',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentMediaEntity, AttachmentMediaEntity,
      QAfterFilterCondition> sourceEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'source',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentMediaEntity, AttachmentMediaEntity,
          QAfterFilterCondition>
      sourceContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'source',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentMediaEntity, AttachmentMediaEntity,
          QAfterFilterCondition>
      sourceMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'source',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentMediaEntity, AttachmentMediaEntity,
      QAfterFilterCondition> sourceIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'source',
        value: '',
      ));
    });
  }

  QueryBuilder<AttachmentMediaEntity, AttachmentMediaEntity,
      QAfterFilterCondition> sourceIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'source',
        value: '',
      ));
    });
  }
}

extension AttachmentMediaEntityQueryObject on QueryBuilder<
    AttachmentMediaEntity, AttachmentMediaEntity, QFilterCondition> {
  QueryBuilder<AttachmentMediaEntity, AttachmentMediaEntity,
      QAfterFilterCondition> image(FilterQuery<AttachmentMediaImageEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'image');
    });
  }
}

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

const AttachmentMediaImageEntitySchema = Schema(
  name: r'AttachmentMediaImageEntity',
  id: -1269339234151531031,
  properties: {
    r'height': PropertySchema(
      id: 0,
      name: r'height',
      type: IsarType.long,
    ),
    r'src': PropertySchema(
      id: 1,
      name: r'src',
      type: IsarType.string,
    ),
    r'width': PropertySchema(
      id: 2,
      name: r'width',
      type: IsarType.long,
    )
  },
  estimateSize: _attachmentMediaImageEntityEstimateSize,
  serialize: _attachmentMediaImageEntitySerialize,
  deserialize: _attachmentMediaImageEntityDeserialize,
  deserializeProp: _attachmentMediaImageEntityDeserializeProp,
);

int _attachmentMediaImageEntityEstimateSize(
  AttachmentMediaImageEntity object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.src;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _attachmentMediaImageEntitySerialize(
  AttachmentMediaImageEntity object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeLong(offsets[0], object.height);
  writer.writeString(offsets[1], object.src);
  writer.writeLong(offsets[2], object.width);
}

AttachmentMediaImageEntity _attachmentMediaImageEntityDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = AttachmentMediaImageEntity(
    height: reader.readLongOrNull(offsets[0]),
    src: reader.readStringOrNull(offsets[1]),
    width: reader.readLongOrNull(offsets[2]),
  );
  return object;
}

P _attachmentMediaImageEntityDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readLongOrNull(offset)) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    case 2:
      return (reader.readLongOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

extension AttachmentMediaImageEntityQueryFilter on QueryBuilder<
    AttachmentMediaImageEntity, AttachmentMediaImageEntity, QFilterCondition> {
  QueryBuilder<AttachmentMediaImageEntity, AttachmentMediaImageEntity,
      QAfterFilterCondition> heightIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'height',
      ));
    });
  }

  QueryBuilder<AttachmentMediaImageEntity, AttachmentMediaImageEntity,
      QAfterFilterCondition> heightIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'height',
      ));
    });
  }

  QueryBuilder<AttachmentMediaImageEntity, AttachmentMediaImageEntity,
      QAfterFilterCondition> heightEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'height',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentMediaImageEntity, AttachmentMediaImageEntity,
      QAfterFilterCondition> heightGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'height',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentMediaImageEntity, AttachmentMediaImageEntity,
      QAfterFilterCondition> heightLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'height',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentMediaImageEntity, AttachmentMediaImageEntity,
      QAfterFilterCondition> heightBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'height',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AttachmentMediaImageEntity, AttachmentMediaImageEntity,
      QAfterFilterCondition> srcIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'src',
      ));
    });
  }

  QueryBuilder<AttachmentMediaImageEntity, AttachmentMediaImageEntity,
      QAfterFilterCondition> srcIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'src',
      ));
    });
  }

  QueryBuilder<AttachmentMediaImageEntity, AttachmentMediaImageEntity,
      QAfterFilterCondition> srcEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'src',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentMediaImageEntity, AttachmentMediaImageEntity,
      QAfterFilterCondition> srcGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'src',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentMediaImageEntity, AttachmentMediaImageEntity,
      QAfterFilterCondition> srcLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'src',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentMediaImageEntity, AttachmentMediaImageEntity,
      QAfterFilterCondition> srcBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'src',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentMediaImageEntity, AttachmentMediaImageEntity,
      QAfterFilterCondition> srcStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'src',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentMediaImageEntity, AttachmentMediaImageEntity,
      QAfterFilterCondition> srcEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'src',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentMediaImageEntity, AttachmentMediaImageEntity,
          QAfterFilterCondition>
      srcContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'src',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentMediaImageEntity, AttachmentMediaImageEntity,
          QAfterFilterCondition>
      srcMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'src',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AttachmentMediaImageEntity, AttachmentMediaImageEntity,
      QAfterFilterCondition> srcIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'src',
        value: '',
      ));
    });
  }

  QueryBuilder<AttachmentMediaImageEntity, AttachmentMediaImageEntity,
      QAfterFilterCondition> srcIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'src',
        value: '',
      ));
    });
  }

  QueryBuilder<AttachmentMediaImageEntity, AttachmentMediaImageEntity,
      QAfterFilterCondition> widthIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'width',
      ));
    });
  }

  QueryBuilder<AttachmentMediaImageEntity, AttachmentMediaImageEntity,
      QAfterFilterCondition> widthIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'width',
      ));
    });
  }

  QueryBuilder<AttachmentMediaImageEntity, AttachmentMediaImageEntity,
      QAfterFilterCondition> widthEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'width',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentMediaImageEntity, AttachmentMediaImageEntity,
      QAfterFilterCondition> widthGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'width',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentMediaImageEntity, AttachmentMediaImageEntity,
      QAfterFilterCondition> widthLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'width',
        value: value,
      ));
    });
  }

  QueryBuilder<AttachmentMediaImageEntity, AttachmentMediaImageEntity,
      QAfterFilterCondition> widthBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'width',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension AttachmentMediaImageEntityQueryObject on QueryBuilder<
    AttachmentMediaImageEntity, AttachmentMediaImageEntity, QFilterCondition> {}
