/*
 * Created Date: Tuesday, 4th June 2024, 09:12:37
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 12th September 2024 00:03:54
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_core/core.dart';
import 'package:gp_core_v2/base/base.dart';
import 'package:gp_dio_log/gp_dio_log.dart';
import 'package:gp_fbwp_crawler/app/base/networking/networking.dart';
import 'package:injectable/injectable.dart';

@module
abstract class ClientModule {
  final loggerInterceptor = LoggerInterceptor();
  final requestCounterInterceptor = RequestCounterInterceptor();
  final gpDioLogInterceptor = GPDioLogInterceptor();
  final errorLoggerInterceptor = ErrorLoggerInterceptor();

  @Singleton(order: DiConstants.kDataDioOrder)
  @Named('kWorkPlaceDio')
  Dio createWorkPlaceDio() {
    final dio = Dio(BaseOptions(
      connectTimeout: const Duration(seconds: 60),
      receiveTimeout: const Duration(seconds: 300),
      sendTimeout: const Duration(seconds: 300),
    ));

    dio.interceptors.addAll([
      const WorkPlaceAuthenticationInterceptor(),
      loggerInterceptor,
      requestCounterInterceptor,
      gpDioLogInterceptor,
      errorLoggerInterceptor,
    ]);

    return dio;
  }

  @Singleton(order: DiConstants.kDataDioOrder)
  @Named('kWorkPlaceDownloadDio')
  Dio createWorkPlaceDownloadDio() {
    final dio = Dio(BaseOptions(
      connectTimeout: const Duration(seconds: 360),
      receiveTimeout: const Duration(seconds: 86400),
      sendTimeout: const Duration(seconds: 86400),
    ));

    dio.interceptors.addAll(_gpInterceptors(dio));

    return dio;
  }

  @Singleton(order: DiConstants.kDataDioOrder)
  @Named('kGapoWorkDio')
  Dio createDio() {
    final dio = Dio(BaseOptions(
      connectTimeout: const Duration(seconds: 60),
      receiveTimeout: const Duration(seconds: 300),
      sendTimeout: const Duration(seconds: 300),
    ));

    dio.interceptors.addAll(_gpInterceptors(dio));

    return dio;
  }

  @Singleton(order: DiConstants.kDataDioOrder)
  @Named('kGapoWorkUserDio')
  Dio createGPUserDio() {
    final dio = Dio(BaseOptions(
      connectTimeout: const Duration(seconds: 60),
      receiveTimeout: const Duration(seconds: 300),
      sendTimeout: const Duration(seconds: 300),
    ));

    dio.interceptors.addAll([
      loggerInterceptor,
      requestCounterInterceptor,
      errorLoggerInterceptor,
    ]);

    return dio;
  }

  @Singleton(order: DiConstants.kDataDioOrder)
  @Named('kGapoWorkUploadDio')
  Dio createGPUploadDio() {
    final dio = Dio(BaseOptions(
      connectTimeout: const Duration(seconds: 120),
      receiveTimeout: const Duration(seconds: 86400),
      sendTimeout: const Duration(seconds: 86400),
    ));

    dio.interceptors.addAll(_gpInterceptors(dio));

    return dio;
  }

  Iterable<Interceptor> _gpInterceptors(Dio dio) => [
        AuthenticationInterceptor(),
        TokenInterceptor(dio),
        loggerInterceptor,
        QueuedInterceptor(),
        requestCounterInterceptor,
        gpDioLogInterceptor,
        errorLoggerInterceptor,
      ];
}
