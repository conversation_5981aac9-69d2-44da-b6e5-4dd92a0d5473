/*
 * Created Date: Friday, 21st June 2024, 09:59:32
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Sunday, 8th September 2024 23:57:20
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:auto_mappr_annotation/auto_mappr_annotation.dart';
import 'package:gp_fbwp_crawler/app/constant/constant.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:gp_fbwp_crawler/domain/entity/entity.dart';
import 'package:gp_fbwp_crawler/mapper/mapper.dart';
import 'package:intl/intl.dart';
import 'package:gp_core/core.dart' hide GPUser;
import 'package:strings/strings.dart';

/// {@macro package:gp_fbwp_crawler/mapper/entity/gapo_entity_mapper.dart}
@AutoMappr(
  [
    MapType<WorkPlaceCommunityMemberEntity, GPUser>(fields: [
      Field('id', from: 'gpUserId'),
    ]),
    MapType<WorkPlaceGroupEntity, GPGroup>(fields: [
      Field('wpGroupId', from: 'id'),
      Field('cover', custom: GapoEntityMapper.mapToGPGroupCover),
      Field('privacy', custom: GapoEntityMapper.mapToGPGroupPrivacy),
      Field('discoverability', custom: GPGroupDiscoverability.visible),
      Field('createdAt', custom: GapoEntityMapper.mapToGPGroupCreateAt),
      Field('previewMembers',
          custom: GapoEntityMapper.mapToGPGroupPreviewMembers),
      Field('owner', custom: GapoEntityMapper.mapToGPGroupOwner),
    ]),
    // Post
    MapType<WorkPlaceFeedEntity, GPPost>(fields: [
      // Field('wpGroupId', from: 'id'),
      Field('userId', custom: GapoEntityMapper.mapToPostUserId),
      Field('content', from: 'message'),
      Field('media', custom: GapoEntityMapper.mapToPostMedia),
      Field('comments', custom: GapoEntityMapper.mapToPostComments),
      // Field('mention', custom: GapoEntityMapper.mapToPostMention),
      Field('privacy', custom: GapoEntityMapper.mapToPostPrivacy),
      Field('target', custom: GapoEntityMapper.mapToPostTarget),
      Field('createdAt', custom: GapoEntityMapper.mapToPostCreateAt),
      Field('contentRtf', custom: 1),
      Field('reactions', custom: GapoEntityMapper.mapToPostReactions),
      Field('seen', custom: GapoEntityMapper.mapToPostSeen),
    ]),
    MapType<WorkPlaceCommunityMemberEntity, GPPostMention>(fields: [
      Field('id', from: 'gpUserId'),
      Field('type', custom: GPMentionType.tag),
    ]),

    // Thread
    MapType<WorkPlaceConversationEntity, GPThread>(fields: [
      Field('userId', custom: GapoEntityMapper.mapToThreadUserId),
      Field('threadId', from: 'id'),
      Field('participantIds',
          custom: GapoEntityMapper.mapToThreadParticipantIds),
      Field('type', custom: GapoEntityMapper.mapToThreadType),
      Field('name', custom: GapoEntityMapper.mapToThreadName),
      Field('isRead', custom: GapoEntityMapper.mapToThreadIsRead),
    ]),

    // Message
    MapType<WorkPlaceMessagesEntity, GPMessage>(fields: [
      Field('threadId', custom: GapoEntityMapper.mapToMessageThreadId),
      Field('type', custom: GapoEntityMapper.mapToMessageType),
      Field('from', custom: GapoEntityMapper.mapToMessageFrom),
      Field('text', from: 'message'),
      Field('media', custom: GapoEntityMapper.mapToMessageMedia),
      Field('createdAt', custom: GapoEntityMapper.mapToMessageCreateAt),
      Field('isMarkdownText', custom: 0),
    ]),

    // Comment
    MapType<WorkPlaceMessageTagsEntity, GPCommentMention>(fields: [
      Field('mentionId', custom: GapoEntityMapper.mapToMentionId),
    ]),
    MapType<WorkPlaceCommentEntity, GPComment>(fields: [
      Field('commentAs', custom: GapoEntityMapper.mapToCommentAs),
      Field('text', custom: GapoEntityMapper.mapToCommentText),
      Field('type', custom: GapoEntityMapper.mapToCommentType),
      Field('medias', custom: GapoEntityMapper.mapToCommentMedias),
      Field('status', custom: GPCommentStatus.approved),
      Field('targetType', custom: 'post'),
      Field('dataSource', custom: 5),
      Field('createdAt', custom: GapoEntityMapper.mapToCommentCreateAt),
      Field('reactions', custom: GapoEntityMapper.mapToCommentReactions),
      Field('replies', custom: GapoEntityMapper.mapToCommentReplies),
    ]),
    MapType<WorkPlaceReactionEntity, GPReaction>(fields: [
      Field('userId', from: 'gpUserId'),
      Field('type', custom: GapoEntityMapper.mapToReactionType),
    ]),
    MapType<WorkPlaceCommunityMemberEntity, GPCommentAs>(fields: [
      Field('authorType', custom: GPCommentAsAuthorType.user),
      Field('authorId', from: 'gpUserId'),
    ]),
    MapType<GPUploadImageResponseModel, UploadResponseEntity>(),
    MapType<GPUploadFileResponseModel, UploadResponseEntity>(),
    MapType<UploadResponseEntity, GPUploadResponse>(),
  ],
)
class GapoEntityMapper extends $GapoEntityMapper {
  const GapoEntityMapper();

  static String? mapToGPGroupCover(WorkPlaceGroupEntity input) {
    if (input.cover == null) return null;
    return input.cover!.gpCoverLink;
  }

  static GPGroupPrivacy? mapToGPGroupPrivacy(WorkPlaceGroupEntity input) {
    if (input.privacy == null) return null;
    switch (input.privacy) {
      case WorkPlaceGroupPrivacy.open:
        return GPGroupPrivacy.public;
      case WorkPlaceGroupPrivacy.closed:
        return GPGroupPrivacy.closed;
      default:
        return null;
    }
  }

  static String? mapToGPGroupCreateAt(WorkPlaceGroupEntity input) {
    if (input.createdTime == null) return null;
    return DateFormat('yyyy-MM-ddTHH:mm:ssZ').format(input.createdTime!);
  }

  static List<GPUser> mapToGPGroupPreviewMembers(WorkPlaceGroupEntity input) {
    final output = const GapoEntityMapper()
        .convertList<WorkPlaceCommunityMemberEntity, GPUser>(input.members);
    return output;
  }

  static GPUser? mapToGPGroupOwner(WorkPlaceGroupEntity input) {
    if (input.owner.value == null) return null;

    return const GapoEntityMapper()
        .convert<WorkPlaceCommunityMemberEntity, GPUser>(input.owner.value);
  }

  static String? mapToPostUserId(WorkPlaceFeedEntity input) {
    return input.from.value?.gpUserId.toString() ?? '';
  }

  static List<GPPostMedia>? mapToPostMedia(WorkPlaceFeedEntity input) {
    final attachments = input.attachments;
    if (attachments.isEmpty) return null;

    final List<GPPostMedia> output = [];

    // final mediaList =
    //     attachments.fold<List<GPPostMedia>>([], (list, attachment) {
    //   if (attachment.type == AttachmentType.question) return list;
    //   if (attachment.subAttachments == null) {
    //     final type = attachment.type;
    //     final gpType = _mapAttachmentType(type);
    //     return list..add(GPPostMedia(src: attachment.gpLink, type: gpType));
    //   } else {
    //     for (WorkPlaceAttachmentEntity element
    //         in attachment.subAttachments!.attachments ?? []) {
    //       final type = element.type;
    //       final gpType = _mapAttachmentType(type);
    //       list.add(GPPostMedia(src: element.gpLink, type: gpType));
    //     }
    //     return list;
    //   }
    // });

    for (var attachment in attachments) {
      if (attachment.uploadResponse == null) continue;
      final gpMedia = attachment.mapToPostMedia();
      if (gpMedia != null) {
        output.add(gpMedia);
      }
    }

    return output;
  }

  static List<GPComment>? mapToPostComments(WorkPlaceFeedEntity input) {
    final comments = input.comments.toList();
    comments.removeWhere((element) => element.from.value == null);

    return const GapoEntityMapper()
        .convertList<WorkPlaceCommentEntity, GPComment>(comments);
  }

  static List<GPPostMention>? mapToPostMention(WorkPlaceFeedEntity input) {
    return input.to
        .map((e) => const GapoEntityMapper()
            .convert<WorkPlaceCommunityMemberEntity, GPPostMention>(e))
        .toList();
  }

  static GPPostPrivacy? mapToPostPrivacy(WorkPlaceFeedEntity input) {
    if (input.isUserFeed) {
      return GPPostPrivacy.work;
    } else {
      return GPPostPrivacy.group;
    }
  }

  static String? mapToPostTarget(WorkPlaceFeedEntity input) {
    if (input.isUserFeed) {
      return "user:${input.from.value?.gpUserId}";
    } else {
      return "group:${input.group.value?.gpGroupId}";
    }
  }

  static String? mapToPostCreateAt(WorkPlaceFeedEntity input) {
    if (input.createdTime == null) return null;
    return DateFormat('yyyy-MM-ddTHH:mm:ssZ').format(input.createdTime!);
  }

  static int? mapToPostContentRtf(WorkPlaceFeedEntity input) {
    if (input.formatting == null) return null;
    return input.formatting == WorkPlaceFormatting.markdown ? 1 : 0;
  }

  static List<int>? mapToThreadParticipantIds(
      WorkPlaceConversationEntity input) {
    return input.participants.map((e) => e.gpUserId).whereType<int>().toList();
  }

  static int? mapToThreadUserId(WorkPlaceConversationEntity input) {
    if (input.participants.isEmpty) {
      return null;
    }
    return input.participants.firstWhere((e) => e.gpUserId != null).gpUserId;
  }

  static GPThreadType mapToThreadType(WorkPlaceConversationEntity input) {
    // switch (input.type) {
    //   case WorkPlaceThreadType.group:
    //     return GPThreadType.group;
    //   case WorkPlaceThreadType.direct:
    //     return GPThreadType.direct;
    // }

    // Chuyển hết về group do ko import nhiều lần thread direct
    return GPThreadType.group;
  }

  static String mapToThreadName(WorkPlaceConversationEntity input) {
    if (input.name == null) {
      final participants = input.participants.map((e) => e.name).toList();
      // participants.removeAt(0);
      String name = participants.join(', ');
      const int maxLength = 100;
      if (name.length > maxLength) {
        name = name.substring(0, maxLength);
      }
      return name;
    } else {
      return input.name!;
    }
  }

  static int mapToThreadIsRead(WorkPlaceConversationEntity input) {
    return AppConstants.isReadConversation ? 1 : 0;
  }

  static GPMessageType? mapToMessageType(WorkPlaceMessagesEntity input) {
    return input.messageType;
  }

  static String? mapToMessageThreadId(WorkPlaceMessagesEntity input) {
    return input.conversation.value?.id ?? input.wpThreadId;
  }

  static int? mapToMessageFrom(WorkPlaceMessagesEntity input) {
    return input.from.value?.gpUserId;
  }

  static List<GPUploadResponse>? mapToMessageMedia(
      WorkPlaceMessagesEntity input) {
    if (input.sticker.value?.uploadResponse != null) {
      return [
        const GapoEntityMapper()
            .convert<UploadResponseEntity, GPUploadResponse>(
                input.sticker.value?.uploadResponse)
      ];
    }
    final attachments = List.from(input.attachments);
    attachments.removeWhere((element) => element.uploadResponse == null);
    return attachments.map((e) {
      return const GapoEntityMapper()
          .convert<UploadResponseEntity, GPUploadResponse>(e.uploadResponse);
    }).toList();
  }

  static GPCommentAs? mapToCommentAs(WorkPlaceCommentEntity input) {
    if (input.from.value == null) return null;

    return const GapoEntityMapper()
        .convert<WorkPlaceCommunityMemberEntity, GPCommentAs>(input.from.value);
  }

  static String? mapToCommentText(WorkPlaceCommentEntity input) {
    if (input.message == null) return null;
    return Strings.toEscaped(input.message).replaceAll(';', ',');
  }

  static String? mapToCommentCreateAt(WorkPlaceCommentEntity input) {
    if (input.createdTime == null) return null;
    return DateFormat('yyyy-MM-ddTHH:mm:ssZ').format(input.createdTime!);
  }

  static String? mapToMessageCreateAt(WorkPlaceMessagesEntity input) {
    if (input.createdTime == null) return null;
    return DateFormat('yyyy-MM-ddTHH:mm:ssZ').format(input.createdTime!);
  }

  static GPCommentType? mapToCommentType(WorkPlaceCommentEntity input) {
    if (input.attachment.value != null) {
      return GPCommentType.image;
    }

    return GPCommentType.text;
  }

  static int? mapToMentionId(WorkPlaceMessageTagsEntity input) {
    if (input.id == null || input.id?.isEmpty == true) return null;

    return int.tryParse(input.id!);
  }

  static List<GPCommentMedia>? mapToCommentMedias(
      WorkPlaceCommentEntity input) {
    if (input.attachment.value == null ||
        input.attachment.value?.uploadResponse == null) return null;

    GPCommentMediaType? type;

    final attachmentEntity = input.attachment.value;
    final attachmentType = attachmentEntity?.type;

    switch (attachmentType) {
      case AttachmentType.photo:
      case AttachmentType.animateImageShare:
      case AttachmentType.sticker:
        type = GPCommentMediaType.image;
        break;
      case AttachmentType.video:
        type = GPCommentMediaType.video;
        break;
      case AttachmentType.fileUpload:
        type = GPCommentMediaType.file;
        break;
      default:
        type = GPCommentMediaType.file;
    }

    return [
      GPCommentMedia(
        width: attachmentEntity?.media?.image?.width,
        height: attachmentEntity?.media?.image?.height,
        src: attachmentEntity?.gpLink,
        type: type,
        id: attachmentEntity?.gpId,
        fileType: attachmentEntity?.uploadResponse?.fileType,
        name: attachmentEntity?.uploadResponse?.name,
        size: attachmentEntity?.uploadResponse?.size,
      )
    ];
  }

  static List<GPReaction>? mapToPostReactions(WorkPlaceFeedEntity input) {
    if (input.reactions == null) return null;
    final reactions = input.reactions!.toList();
    reactions.removeWhere((element) => element.gpUserId == null);
    return const GapoEntityMapper()
        .convertList<WorkPlaceReactionEntity, GPReaction>(reactions);
  }

  static List<GPUser>? mapToPostSeen(WorkPlaceFeedEntity input) {
    if (input.seen.isEmpty) return null;
    return const GapoEntityMapper()
        .convertList<WorkPlaceCommunityMemberEntity, GPUser>(input.seen);
  }

  static List<GPReaction>? mapToCommentReactions(WorkPlaceCommentEntity input) {
    if (input.reactions == null) return null;
    final reactions = input.reactions!.toList();
    reactions.removeWhere((element) => element.gpUserId == null);
    return const GapoEntityMapper()
        .convertList<WorkPlaceReactionEntity, GPReaction>(reactions);
  }

  static List<GPComment> mapToCommentReplies(WorkPlaceCommentEntity input) {
    final replies = input.replies.toList();
    replies.removeWhere((element) => element.from.value == null);
    return const GapoEntityMapper()
        .convertList<WorkPlaceCommentEntity, GPComment>(replies);
  }

  static GPReactionType? mapToReactionType(WorkPlaceReactionEntity input) {
    switch (input.type) {
      case WorkPlaceReactionType.like:
        return GPReactionType.like;
      case WorkPlaceReactionType.love:
        return GPReactionType.love;
      case WorkPlaceReactionType.haha:
        return GPReactionType.laugh;
      case WorkPlaceReactionType.sad:
        return GPReactionType.sad;
      case WorkPlaceReactionType.wow:
      case WorkPlaceReactionType.angry:
      case WorkPlaceReactionType.care:
        return GPReactionType.like;
      default:
        return null;
    }
  }
}
