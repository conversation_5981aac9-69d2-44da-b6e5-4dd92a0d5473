import 'dart:convert';

import 'package:gp_fbwp_crawler/domain/entity/entity.dart';
import 'package:isar/isar.dart';

part 'crawl_checkpoint.entity.g.dart';

@Collection()
class CrawlCheckpoint {
  CrawlCheckpoint({
    required this.crawlType,
    this.nextQueries,
    this.checkpointId,
    this.isDone = false,
    this.id,
    this.itemLength,
    this.createdAt,
    this.updatedAt,
    this.lastDoneAt,
  });

  @Enumerated(EnumType.name)
  final GPBaseCrawlType crawlType;
  final String? nextQueries;
  final String? checkpointId;
  final String? id;
  final bool isDone;
  final int? itemLength;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? lastDoneAt;

  @ignore
  Map<String, String>? get requestParams {
    if (nextQueries == null) return null;
    return jsonDecode(nextQueries!) as Map<String, String>;
  }

  late final Id dbId = id != null ? id.hashCode : crawlType.name.hashCode;

  CrawlCheckpoint copyWith({
    String? nextQueries,
    String? checkpointId,
    String? id,
    bool? isDone,
    int? itemLength,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastDoneAt,
  }) {
    return CrawlCheckpoint(
      crawlType: crawlType,
      nextQueries: nextQueries,
      checkpointId: checkpointId ?? this.checkpointId,
      id: id ?? this.id,
      isDone: isDone ?? this.isDone,
      itemLength: itemLength ?? this.itemLength,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastDoneAt: lastDoneAt ?? this.lastDoneAt,
    );
  }
}
