/*
 * Created Date: Saturday, 1st June 2024, 12:43:54
 * Author: ToanNM
 * -----
 * Last Modified: Sunday, 23rd June 2024 09:12:17
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:isar/isar.dart';

part 'base_crawl_log.entity.g.dart';

@Collection()
final class GPBaseCrawlLogEntity {
  GPBaseCrawlLogEntity({
    required this.objectId,
    required this.crawlType,
    required this.lastUpdatedAt,
    this.crawlDownloadStatus,
    this.crawlSyncStatus,
  });

  final Id id = Isar.autoIncrement;

  @Index(type: IndexType.value, caseSensitive: false)
  final String objectId;

  @Index(type: IndexType.value, caseSensitive: false)
  @enumerated
  final GPBaseCrawlType crawlType;

  final DateTime lastUpdatedAt;

  final BaseCrawlDownloadStatus? crawlDownloadStatus;
  final BaseCrawlSyncStatus? crawlSyncStatus;
}
