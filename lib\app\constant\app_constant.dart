import 'package:gp_fbwp_crawler/domain/entity/entity.dart';
import 'package:isar/isar.dart';

class AppConstants {
  /// Các thread import sẽ được đánh dấu đã đọc
  static bool isReadConversation = true;
  static bool needRunOnGapoWork = false;
  static bool deleteUploadedLocalFile = false;
  static String kDefaultPassword = 'Gapo@123';
  /// Bật nếu sử dụng đăng nhập SSO
  static bool useRandomPassword = false;
  static bool runInMultiClients = true;
  static bool isMainComputer = true;
  static String computerId = '';
  static int numPCCrawl = 2;
  static String listIdFilePath = "data/computer_ids.txt";
  static String databaseName = "gapo-0210";
  static String baseDatabaseName = "base";
  static String resourceFolderName = "gapo-resources";
  static String workspaceId = "583043415814937";
  static String token =
      "DQWRJRzY1S3dMNWpIdWpmNk9BZAFR6RWw4cHM5UjhpaHdqenlZAZAEdFWnVDUFFidDRtRk5hdDRCZATkxQTNZAM1B6blN4NEkzV01zZAkpLZAGxic2VCYmZAtSzVrT3VOallxNkhGZAHU5ZAnE0Q05jaW9ycTNqX2wwUFlXOXRIVjdMRUc0S0VVRFphdHdkbG5od3RpVFhzM3pKWmU2bWVPRXJjNFNRLVdoRkhHajlzaVpKMVRoak5USE1MRlRwM09MN0luclQ3TjZAERGVQNEY2ZAUdreXBTX0d3";
  static List<CollectionSchema<dynamic>> dbSchemas = [
    // base
    AppConfigEntitySchema,
    GPBaseCrawlLogEntitySchema,
    CrawlCheckpointSchema,
    // member
    WorkPlaceCommunityMemberEntitySchema,
    // group
    WorkPlaceGroupEntitySchema,
    // feed
    WorkPlaceFeedEntitySchema,
    // thread
    WorkPlaceConversationEntitySchema,
    WorkPlaceMessagesEntitySchema,
    WorkPlaceConversationAttachmentsEntitySchema,
    // other
    WorkPlaceAttachmentEntitySchema,
    WorkPlaceCommentEntitySchema,
    WorkPlaceStickerEntitySchema,
    // community
    WorkPlaceCommunityEntitySchema,
    GPLogEntitySchema,
    GPDashboardEntitySchema,
    // WorkPlaceGroupFeedEntitySchema,
    // WorkPlaceUserConversationsEntitySchema,
    // WorkPlaceUserFeedsEntitySchema,
    // WorkPlaceReactionEntitySchema,
  ];
  // static bool enableEmailFilter = false;

  static List<String> filterEmails = [
    // "<EMAIL>",
    // "<EMAIL>",
  ];

  // static List<String> filterEmails = [
  //   "<EMAIL>"
  // ];

  static List<String> userIds = [
    // '100023834177621'
    // '100076177455357',
    // '100066980774793',
    // '100068131471427',
    // '61559004009084',
    // '100068013916649',
    // '61551606293761',
    // '100067798907123',
  ];

  static final List<String> threadIds = [
    // 't_8293610154052754'
  ];

  static List<String> groupIds = [
    // '472037969048809',
  ];
}
