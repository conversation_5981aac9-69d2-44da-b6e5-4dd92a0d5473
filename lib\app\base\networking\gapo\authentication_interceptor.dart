import 'package:gp_core/core.dart';
import 'package:gp_fbwp_crawler/app/constant/constant.dart';

class AuthenticationInterceptor extends Interceptor {
  AuthenticationInterceptor();

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    options.headers.addAll(await _authHeaders());

    handler.next(options);
  }
}

Future<Map<String, String>> _authHeaders() async {
  String workspaceId = AppConstants.workspaceId; //Constants.workspaceId();
  String language = Constants.language();

  final String? userAgent = Constants.userAgent();

  final accessToken = await TokenManager.accessToken();

  Map<String, String> headers = {
    'x-gapo-workspace-id': workspaceId,
    'x-gapo-lang': language,
    'x-gapo-user-id': Constants.userId(),
  };

  if (accessToken.isNotEmpty) {
    headers.addIf(
        accessToken.isNotEmpty, "Authorization", "Bearer $accessToken");
  }

  if (userAgent != null && userAgent.isNotEmpty) {
    headers.addAll({
      'User-Agent': userAgent,
    });
  }

  return headers;
}
