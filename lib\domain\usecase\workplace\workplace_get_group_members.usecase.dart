/*
 * Created Date: Sunday, 11th August 2024, 14:27:18
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Monday, 9th September 2024 09:45:12
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:developer';

import 'package:gp_core/core.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';
import 'package:retry/retry.dart';

// ignore_for_file: public_member_api_docs

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class WorkPlaceGroupMembersUseCase extends GPBaseFutureUseCase<
    WorkPlaceGroupMembersInput,
    WorkPlaceListReponse<WorkPlaceUser>> with WorkPlaceFetchAllDataMixin {
  WorkPlaceGroupMembersUseCase(
    @Named('kWorkPlaceRepository') this._worplaceRepository,
  );

  final WorkPlaceRepository _worplaceRepository;

  @override
  Future<WorkPlaceListReponse<WorkPlaceUser>> buildUseCase(
    WorkPlaceGroupMembersInput input,
  ) async {
    return await retry(
      () async {
        final params = WorkPlaceBaseParams(
          id: input.groupId,
          fields: input.fields,
          limit: '30'
        );

        return await fetchAllData<WorkPlaceUser>(
          params: params,
          loadFunction: _worplaceRepository.groupMembers,
        );
      },
      maxAttempts: 2,
      retryIf: (e) {
        if (e is DioException && e.response?.statusCode == 400) {
          return false;
        }

        return true;
      },
      onRetry: (e) {
        log('ERROR: WorkPlaceGroupMembersUseCase error -> $e');
      },
    );
  }
}

class WorkPlaceGroupMembersInput extends GPBaseInput {
  const WorkPlaceGroupMembersInput(
      {required this.groupId, this.fields = 'id,name,email'});

  final String groupId;
  final String? fields;
}

class WorkPlaceGroupMemberParams {
  const WorkPlaceGroupMemberParams({
    this.fields,
    this.next,
    required this.groupId,
  });

  final String? fields;
  final String? next;
  final String groupId;
}
