import 'dart:async';

import 'package:get_it/get_it.dart';
import 'package:gp_fbwp_crawler/app/features/crawler/queues/base_queue.dart';
import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:isar/isar.dart';

import '../../../../../mapper/gp_mapper.dart';
import '../../mixin/checkpoint_mixin.dart';
import '../../mixin/upload_mixin.dart';

class BaseGroupQueue extends GPBaseQueue
    with
        GPMapperMixin,
        AttachmentHandlerMixin,
        CheckpointMixin,
        _VariablesMixin,
        _DbHandlerMixin {
  BaseGroupQueue({required super.crawlQueueBloc, required super.commonBloc});

  bool needToDownloadOrUpload(WorkPlaceGroupEntity groupEntity) {
    if (groupEntity.cover != null) {
      final bool hasSource = groupEntity.cover?.source?.isNotEmpty ?? false;

      final bool alreadyDownloaded =
          (groupEntity.cover!.gpCoverLink?.isNotEmpty ?? false);

      if (!hasSource || alreadyDownloaded) {
        return false;
      }

      return true;
    }

    return false;
  }
}

mixin _VariablesMixin {
  final wpGetAllGroupsUseCase = GetIt.I<WorkPlaceGetAllGroupsUseCase>();
  final wpGetAllGroupsByIdsUseCase =
      GetIt.I<WorkPlaceGetAllGroupsByIdUseCase>();
  final groupMembersUseCase = GetIt.I<WorkPlaceGroupMembersUseCase>();

  final gpCreateGroupUseCase = GetIt.I<GPCreateGroupUseCase>();
  final gpInviteGroupUseCase = GetIt.I<GPInviteGroupUseCase>();
  final gpAdjustMemberRoleGroupUseCase =
      GetIt.I<GPAdjustMemberRoleGroupUseCase>();
  final gpLeaveGroupUseCase = GetIt.I<GPLeaveGroupUseCase>();
}

mixin _DbHandlerMixin {
  late final Isar isar = GetIt.I<Isar>(instanceName: 'kIsar');
  final localService =
      GetIt.I<WorkPlaceLocalService>(instanceName: 'kWorkPlaceLocalService');

  Future getAllGroups() {
    return localService.getAllGroups();
  }

  Future saveGroup(WorkPlaceGroupEntity groupEntity) {
    return localService.saveGroup(groupEntity);
  }

  Future saveGroups(List<WorkPlaceGroupEntity> groups) {
    return localService.saveGroups(groups);
  }

  Future updateGroup(List<WorkPlaceGroupEntity> groupEntitys) async {
    return localService.updateGroup(groupEntitys);
  }

  Future getCommunity() async {
    return localService.getCommunity();
  }
}
