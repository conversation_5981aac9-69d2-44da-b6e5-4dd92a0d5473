/*
 * Created Date: Friday, 21st June 2024, 21:31:21
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Saturday, 31st August 2024 10:03:04
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:developer';

import 'package:get_it/get_it.dart';
import 'package:gp_core_v2/base/constants/constants.dart';
import 'package:gp_core_v2/base/usecase/usecase.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/usecase/usecase.dart';
import 'package:gp_fbwp_crawler/helpers/helpers.dart';
import 'package:injectable/injectable.dart';
import 'package:gp_core/core.dart';
import 'package:retry/retry.dart';

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class InitGPAdminUseCase
    extends GPBaseFutureUseCase<InitGPAdminInput, InitGPAdminOutput> {
  const InitGPAdminUseCase();

  @override
  Future<InitGPAdminOutput> buildUseCase(
    InitGPAdminInput input,
  ) async {
    return await retry(
      () async {
        final authResponse = await _login();
        if (authResponse != null) {
          await _saveTokenInfo(authResponse);

          return const InitGPAdminOutput(isSuccess: true);
        }
        
        return const InitGPAdminOutput(isSuccess: false);
      },
      maxAttempts: 5,
    );
  }

  Future<AuthResponse?> _login() async {
    final GPAuthUseCase authUseCase = GetIt.I<GPAuthUseCase>();
    final ApiResponseV2<AuthResponse> authResponse = await authUseCase.execute(
      GPAuthInput(
        params: AuthParams(
          clientId: "6n6rwo86qmx7u8aahgrq",
          deviceModel: "Simulator iPhone 11",
          email: GetIt.I<String>(instanceName: 'kAdminEmail'),
          password: GetIt.I<String>(instanceName: 'kAdminPassword'),
        ),
      ),
    );

    if (!authResponse.data.hasData) {
      log('no Auth response!!!');
      return null;
    }

    return authResponse.data;
  }

  Future _saveTokenInfo(AuthResponse authResponse) async {
    if (!authResponse.hasData) return;

    final token = authResponse.accessToken;
    final jwtData = JwtDecoder.decode(authResponse.accessToken!);
    final wsId = jwtData['wsids'].toString().split(',').first;
    // final wsId = '582845796066761';
    
    final tokenInfo = TokenInfo.fromJson({
      "userId": authResponse.userId.toString(),
      "displayName": 'Admin',
      "avatar": '',
      "accessToken": token,
      "refreshToken": authResponse.refreshToken,
      "workspaceId": wsId,
      "language": Constants.language(),
      "environment": Constants.environment().name,
    });

    Constants.updateTokenInfo(tokenInfo);
    await TokenManager.saveTokenInfo(tokenInfo);
  }
}

class InitGPAdminInput extends GPBaseInput {
  const InitGPAdminInput();
}

class InitGPAdminOutput extends GPBaseOutput {
  const InitGPAdminOutput({required this.isSuccess});
  final bool isSuccess;
}
