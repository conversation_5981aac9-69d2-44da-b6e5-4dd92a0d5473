import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'bloc/bloc.dart';
import 'widgets/widgets.dart';

class {{feature_name.pascalCase()}}Page extends StatelessWidget {
  const {{feature_name.pascalCase()}}Page({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<{{feature_name.pascalCase()}}Bloc>(
      create: (context) => {{feature_name.pascalCase()}}Bloc(),
      child: BlocBuilder<{{feature_name.pascalCase()}}Bloc, {{feature_name.pascalCase()}}State>(
        builder: (BuildContext context, {{feature_name.pascalCase()}}State state) {
          return Scaffold(
            appBar: AppBar(),
            body: const {{feature_name.pascalCase()}}BodyPage(),
          );        
        },
      ),
    );
  }
}
