// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workplace_post_comments_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkPlaceCommentsResponse _$WorkPlaceCommentsResponseFromJson(
        Map<String, dynamic> json) =>
    WorkPlaceCommentsResponse(
      id: json['id'] as String,
      commentCount: (json['comment_count'] as num?)?.toInt(),
      likeCount: (json['like_count'] as num?)?.toInt(),
      message: json['message'] as String?,
      createdTime: _$JsonConverterFromJson<String, DateTime?>(
          json['created_time'], const DateTimeConverter().fromJson),
      from: json['from'] == null
          ? null
          : WorkPlaceUser.fromJson(json['from'] as Map<String, dynamic>),
      attachment: json['attachment'] == null
          ? null
          : WorkPlacePostAttachmentsResponse.fromJson(
              json['attachment'] as Map<String, dynamic>),
      messageTags: (json['message_tags'] as List<dynamic>?)
          ?.map((e) => WorkPlaceMessageTags.fromJson(e as Map<String, dynamic>))
          .toList(),
      reactions: json['reactions'] == null
          ? null
          : WorkPlaceListReponse<WorkPlaceReaction>.fromJson(
              json['reactions'] as Map<String, dynamic>),
      likes: json['likes'] == null
          ? null
          : WorkPlaceListReponse<WorkPlaceReaction>.fromJson(
              json['likes'] as Map<String, dynamic>),
    );

Value? _$JsonConverterFromJson<Json, Value>(
  Object? json,
  Value? Function(Json json) fromJson,
) =>
    json == null ? null : fromJson(json as Json);
