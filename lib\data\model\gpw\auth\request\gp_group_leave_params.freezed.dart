// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'gp_group_leave_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$GPGroupLeaveParams {
  @JsonKey(name: 'group_id')
  String get groupId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GPGroupLeaveParamsCopyWith<GPGroupLeaveParams> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GPGroupLeaveParamsCopyWith<$Res> {
  factory $GPGroupLeaveParamsCopyWith(
          GPGroupLeaveParams value, $Res Function(GPGroupLeaveParams) then) =
      _$GPGroupLeaveParamsCopyWithImpl<$Res, GPGroupLeaveParams>;
  @useResult
  $Res call({@JsonKey(name: 'group_id') String groupId});
}

/// @nodoc
class _$GPGroupLeaveParamsCopyWithImpl<$Res, $Val extends GPGroupLeaveParams>
    implements $GPGroupLeaveParamsCopyWith<$Res> {
  _$GPGroupLeaveParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? groupId = null,
  }) {
    return _then(_value.copyWith(
      groupId: null == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GPGroupLeaveParamsImplCopyWith<$Res>
    implements $GPGroupLeaveParamsCopyWith<$Res> {
  factory _$$GPGroupLeaveParamsImplCopyWith(_$GPGroupLeaveParamsImpl value,
          $Res Function(_$GPGroupLeaveParamsImpl) then) =
      __$$GPGroupLeaveParamsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({@JsonKey(name: 'group_id') String groupId});
}

/// @nodoc
class __$$GPGroupLeaveParamsImplCopyWithImpl<$Res>
    extends _$GPGroupLeaveParamsCopyWithImpl<$Res, _$GPGroupLeaveParamsImpl>
    implements _$$GPGroupLeaveParamsImplCopyWith<$Res> {
  __$$GPGroupLeaveParamsImplCopyWithImpl(_$GPGroupLeaveParamsImpl _value,
      $Res Function(_$GPGroupLeaveParamsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? groupId = null,
  }) {
    return _then(_$GPGroupLeaveParamsImpl(
      groupId: null == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _$GPGroupLeaveParamsImpl implements _GPGroupLeaveParams {
  const _$GPGroupLeaveParamsImpl(
      {@JsonKey(name: 'group_id') required this.groupId});

  @override
  @JsonKey(name: 'group_id')
  final String groupId;

  @override
  String toString() {
    return 'GPGroupLeaveParams(groupId: $groupId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GPGroupLeaveParamsImpl &&
            (identical(other.groupId, groupId) || other.groupId == groupId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, groupId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GPGroupLeaveParamsImplCopyWith<_$GPGroupLeaveParamsImpl> get copyWith =>
      __$$GPGroupLeaveParamsImplCopyWithImpl<_$GPGroupLeaveParamsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GPGroupLeaveParamsImplToJson(
      this,
    );
  }
}

abstract class _GPGroupLeaveParams implements GPGroupLeaveParams {
  const factory _GPGroupLeaveParams(
          {@JsonKey(name: 'group_id') required final String groupId}) =
      _$GPGroupLeaveParamsImpl;

  @override
  @JsonKey(name: 'group_id')
  String get groupId;
  @override
  @JsonKey(ignore: true)
  _$$GPGroupLeaveParamsImplCopyWith<_$GPGroupLeaveParamsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
