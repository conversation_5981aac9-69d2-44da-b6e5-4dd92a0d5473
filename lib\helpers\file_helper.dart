import 'dart:developer';
import 'dart:ffi';
import 'dart:io';

import 'dart:convert';
import 'package:async_task/async_task_extension.dart';
import 'package:csv/csv.dart';
import 'package:gp_fbwp_crawler/domain/entity/workplace/group/workplace_group.entity.dart';
import 'package:gp_fbwp_crawler/domain/entity/gapo/gp_post.dart';
import 'package:gp_fbwp_crawler/domain/entity/gapo/gpuser.dart' as local;
import 'package:gp_fbwp_crawler/domain/entity/gapo/gp_comment.dart';
import 'package:gp_fbwp_crawler/domain/entity/gapo/gp_migrate.dart';
import 'package:gp_core/core.dart' hide GPUser;
import 'package:gp_fbwp_crawler/app/app.dart';
import 'package:gp_fbwp_crawler/domain/entity/enums/enums.dart';
import 'package:gp_fbwp_crawler/helpers/helpers.dart';
import 'package:isar/isar.dart';

class FileHelper {
  static Future<Directory> getDownloadDirectory() async {
    final documentDirectory = await getApplicationDocumentsDirectory();
    String path =
        '${documentDirectory.path}/${AppConstants.resourceFolderName}';

    return Directory(path);
  }

  static Future<String> downloadDirectoryPath(String fileName) async {
    final documentDirectory = await getApplicationDocumentsDirectory();
    String path =
        '${documentDirectory.path}/${AppConstants.resourceFolderName}';

    try {
      if (!File(path).existsSync()) {
        await Directory(path).create();
      }
    } catch (e) {
      log('e -> $e');
    }

    String filePath = '$path/$fileName';

    return filePath;
  }

  static String? getFileNameFromUrl(String url) {
    final regex = RegExp(r"[^\/]+\.*\?");
    final match = regex.firstMatch(url);

    if (match != null) {
      final result = match.group(0) ?? '';
      return result.substring(0, result.length - 1);
    } else {
      return null;
    }
  }

  static Future<String> getFilePathFromUrl(String url) async {
    String fileName;
    try {
      fileName = Uri.decodeFull(getFileNameFromUrl(url) ?? '');
    } catch (e) {
      fileName = getFileNameFromUrl(url) ?? '';
    }

    if (fileName.isNotEmpty) {
      return await downloadDirectoryPath(fileName);
    } else {
      return '';
    }
  }

  static GPApiUploadType apiUploadType(String fileName) {
    final isImage =
        FileExtensions.kPhotoFileExtensions.contains(fileName.split(".").last);
    final isVideo =
        FileExtensions.kVideoFileExtensions.contains(fileName.split(".").last);
    final isAudio =
        FileExtensions.kAudioFileExtensions.contains(fileName.split(".").last);

    return isVideo
        ? GPApiUploadType.video
        : isAudio
            ? GPApiUploadType.audio
            : isImage
                ? GPApiUploadType.image
                : GPApiUploadType.files;
  }

  static Future saveCSV(String filename, String csv) async {
    String? outputFile = await FilePicker.platform.saveFile(
      dialogTitle: l10n.download_csv_title,
      fileName: filename,
    );

    try {
      File returnedFile = File('$outputFile');
      await returnedFile.writeAsString(csv);
      log("File exported successfully!");
    } catch (e) {
      log("File exported failed!");
    }
  }
}

extension FileSizeExtensions on num {
  /// method returns a human readable string representing a file size
  /// size can be passed as number or as string
  /// the optional parameter 'round' specifies the number of numbers after comma/point (default is 2)
  /// the optional boolean parameter 'useBase1024' specifies if we should count in 1024's (true) or 1000's (false). e.g. 1KB = 1024B (default is true)
  String toHumanReadableFileSize({int round = 2, bool useBase1024 = true}) {
    const List<String> affixes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];

    num divider = useBase1024 ? 1024 : 1000;

    num size = this;
    num runningDivider = divider;
    num runningPreviousDivider = 0;
    int affix = 0;

    while (size >= runningDivider && affix < affixes.length - 1) {
      runningPreviousDivider = runningDivider;
      runningDivider *= divider;
      affix++;
    }

    String result =
        (runningPreviousDivider == 0 ? size : size / runningPreviousDivider)
            .toStringAsFixed(round);

    //Check if the result ends with .00000 (depending on how many decimals) and remove it if found.
    if (result.endsWith("0" * round)) {
      result = result.substring(0, result.length - round - 1);
    }

    return "$result ${affixes[affix]}";
  }
}

//Dai
Future<void> selectMultipleFiles() async {
  FilePickerResult? result =
      await FilePicker.platform.pickFiles(allowMultiple: true);
  if (result != null) {
    List<String?> filePaths = result.paths;
    for (var filePath in filePaths) {
      print('File path: $filePath');
    }
  }
}

// Future<List<GPPost>> readAndMergeCSVFiles() async {
Future<List<GPPost>> readAndMergeCSVFiles() async {
  List<GPPost> mergedPosts = [];
  FilePickerResult? result = await FilePicker.platform.pickFiles(
    allowMultiple: true,
    type: FileType.custom,
    allowedExtensions: ['csv'],
  );

  if (result != null) {
    List<File> files =
        result.paths.whereType<String>().map((path) => File(path)).toList();
    for (var file in files) {
      try {
        String content = await file.readAsString(encoding: utf8);
        List<String> lines = content
            .split(RegExp(r'\r?\n'))
            .map((line) => line.trim())
            .where((line) => line.isNotEmpty)
            .toList();

        print("First 5 lines from CSV: ");
        lines.take(5).forEach(print);

        int dataStartIndex =
            lines.indexWhere((line) => RegExp(r'^[^,]+,').hasMatch(line));
        if (dataStartIndex == -1) {
          print("Could not find the start of data rows in file: ${file.path}");
          continue;
        }

        for (var i = dataStartIndex; i < lines.length; i++) {
          var row = lines[i];
          List<String> values =
              row.split(';').map((value) => cleanCsvValue(value)).toList();

          // Kiểm tra số cột trước khi xử lý
          if (values.length < 11) {
            print("Row does not have enough columns: $row");
            continue; // Bỏ qua dòng không hợp lệ
          }

          print("Processing row: $row");

          // Tiến hành xử lý dữ liệu như trước
          String? userId = values[0];
          String? content = values[1];
          int? contentRtf = int.tryParse(values[2]);
          List<GPPostMedia>? media = parseMedia(values[3]);
          List<GPPostMention>? mention = parseMention(values[4]);
          GPPostPrivacy? privacy = parsePrivacy(values[5]);
          String? target = values[6];
          List<GPComment>? comments = parseComments(values[7]);
          List<local.GPUser>? seen = parseSeen(values[8]);
          List<GPReaction>? reactions = parseReactions(values[9]);
          String? createdAt = values[10];

          if (userId.isNotEmpty) {
            mergedPosts.add(GPPost(
              userId: userId,
              content: content,
              contentRtf: contentRtf,
              media: media,
              mention: mention,
              privacy: privacy,
              target: target,
              comments: comments,
              seen: seen,
              reactions: reactions,
              createdAt: createdAt,
            ));
          } else {
            print("Invalid user ID in row: $row");
          }
        }
      } catch (e) {
        print("Error reading file: ${file.path}");
        print(e);
      }
    }
  }

  return mergedPosts;
}

String cleanCsvValue(String value) {
  // Loại bỏ dấu nháy đơn hoặc nháy kép ở đầu và cuối chuỗi
  value = value.replaceAll(RegExp(r"^[\']|[\']$"), '').trim();

  // Loại bỏ các dấu \ dư thừa trong chuỗi
  return value.replaceAll(r'\', '');
}

Future<void> exportPostsToCSV(List<GPPost> posts, String filename) async {
  List<List<dynamic>> csvData = [
    [
      'user_id',
      'content',
      'content_rtf',
      'media',
      'mention',
      'privacy',
      'target',
      'comments',
      'seen',
      'reactions',
      'created_at'
    ]
  ];

  for (var post in posts) {
    csvData.add([
      post.userId ?? '',
      post.content ?? '',
      post.contentRtf ?? '',
      post.media != null
          ? jsonEncode(post.media!.map((m) => m.toJson()).toList())
          : '',
      post.mention != null
          ? jsonEncode(post.mention!.map((m) => m.toJson()).toList())
          : '',
      post.privacy != null ? (post.privacy!.index + 1).toString() : '',
      post.target ?? '',
      post.comments != null
          ? jsonEncode(post.comments!.map((c) => c.toJson()).toList())
          : '',
      post.seen != null
          ? jsonEncode(post.seen!.map((s) => s.toJson()).toList())
          : '',
      post.reactions != null
          ? jsonEncode(post.reactions!.map((r) => r.toJson()).toList())
          : '',
      post.createdAt ?? '',
    ]);
  }

  String csv = const ListToCsvConverter(fieldDelimiter: ';').convert(csvData);

  String? outputFile = await FilePicker.platform.saveFile(
    dialogTitle: 'Chọn vị trí lưu file CSV',
    fileName: filename,
    type: FileType.custom,
    allowedExtensions: ['csv'],
  );

  if (outputFile != null) {
    try {
      File file = File(outputFile);
      await file.writeAsString(csv);
      print("File đã được xuất thành công tại: $outputFile");
    } catch (e) {
      print("Xuất file thất bại: $e");
    }
  } else {
    print("Người dùng đã hủy việc chọn vị trí lưu file");
  }
}

List<GPPostMedia>? parseMedia(String mediaString) {
  if (mediaString.isEmpty) return null;
  try {
    String cleanedString = cleanJsonString(mediaString);
    var parsed = json.decode(cleanedString);
    if (parsed is List) {
      return parsed.map((item) => GPPostMedia.fromJson(item)).toList();
    } else {
      print("Media data is not a list: $mediaString");
      return null;
    }
  } catch (e) {
    print("Error parsing media: $e");
    return null;
  }
}

GPPostPrivacy? parsePrivacy(String privacyString) {
  // Thử chuyển đổi chuỗi thành số
  int? privacyValue = int.tryParse(privacyString);

  // Nếu chuỗi hợp lệ và khớp với @JsonValue của enum, trả về giá trị tương ứng
  if (privacyValue != null) {
    for (var privacy in GPPostPrivacy.values) {
      if (privacy.index + 1 == privacyValue) {
        // Điều chỉnh theo giá trị @JsonValue
        return privacy;
      }
    }
  }

  // Nếu không hợp lệ, trả về null
  return null;
}

List<GPPostMention>? parseMention(String mentionString) {
  if (mentionString.isEmpty || mentionString == "'") {
    return null; // Trả về null nếu chuỗi trống hoặc chuỗi rỗng mảng
  }
  try {
    var cleanedString = cleanJsonString(mentionString);
    List<dynamic> jsonList = json.decode(cleanedString);
    return jsonList.map((item) => GPPostMention.fromJson(item)).toList();
  } catch (e) {
    print("Error parsing mentions: $e");
    return null;
  }
}

List<local.GPUser>? parseSeen(String seenString) {
  if (seenString.isEmpty || seenString == "'") {
    return null; // Trả về null nếu chuỗi trống hoặc chuỗi rỗng mảng
  }
  try {
    var cleanedString = cleanJsonString(seenString);
    List<dynamic> jsonList = json.decode(cleanedString);
    return jsonList.map((item) => local.GPUser.fromJson(item)).toList();
  } catch (e) {
    print("Error parsing seen: $e");
    return null;
  }
}

List<GPComment>? parseComments(String commentsString) {
  if (commentsString.isEmpty) return null;
  try {
    String cleanedString = cleanJsonString(commentsString);
    List<dynamic> jsonList = json.decode(cleanedString);
    return jsonList.map((item) => GPComment.fromJson(item)).toList();
  } catch (e) {
    print("Error parsing comments: $e");
    return null;
  }
}

List<GPReaction>? parseReactions(String reactionsString) {
  if (reactionsString.isEmpty) return null;
  try {
    String cleanedString = cleanJsonString(reactionsString);
    List<dynamic> jsonList = json.decode(cleanedString);
    return jsonList.map((item) => GPReaction.fromJson(item)).toList();
  } catch (e) {
    print("Error parsing reactions: $e");
    return null;
  }
}

String cleanJsonString(String input) {
  String cleanedString =
      input.replaceAll(r'\\', ''); // Remove escaped backslashes
  cleanedString = cleanedString.replaceAll(
      RegExp(r"^[\']|[\']$"), ''); // Remove surrounding quotes
  return cleanedString;
}

Future<List<GPMigrate>> readAndMergeCSVFilesMigrateData() async {
  List<GPMigrate> mergedPosts = [];
  bool hasValidData = false;
  FilePickerResult? result = await FilePicker.platform.pickFiles(
    allowMultiple: true,
    type: FileType.custom,
    allowedExtensions: ['csv'],
  );

  if (result != null) {
    List<File> files = result.paths.map((path) => File(path!)).toList();
    for (var file in files) {
      try {
        String content = await file.readAsString(encoding: utf8);
        List<String> lines = content
            .split('\n')
            .map((line) => line.trim())
            .where((line) => line.isNotEmpty)
            .toList();

        print("First 5 lines of the file:");
        lines.take(5).forEach(print);

        int dataStartIndex = lines.indexWhere((line) => line.startsWith('1,'));
        if (dataStartIndex == -1) {
          print("Could not find the start of data rows in file: ${file.path}");
          continue;
        }

        for (var i = dataStartIndex; i < lines.length; i++) {
          var row = lines[i];
          List<String> values =
              row.split(',').map((value) => value.trim()).toList();

          print("Processing row: $row");
          if (values.length >= 10) {
            int? ordinal = int.tryParse(values[0]);
            String? groupName = values[1].replaceAll('"', '').trim();
            String? firstPostTime = values[2].replaceAll('"', '').trim();
            String? lastPostTime = values[3].replaceAll('"', '').trim();
            int? totalPeople = int.tryParse(values[4]);
            int? totalPosts = int.tryParse(values[5]);
            int? totalComments = int.tryParse(values[6]);
            int? totalReactions = int.tryParse(values[7]);
            int? totalMedia = int.tryParse(values[8]);
            int? totalViews = int.tryParse(values[9]);

            if (groupName.isNotEmpty) {
              hasValidData = true;
              mergedPosts.add(GPMigrate(
                ordinal: ordinal,
                groupName: [GPGroupOne(name: groupName)],
                firstPostTime: firstPostTime,
                lastPostTime: lastPostTime,
                totalPosts: totalPosts,
                totalComments: totalComments,
                totalReactions: totalReactions,
                totalMedia: totalMedia,
                totalViews: totalViews,
              ));
            } else {
              print("Invalid group name in row: $row");
            }
          } else {
            print("Row does not have enough columns: $row");
          }
        }
      } catch (e) {
        print('Error reading file: ${file.path}');
        print(e);
      }
    }
  }

  if (!hasValidData) {
    print('No valid data found in the CSV files.');
  } else {
    print('Successfully read ${mergedPosts.length} posts from CSV files.');
  }

  return mergedPosts;
}

Future<void> exportPosts1ToCSV(List<GPMigrate> posts, String filename) async {
  StringBuffer csvContent = StringBuffer();

  csvContent.writeln(
      'STT,Tên nhóm,"Thời gian Bài đăng đầu tiên","Thời gian Bài đăng cuối cùng","Tổng số thành viên","Tổng số bài viết","Tổng số bình luận","Tổng số lượt thả cảm xúc","Tổng số media","Tổng số lượt xem"');

  for (var post in posts) {
    csvContent.writeln(
        '${post.ordinal ?? ""},${post.groupName?.first.name ?? ""},${post.firstPostTime ?? ""},${post.lastPostTime ?? ""},${post.totalPeople ?? ""},${post.totalPosts ?? ""},${post.totalComments ?? ""},${post.totalReactions ?? ""},${post.totalMedia ?? ""},${post.totalViews ?? ""}');
  }

  String? outputFile = await FilePicker.platform.saveFile(
    dialogTitle: 'Chọn vị trí lưu file CSV',
    fileName: filename,
    type: FileType.custom,
    allowedExtensions: ['csv'],
  );

  if (outputFile != null) {
    try {
      File file = File(outputFile);
      await file.writeAsString(csvContent.toString());
      print("File đã được xuất thành công tại: $outputFile");
    } catch (e) {
      print("Xuất file thất bại: $e");
    }
  } else {
    print("Người dùng đã hủy việc chọn vị trí lưu file");
  }
}

class Groups {
  String id;
  String name;
  int totalmembers;

  Groups({
    required this.id,
    required this.name,
    required this.totalmembers,
  });
}

class GroupSummaryNew {
  String id;
  Groups groups;
  DateTime firstPostTime; // Thời gian bài đăng đầu tiên
  DateTime lastPostTime; // Thời gian bài đăng cuối cùng
  int totalPosts; // Tổng số bài viết
  int totalComments; // Tổng số bình luận
  int totalReactions; // Tổng số lượt cảm xúc
  int totalMedia; // Tổng số media

  GroupSummaryNew({
    required this.id,
    required this.firstPostTime,
    required this.lastPostTime,
    required this.groups,
    required this.totalPosts,
    required this.totalComments,
    required this.totalReactions,
    required this.totalMedia,
  });

  int get totalPeople => groups.totalmembers;
  String? get nameGroup => groups.name;
}

Future<Map<String, GroupSummaryNew>> readAndMergeCSVFiles2(
    Map<String, Groups> groupsMap) async {
  Map<String, GroupSummaryNew> userPosts = {};
  List<GPPost> mergedPosts = [];
  FilePickerResult? result = await FilePicker.platform.pickFiles(
    allowMultiple: true,
    type: FileType.custom,
    allowedExtensions: ['csv'],
  );

  if (result != null) {
    List<File> files =
        result.paths.whereType<String>().map((path) => File(path)).toList();
    for (var file in files) {
      try {
        String content = await file.readAsString(encoding: utf8);

        List<List<dynamic>> rows = const CsvToListConverter(
          fieldDelimiter: ';',
          textDelimiter: '"',
          eol: '\n',
        ).convert(content);

        for (int i = 1; i < rows.length; i++) {
          var values = rows[i];

          if (values.length < 11) {
            print("Row does not have enough columns: ${values.join(',')}");
            continue;
          }

          print("Processing row: ${values.join(',')}");

          String? userId = values[0]?.toString();
          String? content = values[1]?.toString();
          int? contentRtf = int.tryParse(values[2]?.toString() ?? '');
          List<GPPostMedia>? media =
              parseMediaAfter(values[3]?.toString() ?? '');
          List<GPPostMention>? mention =
              parseMentionAfter(values[4]?.toString() ?? '');
          GPPostPrivacy? privacy =
              parsePrivacyGroup(values[5]?.toString() ?? '');
          String? target = values[6]?.toString();
          List<GPComment>? comments =
              parseCommentsAfter(values[7]?.toString() ?? '');
          List<local.GPUser>? seen =
              parseSeenAfter(values[8]?.toString() ?? '');
          List<GPReaction>? reactions =
              parseReactionsAfter(values[9]?.toString() ?? '');
          String? createdAtStr = values[10]?.toString();

          DateTime? createdAt;
          try {
            createdAt =
                DateFormat("yyyy-MM-ddTHH:mm:ss").parse(createdAtStr ?? '');
          } catch (e) {
            print(
                "Invalid date format for createdAt in row: ${values.join(',')}");
            continue;
          }

          String? groupId = extractGroupId(target ?? '');

          if (groupId == null || groupId.isEmpty) {
            print("Invalid group ID in row: ${values.join(',')}");
            continue;
          }

          if (!groupsMap.containsKey(groupId)) {
            groupsMap[groupId] = Groups(
              id: groupId,
              name: '',
              totalmembers: 0,
            );
          }

          if (userId != null && userId.isNotEmpty) {
            GPPost post = GPPost(
              userId: userId,
              content: content,
              contentRtf: contentRtf,
              media: media ?? [],
              mention: mention ?? [],
              privacy: privacy ?? GPPostPrivacy.group,
              target: target,
              comments: comments ?? [],
              seen: seen ?? [],
              reactions: reactions ?? [],
              createdAt: createdAt.toString(),
            );
            mergedPosts.add(post);

            if (!userPosts.containsKey(groupId)) {
              userPosts[groupId] = GroupSummaryNew(
                id: groupId,
                firstPostTime: createdAt,
                lastPostTime: createdAt,
                groups: groupsMap[groupId]!,
                totalPosts: 0,
                totalComments: 0,
                totalReactions: 0,
                totalMedia: 0,
              );
            }

            GroupSummaryNew summary = userPosts[groupId]!;

            summary.totalPosts++;

            if (createdAt.isBefore(summary.firstPostTime)) {
              summary.firstPostTime = createdAt;
            }
            if (createdAt.isAfter(summary.lastPostTime) &&
                createdAt.isBefore(DateTime.now())) {
              summary.lastPostTime = createdAt;
            }

            summary.totalComments += countCommentsAndRep(comments);
            summary.totalReactions += reactions?.length ?? 0;

            summary.totalMedia += media?.length ?? 0;
          } else {
            print("Invalid user ID in row: ${values.join(',')}");
          }
        }
      } catch (e) {
        print("Error reading file: ${file.path}");
        print(e);
      }
    }
  }

  return userPosts;
}

String cleanCsvValueAfter(String value) {
  value = value.replaceAll(RegExp(r"^[\']|[\']$"), '').trim();

  return value.replaceAll(r'\', '');
}

List<GPPostMedia>? parseMediaAfter(String mediaString) {
  if (mediaString.isEmpty) return null;
  try {
    String cleanedString = cleanJsonString(mediaString);
    var parsed = json.decode(cleanedString);
    if (parsed is List) {
      return parsed.map((item) => GPPostMedia.fromJson(item)).toList();
    } else {
      print("Media data is not a list: $mediaString");
      return null;
    }
  } catch (e) {
    print("Error parsing media: $e");
    return null;
  }
}

GPPostPrivacy? parsePrivacyGroup(String privacyString) {
  int? privacyValue = int.tryParse(privacyString);

  if (privacyValue != null &&
      privacyValue >= 0 &&
      privacyValue < GPPostPrivacy.values.length) {
    return GPPostPrivacy.values[privacyValue];
  }

  return GPPostPrivacy.group;
}

GPPostPrivacy? parsePrivacyAll(String privacyString) {
  int? privacyValue = int.tryParse(privacyString);

  switch (privacyValue) {
    case 1:
      return GPPostPrivacy.public;
    case 2:
      return GPPostPrivacy.friend;
    case 3:
      return GPPostPrivacy.private;
    case 4:
      return GPPostPrivacy.group;
    case 5:
      return GPPostPrivacy.work;
    default:
      return null;
  }
}

GPPostPrivacy? parsePrivacyUser(String privacyString) {
  int? privacyValue = int.tryParse(privacyString);

  if (privacyValue != null &&
      privacyValue >= 0 &&
      privacyValue < GPPostPrivacy.values.length) {
    return GPPostPrivacy.values[privacyValue];
  }

  return GPPostPrivacy.work;
}

List<GPPostMention>? parseMentionAfter(String mentionString) {
  if (mentionString.isEmpty || mentionString == "'") {
    return null; // Trả về null nếu chuỗi trống hoặc chuỗi rỗng mảng
  }
  try {
    var cleanedString = cleanJsonString(mentionString);
    List<dynamic> jsonList = json.decode(cleanedString);
    return jsonList.map((item) => GPPostMention.fromJson(item)).toList();
  } catch (e) {
    print("Error parsing mentions: $e");
    return null;
  }
}

List<local.GPUser>? parseSeenAfter(String seenString) {
  if (seenString.isEmpty || seenString == "'") {
    return null;
  }
  try {
    var cleanedString = cleanJsonString(seenString);
    List<dynamic> jsonList = json.decode(cleanedString);
    return jsonList.map((item) => local.GPUser.fromJson(item)).toList();
  } catch (e) {
    print("Error parsing seen: $e");
    return null;
  }
}

List<GPComment>? parseCommentsAfter(String commentsString) {
  if (commentsString.isEmpty) return null;
  try {
    String cleanedString = cleanJsonString(commentsString);
    List<dynamic> jsonList = json.decode(cleanedString);
    return jsonList.map((item) => GPComment.fromJson(item)).toList();
  } catch (e) {
    print("Error parsing comments: $e");
    return null;
  }
}

List<GPReaction>? parseReactionsAfter(String reactionsString) {
  if (reactionsString.isEmpty) return null;
  try {
    String cleanedString = cleanJsonString(reactionsString);
    List<dynamic> jsonList = json.decode(cleanedString);
    return jsonList.map((item) => GPReaction.fromJson(item)).toList();
  } catch (e) {
    print("Error parsing reactions: $e");
    return null;
  }
}

String cleanJsonStringAfter(String input) {
  String cleanedString = input.replaceAll(r'\\', '');
  cleanedString = cleanedString.replaceAll(RegExp(r"^[\']|[\']$"), '');
  return cleanedString;
}

Future<void> exportGroupSummaryNewToCSV(
    Map<String, GroupSummaryNew> userPosts) async {
  final directory = await getApplicationDocumentsDirectory();
  final filePath = '${directory.path}/group_summary_test.csv';
  final file = File(filePath);

  StringBuffer csvContent = StringBuffer();
  csvContent.writeln(
      'STT;Tên nhóm;"Thời gian Bài đăng đầu tiên";"Thời gian Bài đăng cuối cùng";"Tổng số thành viên";"Tổng số bài viết";"Tổng số bình luận";"Tổng số lượt thả cảm xúc";"Tổng số media"');

  int ordinal = 1;
  final dateFormat = DateFormat("MM/dd/yyyy HH:mm");
  // final numberFormat = NumberFormat("#,###", "vi_VN");

  userPosts.forEach((groupId, summary) {
    String firstPostTime = summary.firstPostTime != null
        ? dateFormat.format(summary.firstPostTime!)
        : '';

    String lastPostTime = summary.lastPostTime != null
        ? dateFormat.format(summary.lastPostTime!)
        : '';

    csvContent.writeln(
        '$ordinal;${summary.nameGroup};$firstPostTime;$lastPostTime;${(summary.totalPeople)};${(summary.totalPosts)};${(summary.totalComments)};${(summary.totalReactions)};${(summary.totalMedia)}');
    ordinal++;
  });

  await file.writeAsString(csvContent.toString());

  print('Group summary data exported to CSV file at: $filePath');
}

String? extractGroupId(String target) {
  if (target.startsWith("group:")) {
    return target.split("group:")[1];
  }
  return null;
}

Future<Map<String, Groups>> readGroupsFromCSV() async {
  FilePickerResult? result = await FilePicker.platform.pickFiles(
    type: FileType.custom,
    allowedExtensions: ['csv'],
  );

  if (result == null) {
    print("No file selected.");
    return {};
  }

  String? filePath = result.files.single.path;
  if (filePath == null) {
    print("Invalid file path.");
    return {};
  }

  final file = File(filePath);

  if (!file.existsSync()) {
    print("File not found: $filePath");
    return {};
  }

  String content = await file.readAsString(encoding: utf8);

  List<List<dynamic>> rows = const CsvToListConverter(
    fieldDelimiter: ';',
    textDelimiter: "\'",
    eol: '\n',
  ).convert(content);

  Map<String, Groups> groupsMap = {};

  for (int i = 1; i < rows.length; i++) {
    var values = rows[i];

    if (values.length < 11) {
      print("Row does not have enough columns: ${values.join(';')}");
      continue;
    }

    try {
      String id = values[0].toString().replaceAll("'", "");
      String name = values[1].toString().replaceAll("'", "");
      int totalMembers =
          int.tryParse(values[10].toString().replaceAll("'", "")) ?? 0;

      Groups group = Groups(
        id: id,
        name: name,
        totalmembers: totalMembers,
      );

      groupsMap[id] = group;
    } catch (e) {
      print("Error processing row: ${values.join(';')} - Error: $e");
    }
  }

  return groupsMap;
}

class Members {
  String id;
  local.GPUser user;
  DateTime firstPostTimeUser;
  DateTime lastPostTimeUser;
  int totalPostsUser;
  int totalCommentsUser;
  int totalReactionsUser;
  int totalMediaUser;
  DateTime firstPostTimeGroup;
  DateTime lastPostTimeGroup;
  Groups group;
  int totalPostsUseronGroup;
  int totalCommentsGroup;
  int totalReactionsGroup;
  int totalMediaGroup;

  Members({
    required this.id,
    required this.user,
    required this.firstPostTimeUser,
    required this.lastPostTimeUser,
    required this.totalPostsUser,
    required this.totalCommentsUser,
    required this.totalReactionsUser,
    required this.totalMediaUser,
    required this.firstPostTimeGroup,
    required this.lastPostTimeGroup,
    required this.group,
    required this.totalCommentsGroup,
    required this.totalReactionsGroup,
    required this.totalMediaGroup,
    required this.totalPostsUseronGroup,
  });

  String? get name => user.name;
  String? get nameGroup => group.name;
}

extension StringToInt on String {
  int? toInt() {
    return int.tryParse(this);
  }
}

String? extractTarget(String target) {
  if (target.startsWith("user:")) {
    return target.split("user:")[1];
  }
  if (target.startsWith("group:")) {
    return target.split("group:")[1];
  }
  return null;
}

String? extractUserId(String target) {
  if (target.startsWith("user:")) {
    return target.split("user:")[1];
  }
  return null;
}

Future<Map<String, local.GPUser>> readUserFromCSV() async {
  FilePickerResult? result = await FilePicker.platform.pickFiles(
    type: FileType.custom,
    allowedExtensions: ['csv'],
  );

  if (result == null) {
    print("No file selected.");
    return {};
  }

  String? filePath = result.files.single.path;
  if (filePath == null) {
    print("Invalid file path.");
    return {};
  }

  final file = File(filePath);

  if (!file.existsSync()) {
    print("File not found: $filePath");
    return {};
  }

  String content = await file.readAsString(encoding: utf8);

  List<List<dynamic>> rows = const CsvToListConverter(
    fieldDelimiter: ';',
    textDelimiter: "\'",
    eol: '\n',
  ).convert(content);

  Map<String, local.GPUser> groupsUser = {};

  for (int i = 1; i < rows.length; i++) {
    var values = rows[i];

    if (values.length < 7) {
      print("Row does not have enough columns: ${values.join(';')}");
      continue;
    }

    try {
      int id = int.tryParse(values[3].toString().replaceAll("'", "")) ?? 0;
      String name = values[0].toString().replaceAll("'", "");

      local.GPUser user = local.GPUser(
        id: id,
        name: name,
      );

      groupsUser[id.toString()] = user;
    } catch (e) {
      print("Error processing row: ${values.join(';')} - Error: $e");
    }
  }

  return groupsUser;
}

Future<Map<String, Members>> readAndMergeCSVFilesUser(
    Map<String, local.GPUser> groupsUser) async {
  Map<String, Members> userPosts = {};
  Map<String, Groups> groupPosts = {};
  List<GPPost> mergedPosts = [];
  FilePickerResult? result = await FilePicker.platform.pickFiles(
    allowMultiple: true,
    type: FileType.custom,
    allowedExtensions: ['csv'],
  );

  if (result != null) {
    List<File> files =
        result.paths.whereType<String>().map((path) => File(path)).toList();
    for (var file in files) {
      try {
        String content = await file.readAsString(encoding: utf8);

        List<List<dynamic>> rows = const CsvToListConverter(
          fieldDelimiter: ';',
          textDelimiter: '"',
          eol: '\n',
        ).convert(content);

        for (int i = 1; i < rows.length; i++) {
          var values = rows[i];

          if (values.length < 11) {
            print("Row does not have enough columns: ${values.join(',')}");
            continue;
          }

          print("Processing row: ${values.join(',')}");

          String? userId = values[0]?.toString();
          String? content = values[1]?.toString();
          int? contentRtf = int.tryParse(values[2]?.toString() ?? '');
          List<GPPostMedia>? media =
              parseMediaAfter(values[3]?.toString() ?? '');
          List<GPPostMention>? mention =
              parseMentionAfter(values[4]?.toString() ?? '');
          GPPostPrivacy? privacy =
              parsePrivacyUser(values[5]?.toString() ?? '');
          String? target = values[6]?.toString();
          List<GPComment>? comments =
              parseCommentsAfter(values[7]?.toString() ?? '');
          List<local.GPUser>? seen =
              parseSeenAfter(values[8]?.toString() ?? '');
          List<GPReaction>? reactions =
              parseReactionsAfter(values[9]?.toString() ?? '');
          String? createdAtStr = values[10]?.toString();

          DateTime? createdAt;
          try {
            createdAt =
                DateFormat("yyyy-MM-ddTHH:mm:ss").parse(createdAtStr ?? '');
          } catch (e) {
            print(
                "Invalid date format for createdAt in row: ${values.join(',')}");
            continue;
          }

          String? UserID = extractTarget(target ?? '');

          if (UserID == null || UserID.isEmpty) {
            print("Invalid User ID in row: ${values.join(',')}");
            continue;
          }

          if (!groupsUser.containsKey(UserID)) {
            groupsUser[UserID] = local.GPUser(
              id: UserID.toInt(),
              name: '',
            );
          }

          if (userId != null && userId.isNotEmpty) {
            GPPost post = GPPost(
              userId: userId,
              content: content,
              contentRtf: contentRtf,
              media: media ?? [],
              mention: mention ?? [],
              privacy: privacy ?? GPPostPrivacy.work,
              target: target,
              comments: comments ?? [],
              seen: seen ?? [],
              reactions: reactions ?? [],
              createdAt: createdAt.toString(),
            );
            mergedPosts.add(post);

            if (!userPosts.containsKey(UserID)) {
              if (!groupPosts.containsKey(UserID)) {
                groupPosts[UserID] =
                    Groups(id: UserID, name: '', totalmembers: 0);
              }
              userPosts[UserID] = Members(
                id: UserID,
                firstPostTimeUser: createdAt,
                lastPostTimeUser: createdAt,
                user: groupsUser[UserID]!,
                totalPostsUser: 0,
                totalCommentsUser: 0,
                totalReactionsUser: 0,
                totalMediaUser: 0,
                group: groupPosts[UserID]!,
                totalPostsUseronGroup: 0,
                firstPostTimeGroup: createdAt,
                lastPostTimeGroup: createdAt,
                totalCommentsGroup: 0,
                totalReactionsGroup: 0,
                totalMediaGroup: 0,
              );
            }

            Members summaryUser = userPosts[UserID]!;

            summaryUser.totalPostsUser++;

            if (createdAt.isBefore(summaryUser.firstPostTimeUser)) {
              summaryUser.firstPostTimeUser = createdAt;
            }
            if (createdAt.isAfter(summaryUser.lastPostTimeUser) &&
                createdAt.isBefore(DateTime.now())) {
              summaryUser.lastPostTimeUser = createdAt;
            }

            summaryUser.totalCommentsUser += countCommentsAndRep(comments);

            summaryUser.totalReactionsUser += reactions?.length ?? 0;

            summaryUser.totalMediaUser += media?.length ?? 0;
          } else {
            print("Invalid user ID in row: ${values.join(',')}");
          }
        }
      } catch (e) {
        print("Error reading file: ${file.path}");
        print(e);
      }
    }
  }

  return userPosts;
}

Future<void> exportMembersSummaryToCSV(Map<String, Members> userPosts) async {
  final directory = await getApplicationDocumentsDirectory();
  final filePath = '${directory.path}/members_summary_test.csv';
  final file = File(filePath);

  StringBuffer csvContent = StringBuffer();
  csvContent.writeln(
      'STT;Tên user;"Thời gian Bài đăng đầu tiên";"Thời gian Bài đăng cuối cùng";"Tổng số bài viết";"Tổng số bình luận";"Tổng số lượt thả cảm xúc";"Tổng số media"');

  int ordinal = 1;
  final dateFormat = DateFormat("MM/dd/yyyy HH:mm");

  userPosts.forEach((UserID, summaryUser) {
    String firstPostTime = summaryUser.firstPostTimeUser != null
        ? dateFormat.format(summaryUser.firstPostTimeUser!)
        : '';

    String lastPostTime = summaryUser.lastPostTimeUser != null
        ? dateFormat.format(summaryUser.lastPostTimeUser!)
        : '';

    csvContent.writeln(
        '$ordinal;${summaryUser.name};$firstPostTime;$lastPostTime;${(summaryUser.totalPostsUser)};${(summaryUser.totalCommentsUser)};${(summaryUser.totalReactionsUser)};${(summaryUser.totalMediaUser)}');
    ordinal++;
  });

  await file.writeAsString(csvContent.toString());

  print('User summary data exported to CSV file at: $filePath');
}

int countCommentaAndRep(List<GPComment> comments) {
  int count = comments.length;
  for (var comment in comments ?? []) {
    if (comment.replies?.length ?? 0 > 0) {
      count += countCommentaAndRep(comment.replies!);
    }
  }
  return count;
}

int countCommentsAndRep(List<GPComment>? comments) {
  if (comments == null || comments.isEmpty) {
    return 0;
  }

  int count = comments.length;
  for (var comment in comments) {
    if (comment.replies != null) {
      count += countCommentsAndRep(comment.replies);
    }
  }
  return count;
}

Future<Map<String, Members>> readAndMergeCSVFilesUserAll(
    Map<String, local.GPUser> groupsUser, Map<String, Groups> groupsMap) async {
  Map<String, Members> userPosts = {};
  Map<String, GroupSummaryNew> groupPosts = {};
  List<GPPost> mergedPosts = [];
  FilePickerResult? result = await FilePicker.platform.pickFiles(
    allowMultiple: true,
    type: FileType.custom,
    allowedExtensions: ['csv'],
  );

  if (result != null) {
    List<File> files =
        result.paths.whereType<String>().map((path) => File(path)).toList();
    for (var file in files) {
      try {
        String content = await file.readAsString(encoding: utf8);

        List<List<dynamic>> rows = const CsvToListConverter(
          fieldDelimiter: ';',
          textDelimiter: '"',
          eol: '\n',
        ).convert(content);

        for (int i = 1; i < rows.length; i++) {
          var values = rows[i];

          if (values.length < 11) {
            print("Row does not have enough columns: ${values.join(',')}");
            continue;
          }

          print("Processing row: ${values.join(',')}");

          String? userId = values[0]?.toString();
          String? content = values[1]?.toString();
          int? contentRtf = int.tryParse(values[2]?.toString() ?? '');
          List<GPPostMedia>? media =
              parseMediaAfter(values[3]?.toString() ?? '');
          List<GPPostMention>? mention =
              parseMentionAfter(values[4]?.toString() ?? '');
          GPPostPrivacy? privacy = parsePrivacyAll(values[5]?.toString() ?? '');
          String? target = values[6]?.toString();
          List<GPComment>? comments =
              parseCommentsAfter(values[7]?.toString() ?? '');
          List<local.GPUser>? seen =
              parseSeenAfter(values[8]?.toString() ?? '');
          List<GPReaction>? reactions =
              parseReactionsAfter(values[9]?.toString() ?? '');
          String? createdAtStr = values[10]?.toString();

          DateTime? createdAt;
          try {
            createdAt =
                DateFormat("yyyy-MM-ddTHH:mm:ss").parse(createdAtStr ?? '');
          } catch (e) {
            print(
                "Invalid date format for createdAt in row: ${values.join(',')}");
            continue;
          }
          String? groupId = extractTarget(target ?? '');

          if ((userId == null || userId.isEmpty) &&
              (groupId == null || groupId.isEmpty)) {
            print(
                "Invalid Target (both User ID and Group ID are missing) in row: ${values.join(',')}");
            continue;
          }

          if (groupId != null && groupId.isNotEmpty) {
            if (!groupsMap.containsKey(groupId)) {
              groupsMap[groupId] = Groups(
                id: groupId,
                name: '',
                totalmembers: 0,
              );
            }
          }
          if (userId != null && userId.isNotEmpty) {
            if (!groupsUser.containsKey(userId)) {
              groupsUser[userId] = local.GPUser(
                id: userId.toInt(),
                name: '',
              );
            }

            GPPost post = GPPost(
              userId: userId,
              content: content,
              contentRtf: contentRtf,
              media: media ?? [],
              mention: mention ?? [],
              privacy: privacy,
              target: target,
              comments: comments ?? [],
              seen: seen ?? [],
              reactions: reactions ?? [],
              createdAt: createdAt.toString(),
            );
            mergedPosts.add(post);

            if (!userPosts.containsKey(userId)) {
              userPosts[userId] = Members(
                id: userId,
                firstPostTimeUser: createdAt,
                lastPostTimeUser: createdAt,
                user: groupsUser[userId]!,
                totalPostsUser: 0,
                totalCommentsUser: 0,
                totalReactionsUser: 0,
                totalMediaUser: 0,
                group: groupId != null
                    ? groupsMap[groupId]!
                    : Groups(id: '', name: '', totalmembers: 0),
                totalPostsUseronGroup: 0,
                firstPostTimeGroup: createdAt,
                lastPostTimeGroup: createdAt,
                totalCommentsGroup: 0,
                totalReactionsGroup: 0,
                totalMediaGroup: 0,
              );
            }

            Members summaryAll = userPosts[userId]!;

            if (post.privacy == GPPostPrivacy.work) {
              summaryAll.totalPostsUser++;
              if (createdAt.isBefore(summaryAll.firstPostTimeUser)) {
                summaryAll.firstPostTimeUser = createdAt;
              }
              if (createdAt.isAfter(summaryAll.lastPostTimeUser)) {
                summaryAll.lastPostTimeUser = createdAt;
              }
              summaryAll.totalCommentsUser += countCommentsAndRep(comments);
              summaryAll.totalReactionsUser += reactions?.length ?? 0;
              summaryAll.totalMediaUser += media?.length ?? 0;
            } else if (post.privacy == GPPostPrivacy.group) {
              summaryAll.totalPostsUseronGroup++;
              if (summaryAll.firstPostTimeGroup == null ||
                  createdAt.isBefore(summaryAll.firstPostTimeGroup!)) {
                summaryAll.firstPostTimeGroup = createdAt;
              }
              if (summaryAll.lastPostTimeGroup == null ||
                  createdAt.isBefore(summaryAll.firstPostTimeGroup!)) {
                summaryAll.lastPostTimeGroup = createdAt;
              }
              summaryAll.totalCommentsGroup += countCommentsAndRep(comments);
              summaryAll.totalReactionsGroup += reactions?.length ?? 0;
              summaryAll.totalMediaGroup += media?.length ?? 0;
            }
          } else {
            print("Invalid user ID in row: ${values.join(',')}");
          }
        }
      } catch (e) {
        print("Error reading file: ${file.path}");
        print(e);
      }
    }
  }

  return userPosts;
}

Future<void> exportMembersAllSummaryToCSV(
    Map<String, Members> userPosts, Map<String, Groups> groupsMap) async {
  final directory = await getApplicationDocumentsDirectory();
  final filePath = '${directory.path}/all_members_summary_test.csv';
  final file = File(filePath);

  StringBuffer csvContent = StringBuffer();
  csvContent.writeln(
    [
      'STT',
      'Tên user',
      'Thời gian Bài đăng đầu tiên trên trang cá nhân',
      'Thời gian Bài đăng cuối cùng trên trang cá nhân',
      'Tổng số bài viết trên trang cá nhân',
      'Tổng số bình luận trên trang cá nhân',
      'Tổng số lượt thả cảm xúc trên trang cá nhân',
      'Tổng số media trên trang cá nhân',
      'Thời gian Bài đăng đầu tiên trên trang group',
      'Thời gian Bài đăng cuối cùng trong group',
      'Tổng số bài viết trong group',
      'Tổng số bình luận trong group',
      'Tổng số lượt thả cảm xúc trong group',
      'Tổng số media trong group'
    ].join(';'),
  );

  int ordinal = 1;

  userPosts.forEach((UserID, summaryAll) {
    DateTime? firstPostTimeGroup = summaryAll.firstPostTimeGroup;
    DateTime? lastPostTimeGroup = summaryAll.lastPostTimeGroup;

    if (summaryAll.totalPostsUseronGroup > 0) {
      if (firstPostTimeGroup != null) {
        var firstPostGroup = groupsMap.values.firstWhere(
          (group) => group.id == summaryAll.group.id,
          orElse: () => Groups(id: '', name: 'Unknown Group', totalmembers: 0),
        );
      }
    } else {
      firstPostTimeGroup = null;
      lastPostTimeGroup = null;
    }

    csvContent.writeln(
      [
        ordinal,
        summaryAll.name,
        summaryAll.firstPostTimeUser,
        summaryAll.lastPostTimeUser,
        summaryAll.totalPostsUser,
        summaryAll.totalCommentsUser,
        summaryAll.totalReactionsUser,
        summaryAll.totalMediaUser,
        firstPostTimeGroup,
        lastPostTimeGroup,
        summaryAll.totalPostsUseronGroup,
        summaryAll.totalCommentsGroup,
        summaryAll.totalReactionsGroup,
        summaryAll.totalMediaGroup,
      ].join(';'),
    );
    ordinal++;
  });

  await file.writeAsString(csvContent.toString());

  print('User summary data exported to CSV file at: $filePath');
}
