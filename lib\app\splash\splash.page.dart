import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_core/core.dart' hide Assets;
import 'package:gp_fbwp_crawler/app/app_config/app_config.dart';
import 'package:gp_fbwp_crawler/flutter_gen/assets.gen.dart';
import 'package:gp_fbwp_crawler/route/go_router.route.dart';

import '../../l10n/app_localizations.dart';
import '../features/home/<USER>';

class GPSplashScreen extends StatelessWidget {
  const GPSplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    startDateTime = DateTime.now();
    l10n = S.of(context)!;

    return BlocListener<AppConfigBloc, AppConfigState>(
      listener: (context, state) {
        if (!state.isLoading) {
          Future.delayed(const Duration(seconds: 1))
              .then((value) => const HomeRouteData().go(context));
        }
      },
      child: Container(
        color: GPColor.bgPrimary,
        width: double.infinity,
        height: double.infinity,
        child: Center(
          child: Assets.images.splashLoading1.lottie(),
        ),
      ),
    );
  }
}
