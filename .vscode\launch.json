{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Debug Staging",
            "request": "launch",
            "type": "dart",
            "program": "lib/main.staging.dart",
        },
        {
            "name": "Debug UAT",
            "request": "launch",
            "type": "dart",
            "program": "lib/main.uat.dart",
        },
        {
            "name": "Debug PRODUCTION",
            "request": "launch",
            "type": "dart",
            "program": "lib/main.production.dart",
        },
        {
            "name": "PROFILING on STAGING",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile",
            "program": "lib/main.staging.dart",
        },
        {
            "name": "RUN RELEASE PRODUCTION",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            "program": "lib/main.production.dart",
        },
        {
            "name": "Command: Generate l10n",
            "command": "flutter gen-l10n",
            "request": "launch",
            "type": "node-terminal",
            "presentation": {
                "reveal": "silent",
                "close": true,
            }
        },
        {
            "name": "Command: Generate data, domain",
            "command": "dart run build_runner build --delete-conflicting-outputs",
            "request": "launch",
            "type": "node-terminal",
            "presentation": {
                "reveal": "silent",
                "close": true,
            }
        },
        {
            "name": "Command: Update AppInfo (dev)",
            "command": "dart run package_rename_plus -p project_configs/package_rename_config-dev.yaml",
            "request": "launch",
            "type": "node-terminal",
            "presentation": {
                "reveal": "silent",
                "close": true,
            }
        },
        {
            "name": "Command: Update App LauncherIcon(dev)",
            "command": "dart run flutter_launcher_icons -f project_configs/flutter_launcher_icons-dev.yaml",
            "request": "launch",
            "type": "node-terminal",
            "presentation": {
                "reveal": "silent",
                "close": true,
            }
        },
        {
            "name": "Command: Generate assets",
            "command": "fluttergen",
            "request": "launch",
            "type": "node-terminal",
            "presentation": {
                "reveal": "silent",
                "close": true,
            }
        },
    ]
}