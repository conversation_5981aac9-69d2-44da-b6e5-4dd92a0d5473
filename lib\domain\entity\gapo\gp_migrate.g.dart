// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'gp_migrate.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GPMigrate _$GPMigrateFromJson(Map<String, dynamic> json) => GPMigrate(
      ordinal: (json['ordinal'] as num?)?.toInt(),
      groupName: (json['group_name'] as List<dynamic>?)
          ?.map((e) => GPGroupOne.fromJson(e as Map<String, dynamic>))
          .toList(),
      firstPostTime: json['first_post_time'] as String?,
      lastPostTime: json['last_post_time'] as String?,
      totalPeople: (json['total_people'] as num?)?.toInt(),
      totalPosts: (json['total_posts'] as num?)?.toInt(),
      totalComments: (json['total_comments'] as num?)?.toInt(),
      totalReactions: (json['total_reactions'] as num?)?.toInt(),
      totalMedia: (json['total_media'] as num?)?.toInt(),
      totalViews: (json['total_views'] as num?)?.toInt(),
    );

Map<String, dynamic> _$GPMigrateToJson(GPMigrate instance) => <String, dynamic>{
      'ordinal': instance.ordinal,
      'group_name': instance.groupName?.map((e) => e.toJson()).toList(),
      'first_post_time': instance.firstPostTime,
      'last_post_time': instance.lastPostTime,
      'total_people': instance.totalPeople,
      'total_posts': instance.totalPosts,
      'total_comments': instance.totalComments,
      'total_reactions': instance.totalReactions,
      'total_media': instance.totalMedia,
      'total_views': instance.totalViews,
    };

GPGroupOne _$GPGroupOneFromJson(Map<String, dynamic> json) => GPGroupOne(
      name: json['name'] as String?,
      description: json['description'] as String?,
      privacy: $enumDecodeNullable(_$GPGroupPrivacyEnumMap, json['privacy']),
      discoverability: $enumDecodeNullable(
          _$GPGroupDiscoverabilityEnumMap, json['discoverability']),
      wpGroupId: json['wp_group_id'] as String?,
      cover: json['cover'] as String?,
      createdAt: json['created_at'] as String?,
      previewMembers: (json['preview_members'] as List<dynamic>?)
          ?.map((e) => GPUser.fromJson(e as Map<String, dynamic>))
          .toList(),
      owner: json['owner'] == null
          ? null
          : GPUser.fromJson(json['owner'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$GPGroupOneToJson(GPGroupOne instance) =>
    <String, dynamic>{
      'wp_group_id': instance.wpGroupId,
      'name': instance.name,
      'description': instance.description,
      'cover': instance.cover,
      'privacy': _$GPGroupPrivacyEnumMap[instance.privacy],
      'discoverability':
          _$GPGroupDiscoverabilityEnumMap[instance.discoverability],
      'created_at': instance.createdAt,
      'preview_members':
          GPGroupOne._previewMembersToJson(instance.previewMembers),
      'owner': GPGroupOne._ownerToJson(instance.owner),
    };

const _$GPGroupPrivacyEnumMap = {
  GPGroupPrivacy.closed: 'CLOSED',
  GPGroupPrivacy.public: 'PUBLIC',
};

const _$GPGroupDiscoverabilityEnumMap = {
  GPGroupDiscoverability.hidden: 'HIDDEN',
  GPGroupDiscoverability.visible: 'VISIBLE',
};
