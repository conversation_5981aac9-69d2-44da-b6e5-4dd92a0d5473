# Summary

Date : 2024-06-21 19:22:39

Directory /Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler

Total : 311 files,  34417 codes, 1560 comments, 4206 blanks, all 40183 lines

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| Dart | 256 | 32,715 | 1,364 | 3,938 | 38,017 |
| C++ | 12 | 515 | 103 | 160 | 778 |
| XML | 9 | 469 | 44 | 9 | 522 |
| JSON | 11 | 274 | 0 | 3 | 277 |
| YAML | 9 | 214 | 24 | 22 | 260 |
| Groovy | 3 | 94 | 5 | 22 | 121 |
| Swift | 5 | 46 | 4 | 16 | 66 |
| Markdown | 2 | 43 | 0 | 14 | 57 |
| HTML | 1 | 35 | 16 | 17 | 68 |
| Java Properties | 2 | 8 | 0 | 2 | 10 |
| Kotlin | 1 | 4 | 0 | 3 | 7 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 311 | 34,417 | 1,560 | 4,206 | 40,183 |
| . (Files) | 4 | 122 | 24 | 16 | 162 |
| android | 12 | 171 | 47 | 33 | 251 |
| android (Files) | 3 | 46 | 0 | 11 | 57 |
| android/app | 8 | 120 | 47 | 21 | 188 |
| android/app (Files) | 1 | 51 | 5 | 12 | 68 |
| android/app/src | 7 | 69 | 42 | 9 | 120 |
| android/app/src/debug | 1 | 7 | 4 | 0 | 11 |
| android/app/src/main | 6 | 62 | 38 | 9 | 109 |
| android/app/src/main (Files) | 1 | 32 | 6 | 0 | 38 |
| android/app/src/main/kotlin | 1 | 4 | 0 | 3 | 7 |
| android/app/src/main/kotlin/vn | 1 | 4 | 0 | 3 | 7 |
| android/app/src/main/kotlin/vn/gapowork | 1 | 4 | 0 | 3 | 7 |
| android/app/src/main/kotlin/vn/gapowork/crawler | 1 | 4 | 0 | 3 | 7 |
| android/app/src/main/res | 4 | 26 | 32 | 6 | 64 |
| android/app/src/main/res/drawable | 1 | 4 | 7 | 2 | 13 |
| android/app/src/main/res/drawable-v21 | 1 | 4 | 7 | 2 | 13 |
| android/app/src/main/res/values | 1 | 9 | 9 | 1 | 19 |
| android/app/src/main/res/values-night | 1 | 9 | 9 | 1 | 19 |
| android/gradle | 1 | 5 | 0 | 1 | 6 |
| android/gradle/wrapper | 1 | 5 | 0 | 1 | 6 |
| assets | 2 | 2 | 0 | 0 | 2 |
| assets/images | 2 | 2 | 0 | 0 | 2 |
| ios | 11 | 232 | 4 | 13 | 249 |
| ios/Runner | 10 | 225 | 2 | 9 | 236 |
| ios/Runner (Files) | 2 | 13 | 0 | 3 | 16 |
| ios/Runner/Assets.xcassets | 6 | 151 | 0 | 4 | 155 |
| ios/Runner/Assets.xcassets/AppIcon-dev.appiconset | 1 | 1 | 0 | 0 | 1 |
| ios/Runner/Assets.xcassets/AppIcon-prod.appiconset | 1 | 1 | 0 | 0 | 1 |
| ios/Runner/Assets.xcassets/AppIcon-uat.appiconset | 1 | 1 | 0 | 0 | 1 |
| ios/Runner/Assets.xcassets/AppIcon.appiconset | 1 | 122 | 0 | 1 | 123 |
| ios/Runner/Assets.xcassets/LaunchImage.imageset | 2 | 26 | 0 | 3 | 29 |
| ios/Runner/Base.lproj | 2 | 61 | 2 | 2 | 65 |
| ios/RunnerTests | 1 | 7 | 2 | 4 | 13 |
| lib | 257 | 32,722 | 1,354 | 3,932 | 38,008 |
| lib (Files) | 3 | 24 | 0 | 9 | 33 |
| lib/app | 45 | 2,398 | 156 | 412 | 2,966 |
| lib/app (Files) | 4 | 346 | 6 | 55 | 407 |
| lib/app/app_config | 8 | 247 | 17 | 52 | 316 |
| lib/app/app_config (Files) | 1 | 2 | 0 | 1 | 3 |
| lib/app/app_config/bloc | 5 | 236 | 17 | 47 | 300 |
| lib/app/app_config/widgets | 2 | 9 | 0 | 4 | 13 |
| lib/app/base | 7 | 256 | 46 | 73 | 375 |
| lib/app/base (Files) | 1 | 1 | 0 | 1 | 2 |
| lib/app/base/networking | 6 | 255 | 46 | 72 | 373 |
| lib/app/base/networking (Files) | 3 | 107 | 7 | 27 | 141 |
| lib/app/base/networking/gapo | 3 | 148 | 39 | 45 | 232 |
| lib/app/constant | 4 | 40 | 24 | 13 | 77 |
| lib/app/features | 20 | 1,480 | 63 | 215 | 1,758 |
| lib/app/features (Files) | 1 | 1 | 0 | 1 | 2 |
| lib/app/features/crawler | 12 | 1,230 | 46 | 174 | 1,450 |
| lib/app/features/crawler (Files) | 3 | 95 | 0 | 10 | 105 |
| lib/app/features/crawler/bloc | 5 | 1,133 | 46 | 158 | 1,337 |
| lib/app/features/crawler/sync | 2 | 1 | 0 | 3 | 4 |
| lib/app/features/crawler/unsync | 2 | 1 | 0 | 3 | 4 |
| lib/app/features/home | 7 | 249 | 17 | 40 | 306 |
| lib/app/features/home (Files) | 2 | 100 | 2 | 6 | 108 |
| lib/app/features/home/<USER>
| lib/app/splash | 2 | 29 | 0 | 4 | 33 |
| lib/config | 3 | 49 | 0 | 17 | 66 |
| lib/data | 77 | 2,680 | 232 | 494 | 3,406 |
| lib/data (Files) | 1 | 3 | 0 | 1 | 4 |
| lib/data/data_source | 15 | 910 | 74 | 121 | 1,105 |
| lib/data/data_source (Files) | 1 | 2 | 0 | 1 | 3 |
| lib/data/data_source/local | 2 | 46 | 9 | 14 | 69 |
| lib/data/data_source/remote | 12 | 862 | 65 | 106 | 1,033 |
| lib/data/data_source/remote (Files) | 5 | 466 | 30 | 54 | 550 |
| lib/data/data_source/remote/download | 2 | 33 | 0 | 5 | 38 |
| lib/data/data_source/remote/gapo | 5 | 363 | 35 | 47 | 445 |
| lib/data/model | 56 | 1,594 | 138 | 339 | 2,071 |
| lib/data/model (Files) | 2 | 16 | 0 | 4 | 20 |
| lib/data/model/download | 3 | 132 | 15 | 26 | 173 |
| lib/data/model/facebook | 3 | 25 | 4 | 10 | 39 |
| lib/data/model/gpw | 17 | 602 | 70 | 108 | 780 |
| lib/data/model/gpw (Files) | 4 | 44 | 4 | 17 | 65 |
| lib/data/model/gpw/auth | 13 | 558 | 66 | 91 | 715 |
| lib/data/model/gpw/auth (Files) | 1 | 2 | 0 | 1 | 3 |
| lib/data/model/gpw/auth/request | 9 | 522 | 53 | 76 | 651 |
| lib/data/model/gpw/auth/response | 3 | 34 | 13 | 14 | 61 |
| lib/data/model/workplace | 31 | 819 | 49 | 191 | 1,059 |
| lib/data/model/workplace (Files) | 1 | 6 | 0 | 1 | 7 |
| lib/data/model/workplace/base | 8 | 176 | 15 | 45 | 236 |
| lib/data/model/workplace/comment | 5 | 93 | 8 | 25 | 126 |
| lib/data/model/workplace/conversation | 5 | 167 | 8 | 38 | 213 |
| lib/data/model/workplace/enums | 2 | 49 | 0 | 17 | 66 |
| lib/data/model/workplace/group | 5 | 155 | 10 | 31 | 196 |
| lib/data/model/workplace/post | 5 | 173 | 8 | 34 | 215 |
| lib/data/repository | 5 | 173 | 20 | 33 | 226 |
| lib/di | 11 | 517 | 78 | 63 | 658 |
| lib/di (Files) | 1 | 2 | 0 | 1 | 3 |
| lib/di/component | 3 | 354 | 17 | 18 | 389 |
| lib/di/modules | 7 | 161 | 61 | 44 | 266 |
| lib/domain | 94 | 24,475 | 524 | 2,603 | 27,602 |
| lib/domain (Files) | 1 | 3 | 0 | 1 | 4 |
| lib/domain/entity | 68 | 23,788 | 387 | 2,424 | 26,599 |
| lib/domain/entity (Files) | 1 | 4 | 0 | 1 | 5 |
| lib/domain/entity/base | 12 | 2,883 | 60 | 294 | 3,237 |
| lib/domain/entity/base (Files) | 2 | 100 | 10 | 18 | 128 |
| lib/domain/entity/base/app | 4 | 741 | 15 | 87 | 843 |
| lib/domain/entity/base/log | 3 | 1,035 | 15 | 97 | 1,147 |
| lib/domain/entity/base/status | 3 | 1,007 | 20 | 92 | 1,119 |
| lib/domain/entity/enums | 6 | 132 | 47 | 39 | 218 |
| lib/domain/entity/enums (Files) | 4 | 119 | 47 | 33 | 199 |
| lib/domain/entity/enums/upload | 2 | 13 | 0 | 6 | 19 |
| lib/domain/entity/gapo | 25 | 1,061 | 102 | 201 | 1,364 |
| lib/domain/entity/gapo (Files) | 15 | 631 | 24 | 103 | 758 |
| lib/domain/entity/gapo/upload | 10 | 430 | 78 | 98 | 606 |
| lib/domain/entity/gapo/upload (Files) | 3 | 199 | 41 | 45 | 285 |
| lib/domain/entity/gapo/upload/callback | 4 | 188 | 35 | 42 | 265 |
| lib/domain/entity/gapo/upload/params | 3 | 43 | 2 | 11 | 56 |
| lib/domain/entity/workplace | 24 | 19,708 | 178 | 1,889 | 21,775 |
| lib/domain/entity/workplace (Files) | 1 | 5 | 0 | 1 | 6 |
| lib/domain/entity/workplace/feed | 7 | 3,134 | 46 | 332 | 3,512 |
| lib/domain/entity/workplace/group | 3 | 2,628 | 20 | 256 | 2,904 |
| lib/domain/entity/workplace/other | 5 | 5,723 | 52 | 503 | 6,278 |
| lib/domain/entity/workplace/thread | 5 | 5,946 | 42 | 566 | 6,554 |
| lib/domain/entity/workplace/user | 3 | 2,272 | 18 | 231 | 2,521 |
| lib/domain/repository | 5 | 49 | 22 | 22 | 93 |
| lib/domain/usecase | 20 | 635 | 115 | 156 | 906 |
| lib/domain/usecase (Files) | 14 | 435 | 77 | 103 | 615 |
| lib/domain/usecase/gapo | 6 | 200 | 38 | 53 | 291 |
| lib/flutter_gen | 2 | 69 | 10 | 15 | 94 |
| lib/helpers | 5 | 273 | 49 | 66 | 388 |
| lib/l10n | 6 | 100 | 85 | 34 | 219 |
| lib/mapper | 8 | 2,042 | 206 | 183 | 2,431 |
| lib/mapper (Files) | 3 | 256 | 73 | 39 | 368 |
| lib/mapper/entity | 5 | 1,786 | 133 | 144 | 2,063 |
| lib/route | 3 | 95 | 14 | 36 | 145 |
| linux | 3 | 86 | 18 | 27 | 131 |
| macos | 5 | 438 | 2 | 11 | 451 |
| macos/Runner | 4 | 431 | 0 | 7 | 438 |
| macos/Runner (Files) | 2 | 20 | 0 | 6 | 26 |
| macos/Runner/Assets.xcassets | 1 | 68 | 0 | 0 | 68 |
| macos/Runner/Assets.xcassets/AppIcon.appiconset | 1 | 68 | 0 | 0 | 68 |
| macos/Runner/Base.lproj | 1 | 343 | 0 | 1 | 344 |
| macos/RunnerTests | 1 | 7 | 2 | 4 | 13 |
| project_configs | 6 | 132 | 0 | 18 | 150 |
| test | 1 | 14 | 10 | 6 | 30 |
| web | 2 | 70 | 16 | 18 | 104 |
| windows | 8 | 428 | 85 | 132 | 645 |
| windows/runner | 8 | 428 | 85 | 132 | 645 |

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)