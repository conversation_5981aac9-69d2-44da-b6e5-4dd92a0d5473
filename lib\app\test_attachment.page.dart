import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core_v2/base/base.dart';
import 'package:gp_fbwp_crawler/config/app_configs.dart';
import 'package:gp_fbwp_crawler/config/bootstrap.dart';
import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:gp_fbwp_crawler/helpers/jwt_token_decode.dart';

const _kDebugTag = 'DEBUG';

void main(List<String> args) async {
  await initApp(AppConfigStaging());

  WidgetsFlutterBinding.ensureInitialized();
  await GetStorage.init();

  runApp(
    MaterialApp(
      home: Scaffold(
        appBar: AppBar(),
        body: Center(
          child: FutureBuilder(
            future: _initial(),
            builder: (context, snapshot) {
              if (snapshot.hasData && snapshot.data != null) {
                // final GPUploadUseCase uploadUseCase = snapshot.data!;

                // return FutureBuilder(
                //   future: _testUploadFiles(uploadUseCase),
                //   builder: (context, snapshot) {
                //     return StreamBuilder<GPUploadProgressEntity>(
                //       stream: uploadUseCase.dashboardStream,
                //       builder: (index, snapshot) {
                //         if (snapshot.hasData) {
                //           return Column(
                //             children: [
                //               Text(
                //                   "Current progress ${snapshot.data?.progress}"),
                //               const SizedBox(height: 20),
                //               CircularProgressIndicator(
                //                 value: snapshot.data?.progress,
                //               )
                //             ],
                //           );
                //         }

                //         return const Text('No data from stream');
                //       },
                //     );
                //   },
                // );
              }

              return const Center(child: CircularProgressIndicator());
            },
          ),
        ),
      ),
    ),
  );
}

Future<GPUploadUseCase?> _initial() async {
  final authResponse = await _login();
  if (authResponse != null) {
    await _saveTokenInfo(authResponse);

    return GetIt.I<GPUploadUseCase>();
  }

  return null;
}

Future<AuthResponse?> _login() async {
  final GPAuthUseCase authUseCase = GetIt.I<GPAuthUseCase>();
  final ApiResponseV2<AuthResponse> authResponse = await authUseCase.execute(
    GPAuthInput(
      params: AuthParams(
        clientId: "6n6rwo86qmx7u8aahgrq",
        deviceModel: "Simulator iPhone 11",
        email: GetIt.I<String>(instanceName: 'kAdminEmail'),
        password: GetIt.I<String>(instanceName: 'kAdminPassword'),
      ),
    ),
  );

  if (!authResponse.data.hasData) {
    log('no Auth response!!!');
    return null;
  }

  return authResponse.data;
}

Future _saveTokenInfo(AuthResponse authResponse) async {
  if (!authResponse.hasData) return;

  final token = authResponse.accessToken;
  final jwtData = JwtDecoder.decode(authResponse.accessToken!);
  final wsId = jwtData['wsids'].toString().split(',').first;
  final tokenInfo = TokenInfo.fromJson({
    "userId": authResponse.userId.toString(),
    "displayName": 'Admin',
    "avatar": '',
    "accessToken": token,
    "refreshToken": authResponse.refreshToken,
    "workspaceId": wsId,
    "language": Constants.language(),
    "environment": Constants.environment().name,
  });

  Constants.updateTokenInfo(tokenInfo);
  await TokenManager.saveTokenInfo(tokenInfo);
}

Future _testUploadFiles(GPUploadUseCase useCase) async {
  final directory = await getApplicationDocumentsDirectory();

  final String filePath = '${directory.path}/test.txt';
  final String videoPath = '${directory.path}/file_example_MP4_640_3MG.mp4';
  // final String videoPath = '${directory.path}/GapoWork_Calendar_GG_Sync.mov';
  final String imagePath = '${directory.path}/IMG_20240426_104523_067.jpeg';

  final File file = File(filePath);
  final File videoFile = File(videoPath);
  final File imageFile = File(imagePath);

  assert(file.existsSync());
  assert(videoFile.existsSync());
  assert(imageFile.existsSync());

  final UploadFileResponseModelWrapper response = await useCase.execute(
    GPUploadUseCaseInput(
      uploadInput: GPUploadInput(
        fileInputs: [
          GPUploadFileInput(
            file: file,
            uploadType: GPApiUploadType.files,
          ),
          GPUploadFileInput(
            file: videoFile,
            uploadType: GPApiUploadType.video,
          ),
          GPUploadFileInput(
            file: imageFile,
            uploadType: GPApiUploadType.image,
          ),
        ],
        itemCallback: GPUploadCallback(
          onAFileStartUpload: (estimate, uploadFile) {
            logDebug('$_kDebugTag: onAFileStartUpload: ${uploadFile.path}');
          },
          onAFileUploadProgress: (progressModel, uploadFile) {
            logDebug('$_kDebugTag: onAFileUploadProgress: ${uploadFile.path}');
          },
          onAFileUploaded: (totalUploadedFiles, totalFiles, uploadResponse) {
            logDebug(
                '$_kDebugTag: onAFileUploaded: $totalUploadedFiles/$totalFiles, uploadResponse: ${uploadResponse.toJson()}');
          },
        ),
        dashBoardCallback: GPUploadDashBoardCallback(
          onFileStartUpload: (estimate, totalFiles) {
            logDebug(
                '$_kDebugTag: dashBoardCallback onFileStartUpload: $totalFiles');
          },
          onFileProgress: (progressModel) {
            logDebug(
                '$_kDebugTag: dashBoardCallback onFileProgress: ${progressModel.progress}');
          },
          onAFileUploaded: (totalUploadedFiles, totalFiles, uploadResponse) {
            logDebug(
                '$_kDebugTag: dashBoardCallback onAFileUploaded: $totalUploadedFiles/$totalFiles, ${uploadResponse.toJson()}');
          },
          onUploadProcessCompleted: () {
            logDebug('$_kDebugTag: dashBoardCallback onUploadProcessCompleted');
          },
        ),
      ),
    ),
  );

  log('DEBUG: DONE upload Files: UploadFileResponseModel -> ${response.uploadFileResponseModels.first.toJson()}');
}
