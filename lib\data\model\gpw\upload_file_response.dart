import 'package:gp_core_v2/base/usecase/model/base_output.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';

final class UploadFileResponseModelWrapper extends GPBaseOutput {
  const UploadFileResponseModelWrapper({
    required this.uploadFileResponseModels,
    required this.uploadImageResponseModels,
  });

  final List<GPUploadFileResponseModel> uploadFileResponseModels;
  final List<GPUploadImageResponseModel> uploadImageResponseModels;

  Map<String, dynamic> toJson() {
    return {
      "uploadFileResponseModels":
          uploadFileResponseModels.map((e) => e.toJson()).toList(),
      "uploadImageResponseModels":
          uploadImageResponseModels.map((e) => e.toJson()).toList(),
    };
  }
}
