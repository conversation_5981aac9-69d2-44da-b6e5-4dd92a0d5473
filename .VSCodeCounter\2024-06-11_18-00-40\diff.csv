"filename", "language", "YAML", "Dart", "comment", "blank", "total"
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/build.yaml", "YAML", 7, 0, 0, 0, 7
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/app.dart", "Dart", 0, 1, 0, 0, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/base/networking/gapo/authentication_interceptor.dart", "Dart", 0, 33, 0, 11, 44
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/base/networking/gapo/gapo.dart", "Dart", 0, 2, 0, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/base/networking/gapo/gp_token_interceptor.dart", "Dart", 0, 113, 39, 33, 185
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/base/networking/networking.dart", "Dart", 0, 1, 0, 0, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/constant/fb_wp_url.constants.dart", "Dart", 0, 1, 0, 0, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/constant/gapo_url.constants.dart", "Dart", 0, 7, 2, 1, 10
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/bloc/crawl_bloc.dart", "Dart", 0, 55, 6, 10, 71
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/crawl.main.page.dart", "Dart", 0, 4, 0, 2, 6
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/test_attachment.page.dart", "Dart", 0, 110, 0, 37, 147
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/auth.service.dart", "Dart", 0, -21, -9, -5, -35
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/auth.service.g.dart", "Dart", 0, -60, -5, -13, -78
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/gapo/auth.service.dart", "Dart", 0, 25, 9, 6, 40
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/gapo/auth.service.g.dart", "Dart", 0, 96, 5, 14, 115
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/gapo/gapo.dart", "Dart", 0, 2, 0, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/gapo/upload.service.dart", "Dart", 0, 43, 16, 9, 68
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/gapo/upload.service.g.dart", "Dart", 0, 164, 5, 15, 184
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/workplace.service.dart", "Dart", 0, 10, 0, 2, 12
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/workplace.service.g.dart", "Dart", 0, 53, 0, 2, 55
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/auth/auth.dart", "Dart", 0, 2, 0, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/auth/request/auth_check_email_request.dart", "Dart", 0, 16, 9, 8, 33
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/auth/request/auth_check_email_request.g.dart", "Dart", 0, 13, 4, 5, 22
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/auth/request/gp_auth_params.dart", "Dart", 0, 18, 1, 4, 23
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/auth/request/gp_auth_params.freezed.dart", "Dart", 0, 226, 15, 23, 264
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/auth/request/gp_auth_params.g.dart", "Dart", 0, 10, 4, 4, 18
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/auth/request/request.dart", "Dart", 0, 2, 0, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/auth/response/auth_check_mail_response.dart", "Dart", 0, 18, 9, 8, 35
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/auth/response/auth_check_mail_response.g.dart", "Dart", 0, 15, 4, 5, 24
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/auth/response/response.dart", "Dart", 0, 1, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/auth_reponse.dart", "Dart", 0, 2, 0, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/gpw.dart", "Dart", 0, 2, 0, 0, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/upload_file_response.dart", "Dart", 0, 12, 0, 3, 15
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/base/workplace_generic_data_converter.dart", "Dart", 0, 2, 0, 0, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/community/community.dart", "Dart", 0, -1, 0, -1, -2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/community/workplace_community_members_response.dart", "Dart", 0, -30, -3, -9, -42
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/community/workplace_community_members_response.g.dart", "Dart", 0, -15, -4, -4, -23
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/conversation/conversation.dart", "Dart", 0, 2, 0, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/conversation/workplace_conversation_attachment_response.dart", "Dart", 0, 56, 0, 8, 64
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/conversation/workplace_conversation_attachment_response.g.dart", "Dart", 0, 34, 4, 6, 44
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/conversation/workplace_conversations_response.dart", "Dart", 0, 35, 0, 6, 41
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/conversation/workplace_conversations_response.g.dart", "Dart", 0, 27, 4, 5, 36
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/enums/workplace_enums.dart", "Dart", 0, 2, 0, 0, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/group.dart", "Dart", 0, -1, 0, 0, -1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_feeds_response.dart", "Dart", 0, 29, 2, 7, 38
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_feeds_response.g.dart", "Dart", 0, 37, 4, 6, 47
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_feeds_response.dart", "Dart", 0, -29, -2, -7, -38
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_feeds_response.g.dart", "Dart", 0, -37, -4, -6, -47
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_members_response.dart", "Dart", 0, -29, -3, -9, -41
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_members_response.g.dart", "Dart", 0, -15, -4, -4, -23
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_response.dart", "Dart", 0, -1, 0, 0, -1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_response.g.dart", "Dart", 0, -651, -5, -56, -712
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/post/workplace_post_attachments_response.g.dart", "Dart", 0, 1, 0, 0, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/repository/gapo_impl.dart", "Dart", 0, 56, 10, 9, 75
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/repository/repository.dart", "Dart", 0, 1, 0, 0, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/repository/workplace_repo_impl.dart", "Dart", 0, 7, 0, 2, 9
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/component/app.component.config.dart", "Dart", 0, 59, 0, 0, 59
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/modules/client.module.dart", "Dart", 0, 15, 0, 4, 19
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/modules/database.module.dart", "Dart", 0, 2, 0, 0, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/modules/url.module.dart", "Dart", 0, 7, 3, 2, 12
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/log/base_crawl_log.entity.g.dart", "Dart", 0, 2, 0, 0, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/enums/base_crawl_type.dart", "Dart", 0, 1, 1, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gapo.dart", "Dart", 0, -1, 0, 0, -1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gp_auth_params.dart", "Dart", 0, -17, -1, -4, -22
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gp_auth_params.freezed.dart", "Dart", 0, -202, -15, -23, -240
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gp_auth_params.g.dart", "Dart", 0, -9, -4, -4, -17
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace.dart", "Dart", 0, 4, 0, 0, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_attachment.entity.g.dart", "Dart", 0, 2, 0, 0, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_conversation_attachment.entity.dart", "Dart", 0, 49, 0, 5, 54
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_conversation_attachment.entity.g.dart", "Dart", 0, 1930, 10, 149, 2089
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_conversations.entity.dart", "Dart", 0, 37, 0, 6, 43
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_conversations.entity.g.dart", "Dart", 0, 1725, 13, 136, 1874
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_feed.entity.dart", "Dart", 0, 29, 1, 4, 34
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_feed.entity.g.dart", "Dart", 0, 1331, 6, 100, 1437
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_group_feed.entity.dart", "Dart", 0, -16, -1, -3, -20
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_group_feed.entity.g.dart", "Dart", 0, -1201, 0, -105, -1306
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_user_feed.entity.dart", "Dart", 0, 13, 9, 8, 30
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_user_feed.entity.g.dart", "Dart", 0, 602, 6, 57, 665
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/repository/gapo_repo.dart", "Dart", 0, -5, -1, -5, -11
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/repository/workplace_repo.dart", "Dart", 0, 4, 0, 2, 6
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/gapo/gapo.dart", "Dart", 0, 3, 0, 1, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/gapo/gapo_auth.usecase.dart", "Dart", 0, 47, 10, 14, 71
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/gapo/gapo_auth_check_mail.usecase.dart", "Dart", 0, 17, 9, 5, 31
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/gapo/gapo_upload.usecase.dart", "Dart", 0, 34, 10, 9, 53
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/usecase.dart", "Dart", 0, 3, 0, 0, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/workplace_get_all_groups.usecase.dart", "Dart", 0, -2, 0, 0, -2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/workplace_get_community_members.usecase.dart", "Dart", 0, -2, 0, 0, -2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/workplace_get_conversations.usecase.dart", "Dart", 0, 27, 1, 7, 35
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/workplace_get_user_feeds.usecase.dart", "Dart", 0, 27, 1, 7, 35
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/helpers/helpers.dart", "Dart", 0, 1, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/helpers/jwt_token_decode.dart", "Dart", 0, 58, 31, 10, 99
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/entity/workplace_entity_mapper.auto_mappr.dart", "Dart", 0, 192, 5, 6, 203
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/entity/workplace_entity_mapper.dart", "Dart", 0, 113, 0, 11, 124
"Total", "-", 7, 5335, 207, 545, 6094