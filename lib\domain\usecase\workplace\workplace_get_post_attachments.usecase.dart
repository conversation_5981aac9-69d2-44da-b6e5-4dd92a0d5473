/*
 * Created Date: Friday, 21st June 2024, 21:31:21
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Wednesday, 26th June 2024 10:28:47
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:developer';

import 'package:gp_core/core.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';
import 'package:retry/retry.dart';

// ignore_for_file: public_member_api_docs

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class WorkPlacePostAttachmentsUseCase extends GPBaseFutureUseCase<
        WorkPlacePostAttachmentsInput,
        WorkPlaceListReponse<WorkPlacePostAttachmentsResponse>>
    with WorkPlaceFetchAllDataMixin {
  WorkPlacePostAttachmentsUseCase(
    @Named('kWorkPlaceRepository') this._worplaceRepository,
  );

  final WorkPlaceRepository _worplaceRepository;

  @override
  Future<WorkPlaceListReponse<WorkPlacePostAttachmentsResponse>> buildUseCase(
    WorkPlacePostAttachmentsInput input,
  ) async {
    return retry(
      () async {
        final params = WorkPlaceBaseParams(
          id: input.postId,
          fields: input.fields,
        );

        return await fetchAllData<WorkPlacePostAttachmentsResponse>(
          params: params,
          loadFunction: _worplaceRepository.attachments,
        );
      },
      maxAttempts: 1,
      retryIf: (e) {
        if (e is DioException && e.response?.statusCode == 400) {
          return false;
        }

        return true;
      },
      onRetry: (e) {
        log('ERROR: WorkPlacePostAttachmentsUseCase error -> $e');
      },
    );
  }
}

class WorkPlacePostAttachmentsInput extends GPBaseInput {
  const WorkPlacePostAttachmentsInput({
    required this.postId,
    this.fields = 'target,title,type,url',
  });

  final String postId;
  final String? fields;
}
