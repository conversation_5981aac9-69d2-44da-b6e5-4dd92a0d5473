// ignore_for_file: public_member_api_docs
/*
 * Created Date: 2/01/2024 11:12:45
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 7th March 2024 10:57:15
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';

@LazySingleton(as: WorkPlaceRepository, order: DiConstants.kDataRepositoryOrder)
@Named('kWorkPlaceRepository')
final class WorkPlaceRepoImpl implements WorkPlaceRepository {
  const WorkPlaceRepoImpl(
    @Named('kWorkPlaceService') this.workplaceService,
    @Named('kWorkPlaceLocalService') this.workPlaceLocalService,
  );

  final WorkPlaceService workplaceService;
  final WorkPlaceLocalService workPlaceLocalService;

  @override
  Future<WorkPlaceListReponse<WorkPlaceGroupResponse>> groups(
    WorkPlaceBaseParams params,
  ) {
    return workplaceService.groups(
      communityId: params.id,
      params: params.requestParams,
    );
  }
  
  @override
  Future<WorkPlaceGroupResponse> groupById(WorkPlaceBaseParams params) {
    return workplaceService.groupById(
      groupId: params.id,
      params: params.requestParams,
    );
  }

  @override
  Future<AppConfigEntity> getCurrentAppConfig() {
    return workPlaceLocalService.getCurrentAppConfig();
  }

  @override
  Future<String> getAppVersion() async {
    return workPlaceLocalService.getAppVersion();
  }

  @override
  Future<WorkPlaceListReponse<WorkPlaceUser>> communityMembers(
      WorkPlaceBaseParams params) {
    return workplaceService.communityMembers(
      communityId: params.id,
      params: params.requestParams,
    );
  }
  
  @override
  Future<WorkPlaceUser> communityMemberById(WorkPlaceBaseParams params) {
    return workplaceService.communityMemberById(
      memberId: params.id,
      params: params.requestParams,
    );
  }

  @override
  Future<WorkPlaceListReponse<WorkPlaceUser>> groupMembers(
      WorkPlaceBaseParams params) {
    return workplaceService.groupMembers(
        groupId: params.id, params: params.requestParams);
  }

  @override
  Future<WorkPlaceListReponse<WorkPlacePostAttachmentsResponse>> attachments(
    WorkPlaceBaseParams params,
  ) {
    return workplaceService.attachments(
      postId: params.id,
      params: params.requestParams,
    );
  }

  @override
  Future<WorkPlaceListReponse<WorkPlaceCommentsResponse>> comments(
    WorkPlaceBaseParams params,
  ) {
    return workplaceService.comments(
      postId: params.id,
      params: params.requestParams,
    );
  }

  @override
  Future<WorkPlaceListReponse<WorkPlaceFeedsResponse>> groupFeeds(
    WorkPlaceBaseParams params,
  ) {
    return workplaceService.groupFeeds(
      groupId: params.id,
      params: params.requestParams,
    );
  }

  @override
  Future<WorkPlaceListReponse<WorkPlaceFeedsResponse>> userFeeds(
    WorkPlaceBaseParams params,
  ) {
    return workplaceService.userFeeds(
      userId: params.id,
      params: params.requestParams,
    );
  }

  @override
  Future<WorkPlaceListReponse<WorkPlaceUserConversationsResponse>>
      conversations(
    WorkPlaceBaseParams params,
  ) {
    return workplaceService.conversations(
      userId: params.id,
      params: params.requestParams,
    );
  }

  @override
  Future<WorkPlaceListReponse<Messages>> messages(
    WorkPlaceBaseParams params,
  ) {
    return workplaceService.messages(
      conversationId: params.id,
      params: params.requestParams,
    );
  }

  @override
  Future<WorkPlaceListReponse<WorkPlaceReaction>> commentReactions(
      WorkPlaceBaseParams params) {
    return workplaceService.commentReactions(
        commentId: params.id, params: params.requestParams);
  }

  @override
  Future<WorkPlaceListReponse<WorkPlaceReaction>> feedReactions(
      WorkPlaceBaseParams params) {
    return workplaceService.feedReactions(
        postId: params.id, params: params.requestParams);
  }

  @override
  Future<WorkPlaceListReponse<WorkPlaceUser>> feedSeen(
      WorkPlaceBaseParams params) {
    return workplaceService.feedSeen(
        postId: params.id, params: params.requestParams);
  }
}
