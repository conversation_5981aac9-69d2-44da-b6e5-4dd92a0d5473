// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'gp_group.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GPGroup _$GPGroupFromJson(Map<String, dynamic> json) => GPGroup(
      name: json['name'] as String?,
      description: json['description'] as String?,
      privacy: $enumDecodeNullable(_$GPGroupPrivacyEnumMap, json['privacy']),
      discoverability: $enumDecodeNullable(
          _$GPGroupDiscoverabilityEnumMap, json['discoverability']),
      wpGroupId: json['wp_group_id'] as String?,
      cover: json['cover'] as String?,
      createdAt: json['created_at'] as String?,
      previewMembers: (json['preview_members'] as List<dynamic>?)
          ?.map((e) => GPUser.fromJson(e as Map<String, dynamic>))
          .toList(),
      owner: json['owner'] == null
          ? null
          : GPUser.fromJson(json['owner'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$GPGroupToJson(GPGroup instance) => <String, dynamic>{
      'wp_group_id': instance.wpGroupId,
      'name': instance.name,
      'description': instance.description,
      'cover': instance.cover,
      'privacy': _$GPGroupPrivacyEnumMap[instance.privacy],
      'discoverability':
          _$GPGroupDiscoverabilityEnumMap[instance.discoverability],
      'created_at': instance.createdAt,
      'preview_members': GPGroup._previewMembersToJson(instance.previewMembers),
      'owner': GPGroup._ownerToJson(instance.owner),
    };

const _$GPGroupPrivacyEnumMap = {
  GPGroupPrivacy.closed: 'CLOSED',
  GPGroupPrivacy.public: 'PUBLIC',
};

const _$GPGroupDiscoverabilityEnumMap = {
  GPGroupDiscoverability.hidden: 'HIDDEN',
  GPGroupDiscoverability.visible: 'VISIBLE',
};
