import 'package:gp_core/core.dart';
import 'package:gp_core/utils/gp_sentry.dart';

final box = GetStorage();

class _TokenHandler {
  // số lần tối đa gọi renewToken
  final int maxRenewTokenTimes = 3;

  int currentRenewTokenTimes = 0;

  // chờ 1 khoảng `intervalCallRenewToken` mỗi lần gọi renewToken
  final Duration intervalCallRenewToken = const Duration(seconds: 3);

  final _tokenDio = Dio(
    BaseOptions(
      baseUrl: Constants.apiDomain,
      contentType: Headers.jsonContentType,
      sendTimeout: const Duration(seconds: 10),
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 10),
    ),
  );

  void onError(
    Dio dio,
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    // Assume 401 stands for token expired
    if (err.response?.statusCode == 401 &&
        currentRenewTokenTimes <= maxRenewTokenTimes) {
      await _handle401Error(dio, err, handler);
    } else {
      handler.next(err);
    }
  }

  ///
  /// <i> Updated at 13/07/2023 </i>
  ///
  /// <h1> Chú ý: </h1>
  /// Nếu call n api cùng lúc gặp lỗi 401, khi extends [QueuedInterceptorsWrapper]
  /// <br>
  /// 1. Nếu có pass params `onError`:
  ///
  /// - Trường hợp 1: <b> Có 1 api gặp lỗi: </b>
  /// <br> `onResponse`, `onError` của từng api sẽ chạy theo queue:
  /// <br> nếu có 1 api trong queue chưa `resolve` thì các api sau đều sẽ không run vào
  /// <br> `onRequest`, `onReponse`, và `onError`
  /// <br>
  ///
  /// - Trường hợp 2:
  /// <br> Khi không có error ở bất cứ api nào -> các api chạy độc lập, Parallel api
  ///
  /// 2. Nếu không pass params `onError`, `onResponse`:
  /// <br> Các api chạy độc lập, Parallel api
  ///
  /// <br>
  /// <h2> Logic renew token: </h2>
  ///
  /// Với bất cứ api nào gặp lỗi, statusCode = `401`:
  /// 1. Gọi api renewToken:
  /// - Số lần gọi tối đa = [intervalCallRenewToken]
  /// 2. Nếu renewToken thất bại:
  /// - Gọi lại renewToken, tối đa [intervalCallRenewToken]
  /// - Thời gian delay giữa mỗi lần gọi renewToken = [intervalCallRenewToken]
  /// <br> <b>Chỉ hoạt động khi renewToken không thành công</b>
  /// 3. Khi renewToken thành công:
  /// - Handle tiếp api gặp lỗi 401 trước đó
  ///
  Future _handle401Error(
    Dio dio,
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    if (err.response != null) {
      currentRenewTokenTimes++;

      final options = err.response!.requestOptions;

      final String? accessToken = await _renewToken(options, handler);

      logDebug("_____token_interceptor, new accessToken => $accessToken");

      if (accessToken != null) {
        // recall api with new accessToken
        await dio.fetch(options).then((value) {
          return handler.resolve(value);
        });

        currentRenewTokenTimes = 0;

        logDebug("_____token_interceptor done");

        return;
      }
    } else {
      handler.next(err);
    }
  }

  Future<String?> _renewToken(
      RequestOptions options, ErrorInterceptorHandler handler) async {
    RenewTokenInfo? result;

    for (var i = 0; i < maxRenewTokenTimes; i++) {
      gpCoreLog.info("Flutter:core.renewToken: $i/$maxRenewTokenTimes");

      result = await _fetchRenewToken();

      if (result != null) {
        break;
      } else {
        // wait a duration before renewToken again
        await Future.delayed(intervalCallRenewToken);
      }
    }

    // renewToken với last saved refreshToken bên Flutter
    result ??= await _fetchRenewToken(
        refreshTokenStr: await TokenManager.getLastRefreshToken());

    return result?.accessToken;
  }

  Future<RenewTokenInfo?> _fetchRenewToken({String? refreshTokenStr}) async {
    final body = {};

    if (refreshTokenStr == null) {
      body.addAll({"refresh_token": await TokenManager.refreshToken()});
    } else {
      body.addAll({"refresh_token": refreshTokenStr});
    }

    GPCoreTracker().appendMessage(
      'Flutter:core.renewToken begin',
      data: {
        'refresh_token': body,
      },
    );

    try {
      return await _tokenDio
          .post(
              "${Constants.apiDomain}${Constants.authAPI}${Constants.renewTokenPath}",
              data: body)
          .then((d) async {
        ApiResponse<RenewTokenInfo> result = ApiResponse.fromJson(
          d.data,
          (jsonData) => RenewTokenInfo.fromJson(jsonData),
        );

        await TokenManager.saveRenewTokenInfo(result.data);

        return result.data;
      });
    } catch (e, s) {
      gpCoreLog.severe("Flutter.core.renewToken.error", e, s);

      GPCoreTracker().appendError(
        'Flutter.core.renewToken.error',
        data: {'error': e, 'stacktrace': s},
      );

      GPCoreTracker().sendLog(
        message: 'Flutter.core.renewToken.error',
      );

      return null;
    }
  }
}

class TokenInterceptor extends QueuedInterceptorsWrapper {
  TokenInterceptor(this.dio)
      : super(onError: (err, handler) {
          _tokenHandler.onError(dio, err, handler);
        });

  final Dio dio;

  static final _TokenHandler _tokenHandler = _TokenHandler();
}
