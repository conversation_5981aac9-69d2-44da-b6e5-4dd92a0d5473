/*
 * Created Date: Saturday, 6th April 2024, 20:42:05
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Wednesday, 28th August 2024 16:15:57
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:dio/dio.dart' hide Headers;
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/app/app.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:injectable/injectable.dart';
import 'package:retrofit/retrofit.dart';

part 'workplace.service.g.dart';

@LazySingleton(order: DiConstants.kDataServiceOrder)
@Named('kWorkPlaceService')
@RestApi()
abstract class WorkPlaceService {
  @FactoryMethod()
  factory WorkPlaceService(
    @Named('kWorkPlaceDio') Dio dio, {
    @Named('kWorkPlaceUrl') String? baseUrl,
  }) = _WorkPlaceService;

  @GET('/{community_id}${FaceBookWorkPlaceConstants.kWorkPlaceGroups}')
  Future<WorkPlaceListReponse<WorkPlaceGroupResponse>> groups({
    @Path('community_id') required String communityId,
    @Queries() Map<String, String>? params,
  });

  @GET('/{group_id}}')
  Future<WorkPlaceGroupResponse> groupById({
    @Path('community_id') required String groupId,
    @Queries() Map<String, String>? params,
  });

  @GET('/{group_id}${FaceBookWorkPlaceConstants.kWorkPlaceMembers}')
  Future<WorkPlaceListReponse<WorkPlaceUser>> groupMembers({
    @Path('group_id') required String groupId,
    @Queries() Map<String, String>? params,
  });

  @GET('/{community_id}${FaceBookWorkPlaceConstants.kWorkPlaceMembers}')
  Future<WorkPlaceListReponse<WorkPlaceUser>> communityMembers({
    @Path('community_id') required String communityId,
    @Queries() Map<String, String>? params,
  });

  @GET('/{member_id}')
  Future<WorkPlaceUser> communityMemberById({
    @Path('member_id') required String memberId,
    @Queries() Map<String, String>? params,
  });

  @GET('/{group_id}${FaceBookWorkPlaceConstants.kWorkPlaceFeed}')
  Future<WorkPlaceListReponse<WorkPlaceFeedsResponse>> groupFeeds({
    @Path('group_id') required String groupId,
    @Queries() Map<String, String>? params,
  });

  @GET('/{post_id}${FaceBookWorkPlaceConstants.kWorkPlaceComments}')
  Future<WorkPlaceListReponse<WorkPlaceCommentsResponse>> comments({
    @Path('post_id') required String postId,
    @Queries() Map<String, String>? params,
  });

  @GET('/{post_id}${FaceBookWorkPlaceConstants.kWorkPlaceAttachments}')
  Future<WorkPlaceListReponse<WorkPlacePostAttachmentsResponse>> attachments({
    @Path('post_id') required String postId,
    @Queries() Map<String, String>? params,
  });

  @GET('/{user_id}${FaceBookWorkPlaceConstants.kWorkPlaceConversations}')
  Future<WorkPlaceListReponse<WorkPlaceUserConversationsResponse>>
      conversations({
    @Path('user_id') required String userId,
    @Queries() Map<String, String>? params,
  });

  @GET(
    '/{conversation_id}${FaceBookWorkPlaceConstants.kWorkPlaceMessages}',
  )
  Future<WorkPlaceListReponse<Messages>> messages({
    @Path('conversation_id') required String conversationId,
    @Queries() Map<String, String>? params,
  });

  @GET('/{user_id}${FaceBookWorkPlaceConstants.kWorkPlaceFeed}')
  Future<WorkPlaceListReponse<WorkPlaceFeedsResponse>> userFeeds({
    @Path('user_id') required String userId,
    @Queries() Map<String, String>? params,
  });

  @GET('/{post_id}${FaceBookWorkPlaceConstants.kWorkPlaceReactions}')
  Future<WorkPlaceListReponse<WorkPlaceReaction>> feedReactions({
    @Path('post_id') required String postId,
    @Queries() Map<String, String>? params,
  });

  @GET('/{post_id}${FaceBookWorkPlaceConstants.kWorkPlaceSeen}')
  Future<WorkPlaceListReponse<WorkPlaceUser>> feedSeen({
    @Path('post_id') required String postId,
    @Queries() Map<String, String>? params,
  });

  @GET('/{comment_id}${FaceBookWorkPlaceConstants.kWorkPlaceReactions}')
  Future<WorkPlaceListReponse<WorkPlaceReaction>> commentReactions({
    @Path('comment_id') required String commentId,
    @Queries() Map<String, String>? params,
  });
}
