/*
 * Created Date: Tuesday, 11th June 2024, 10:51:06
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 27th August 2024 22:46:49
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:async_task/async_task_extension.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:gp_fbwp_crawler/helpers/gp_upload_management.dart';
import 'package:injectable/injectable.dart';

// ignore_for_file: public_member_api_docs

const Duration kThrottleTimer = Duration(milliseconds: 50);

@Injectable(order: DiConstants.kDomainUseCaseOrder)
final class GPUploadUseCase extends GPBaseFutureUseCase<GPUploadUseCaseInput,
    UploadFileResponseModelWrapper> {
  GPUploadUseCase(
    @Named('kGapoRepository') this._gpRepository,
  );

  // final BehaviorSubject<GPUploadProgressEntity> _rxDashBoardProgress =
  //     BehaviorSubject();
  // late final Stream<GPUploadProgressEntity> dashboardStream =
  //     _rxDashBoardProgress.stream;

  final GapoRepository _gpRepository;

  @override
  Future<UploadFileResponseModelWrapper> buildUseCase(
    GPUploadUseCaseInput input,
  ) async {
    final GPUploadManagment uploadManagment = GPUploadManagment(
      input: input,
      gpRepository: _gpRepository,
      // rxDashBoardProgress: _rxDashBoardProgress,
    );

    final result =  await uploadManagment.startUploadFiles();

    return result;
  }
}

final class GPUploadUseCaseInput extends GPBaseInput {
  const GPUploadUseCaseInput({
    required this.uploadInput,
    this.dashboardProgressThrottleTime = kThrottleTimer,
    this.isParallelism = true,
  });

  final GPUploadInput uploadInput;

  final Duration dashboardProgressThrottleTime;

  final bool isParallelism;
}
