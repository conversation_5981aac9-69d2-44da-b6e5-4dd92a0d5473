import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';

// ignore_for_file: public_member_api_docs

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class GPAuthSetPasswordUseCase extends GPBaseFutureUseCase<
    GPAuthSetPasswordInput, ApiResponseV2WithoutData> {
  const GPAuthSetPasswordUseCase(
    @Named('kGapoRepository') this._gpRepository,
  );

  final GapoRepository _gpRepository;

  @override
  Future<ApiResponseV2WithoutData> buildUseCase(
      GPAuthSetPasswordInput input) async {
    return _gpRepository.setPassword(params: input.params, token: input.token);
  }
}

class GPAuthSetPasswordInput extends GPBaseInput {
  const GPAuthSetPasswordInput({
    required this.params,
    required this.token,
  });

  final GPSetPasswordParams params;
  final String token;
}
