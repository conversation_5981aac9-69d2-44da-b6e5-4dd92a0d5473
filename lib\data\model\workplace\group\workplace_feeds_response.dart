import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'workplace_feeds_response.g.dart';

@JsonSerializable(createToJson: false, fieldRename: FieldRename.snake)
@DateTimeConverter()
class WorkPlaceFeedsResponse {
  WorkPlaceFeedsResponse({
    required this.id,
    this.message,
    this.updatedTime,
    this.from,
    this.attachments,
    this.to,
    this.formatting,
    this.createdTime,
    this.comments,
  });

  final String? message;
  final String id;
  final DateTime? updatedTime;

  /// Information about the person or profile that posted the message.
  final WorkPlaceUser? from;
  final WorkPlaceListReponse<WorkPlacePostAttachmentsResponse>? attachments;

  /// Profiles mentioned or targeted in this post.
  final WorkPlaceListReponse<WorkPlaceUser>? to;
  final WorkPlaceFormatting? formatting;
  final DateTime? createdTime;
  final WorkPlaceListReponse<WorkPlaceCommentsResponse>? comments;

  factory WorkPlaceFeedsResponse.fromJson(Map<String, dynamic> json) =>
      _$WorkPlaceFeedsResponseFromJson(json);

  bool get isPostInTimeline {
    final listTo = List<WorkPlaceUser>.from(to?.data ?? []);
    listTo.removeWhere((element) => element.name?.startsWith('#', 0) ?? true);
    return listTo.isEmpty;
  }
}
