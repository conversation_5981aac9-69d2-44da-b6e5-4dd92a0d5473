import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';

part 'workplace_list_response.freezed.dart';
part 'workplace_list_response.g.dart';

@Freezed(copyWith: true, toJson: false)
class WorkPlaceListReponse<T> extends GPBaseOutput with _$WorkPlaceListReponse<T> {
  factory WorkPlaceListReponse({
    @WorkPlaceModelConverter() required List<T> data,
    WorkPlacePagingResponse? paging,
  }) = _WorkPlaceListReponse<T>;

  factory WorkPlaceListReponse.fromJson(Map<String, dynamic> json) =>
      _$WorkPlaceListReponseFromJson(json);
}
