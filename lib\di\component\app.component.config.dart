// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:dio/dio.dart' as _i14;
import 'package:flutter/material.dart' as _i7;
import 'package:get_it/get_it.dart' as _i1;
import 'package:gp_core/core.dart' as _i11;
import 'package:gp_core_v2/gp_core_v2.dart' as _i3;
import 'package:gp_dio_log/gp_dio_log.dart' as _i6;
import 'package:gp_fbwp_crawler/app/app_config/bloc/app_config_bloc.dart'
    as _i4;
import 'package:gp_fbwp_crawler/app/base/networking/networking.dart' as _i5;
import 'package:gp_fbwp_crawler/app/features/home/<USER>/home_page_bloc.dart'
    as _i8;
import 'package:gp_fbwp_crawler/data/data.dart' as _i27;
import 'package:gp_fbwp_crawler/data/data_source/data_source.dart' as _i24;
import 'package:gp_fbwp_crawler/data/data_source/local/workplace_local.service.dart'
    as _i18;
import 'package:gp_fbwp_crawler/data/data_source/remote/download/download.service.dart'
    as _i12;
import 'package:gp_fbwp_crawler/data/data_source/remote/facebook.service.dart'
    as _i13;
import 'package:gp_fbwp_crawler/data/data_source/remote/gapo/auth.service.dart'
    as _i21;
import 'package:gp_fbwp_crawler/data/data_source/remote/gapo/group.service.dart'
    as _i15;
import 'package:gp_fbwp_crawler/data/data_source/remote/gapo/upload.service.dart'
    as _i16;
import 'package:gp_fbwp_crawler/data/data_source/remote/gapo/user.service.dart'
    as _i17;
import 'package:gp_fbwp_crawler/data/data_source/remote/gapo/workspace.service.dart'
    as _i20;
import 'package:gp_fbwp_crawler/data/data_source/remote/workplace.service.dart'
    as _i19;
import 'package:gp_fbwp_crawler/data/repository/download_impl.dart' as _i23;
import 'package:gp_fbwp_crawler/data/repository/facebook_repo_impl.dart'
    as _i26;
import 'package:gp_fbwp_crawler/data/repository/gapo_impl.dart' as _i28;
import 'package:gp_fbwp_crawler/data/repository/workplace_repo_impl.dart'
    as _i29;
import 'package:gp_fbwp_crawler/di/modules/app.module.dart' as _i66;
import 'package:gp_fbwp_crawler/di/modules/auth.module.dart' as _i65;
import 'package:gp_fbwp_crawler/di/modules/client.module.dart' as _i61;
import 'package:gp_fbwp_crawler/di/modules/database.module.dart' as _i63;
import 'package:gp_fbwp_crawler/di/modules/navigator.module.dart' as _i62;
import 'package:gp_fbwp_crawler/di/modules/url.module.dart' as _i64;
import 'package:gp_fbwp_crawler/domain/domain.dart' as _i25;
import 'package:gp_fbwp_crawler/domain/repository/repository.dart' as _i22;
import 'package:gp_fbwp_crawler/domain/usecase/download_file.usecase.dart'
    as _i37;
import 'package:gp_fbwp_crawler/domain/usecase/facebook_get_community.usecase.dart'
    as _i38;
import 'package:gp_fbwp_crawler/domain/usecase/gapo/gapo_adjust_member_role_group.usecase.dart'
    as _i39;
import 'package:gp_fbwp_crawler/domain/usecase/gapo/gapo_auth.usecase.dart'
    as _i41;
import 'package:gp_fbwp_crawler/domain/usecase/gapo/gapo_auth_check_mail.usecase.dart'
    as _i36;
import 'package:gp_fbwp_crawler/domain/usecase/gapo/gapo_auth_set_password.usecase.dart'
    as _i40;
import 'package:gp_fbwp_crawler/domain/usecase/gapo/gapo_create_group.usecase.dart'
    as _i42;
import 'package:gp_fbwp_crawler/domain/usecase/gapo/gapo_create_user.usecase.dart'
    as _i32;
import 'package:gp_fbwp_crawler/domain/usecase/gapo/gapo_invite_group.usecase.dart'
    as _i44;
import 'package:gp_fbwp_crawler/domain/usecase/gapo/gapo_invite_workspace.usecase.dart'
    as _i43;
import 'package:gp_fbwp_crawler/domain/usecase/gapo/gapo_leave_group.usecase.dart'
    as _i45;
import 'package:gp_fbwp_crawler/domain/usecase/gapo/gapo_login.usecase.dart'
    as _i31;
import 'package:gp_fbwp_crawler/domain/usecase/gapo/gapo_upload.usecase.dart'
    as _i46;
import 'package:gp_fbwp_crawler/domain/usecase/gapo/gapo_user_profile.usecase.dart'
    as _i47;
import 'package:gp_fbwp_crawler/domain/usecase/workplace/workplace_get_all_groups.usecase.dart'
    as _i52;
import 'package:gp_fbwp_crawler/domain/usecase/workplace/workplace_get_app_config.usecase.dart'
    as _i53;
import 'package:gp_fbwp_crawler/domain/usecase/workplace/workplace_get_comment_reactions.usecase.dart'
    as _i48;
import 'package:gp_fbwp_crawler/domain/usecase/workplace/workplace_get_community_members.usecase.dart'
    as _i50;
import 'package:gp_fbwp_crawler/domain/usecase/workplace/workplace_get_community_members_by_id.usecase.dart'
    as _i49;
import 'package:gp_fbwp_crawler/domain/usecase/workplace/workplace_get_conversations.usecase.dart'
    as _i33;
import 'package:gp_fbwp_crawler/domain/usecase/workplace/workplace_get_conversations_messages.usecase.dart'
    as _i35;
import 'package:gp_fbwp_crawler/domain/usecase/workplace/workplace_get_group_by_id.usecase.dart'
    as _i51;
import 'package:gp_fbwp_crawler/domain/usecase/workplace/workplace_get_group_feeds.usecase.dart'
    as _i54;
import 'package:gp_fbwp_crawler/domain/usecase/workplace/workplace_get_group_members.usecase.dart'
    as _i55;
import 'package:gp_fbwp_crawler/domain/usecase/workplace/workplace_get_post_attachments.usecase.dart'
    as _i56;
import 'package:gp_fbwp_crawler/domain/usecase/workplace/workplace_get_post_comments.usecase.dart'
    as _i57;
import 'package:gp_fbwp_crawler/domain/usecase/workplace/workplace_get_post_reactions.usecase.dart'
    as _i58;
import 'package:gp_fbwp_crawler/domain/usecase/workplace/workplace_get_post_seen.usecase.dart'
    as _i59;
import 'package:gp_fbwp_crawler/domain/usecase/workplace/workplace_get_reply_comments.usecase.dart'
    as _i60;
import 'package:gp_fbwp_crawler/domain/usecase/workplace/workplace_get_user_feeds.usecase.dart'
    as _i34;
import 'package:gp_fbwp_crawler/mapper/gp_mapper.dart' as _i30;
import 'package:injectable/injectable.dart' as _i2;
import 'package:isar/isar.dart' as _i9;
import 'package:talker/talker.dart' as _i10;

const String _SaasDevelop = 'SaasDevelop';
const String _SaasQa = 'SaasQa';
const String _SaasProd = 'SaasProd';
const String _SaasUat = 'SaasUat';
const String _OnPremiseDevelop = 'OnPremiseDevelop';
const String _OnPremiseQa = 'OnPremiseQa';

extension GetItInjectableX on _i1.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  Future<_i1.GetIt> init({
    String? environment,
    _i2.EnvironmentFilter? environmentFilter,
  }) async {
    final gh = _i2.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    await _i3.GpCoreV2PackageModule().init(gh);
    final clientModule = _$ClientModule();
    final navigatorModule = _$NavigatorModule();
    final databaseModule = _$DatabaseModule();
    final authModule = _$AuthModule();
    final urlModule = _$UrlModule();
    final appModule = _$AppModule();
    gh.singleton<_i4.AppConfigBloc>(() => _i4.AppConfigBloc());
    gh.factory<_i5.ErrorLoggerInterceptor>(
        () => clientModule.errorLoggerInterceptor);
    //gh.factory<_i6.GPDioLogInterceptor>(() => clientModule.gpDioLogInterceptor);
    gh.singleton<_i7.GlobalKey<_i7.ScaffoldState>>(
      () => navigatorModule.kRootScaffoldKey,
      instanceName: 'kRootScaffoldKey',
    );
    gh.singleton<_i7.GlobalKey<_i7.ScaffoldMessengerState>>(
      () => navigatorModule.kRootScaffoldMessengerKey,
      instanceName: 'kScaffoldMessengerState',
    );
    gh.singleton<_i7.GlobalKey<_i7.NavigatorState>>(
      () => navigatorModule.kNavigatorKey,
      instanceName: 'kNavigatorKey',
    );
    gh.singleton<_i8.HomeBloc>(() => _i8.HomeBloc());
    await gh.factoryAsync<_i9.Isar>(
      () => databaseModule.kIsar,
      instanceName: 'kIsar',
      preResolve: true,
    );
    gh.factory<_i5.LoggerInterceptor>(() => clientModule.loggerInterceptor);
    gh.factory<_i5.RequestCounterInterceptor>(
        () => clientModule.requestCounterInterceptor);
    gh.singleton<String>(
      () => authModule.kAdminEmailProd,
      instanceName: 'kAdminEmail',
      registerFor: {_SaasProd},
    );
    gh.singleton<String>(
      () => authModule.kAdminPasswordUat,
      instanceName: 'kAdminPassword',
      registerFor: {_SaasUat},
    );
    gh.singleton<String>(
      () => authModule.kAdminEmailDev,
      instanceName: 'kAdminEmail',
      registerFor: {
        _SaasDevelop,
        _SaasQa,
      },
    );
    gh.singleton<String>(
      () => authModule.kAdminPasswordDev,
      instanceName: 'kAdminPassword',
      registerFor: {
        _SaasDevelop,
        _SaasQa,
      },
    );
    gh.singleton<String>(
      () => urlModule.kGapoWorkDomainProd,
      instanceName: 'kGapoWorkDomain',
      registerFor: {_SaasProd},
    );
    gh.singleton<String>(
      () => urlModule.kFaceBookUrl,
      instanceName: 'kFaceBookUrl',
    );
    gh.singleton<String>(
      () => urlModule.kWorkPlaceUrl,
      instanceName: 'kWorkPlaceUrl',
    );
    gh.singleton<String>(
      () => urlModule.kGapoWorkUploadDomainStaging,
      instanceName: 'kGapoWorkUploadDomain',
      registerFor: {
        _SaasDevelop,
        _SaasQa,
      },
    );
    gh.singleton<String>(
      () => urlModule.kGapoWorkUploadDomainUat,
      instanceName: 'kGapoWorkUploadDomain',
      registerFor: {_SaasUat},
    );
    gh.singleton<String>(
      () => urlModule.kGapoWorkDomainUat,
      instanceName: 'kGapoWorkDomain',
      registerFor: {_SaasUat},
    );
    gh.singleton<String>(
      () => urlModule.kGapoWorkUploadDomainProd,
      instanceName: 'kGapoWorkUploadDomain',
      registerFor: {_SaasProd},
    );
    gh.singleton<String>(
      () => urlModule.kGapoWorkDomainStaging,
      instanceName: 'kGapoWorkDomain',
      registerFor: {
        _SaasDevelop,
        _SaasQa,
      },
    );
    gh.singleton<String>(
      () => authModule.kAdminPasswordProd,
      instanceName: 'kAdminPassword',
      registerFor: {_SaasProd},
    );
    gh.singleton<String>(
      () => authModule.kAdminEmailUat,
      instanceName: 'kAdminEmail',
      registerFor: {_SaasUat},
    );
    gh.singleton<_i10.Talker>(
      () => appModule.talker,
      registerFor: {
        _SaasDevelop,
        _SaasQa,
        _OnPremiseDevelop,
        _OnPremiseQa,
      },
    );
    await gh.factoryAsync<bool>(
      () => appModule.init,
      preResolve: true,
    );
    gh.singleton<_i11.Dio>(
      () => clientModule.createWorkPlaceDio(),
      instanceName: 'kWorkPlaceDio',
    );
    gh.singleton<_i11.Dio>(
      () => clientModule.createWorkPlaceDownloadDio(),
      instanceName: 'kWorkPlaceDownloadDio',
    );
    gh.singleton<_i11.Dio>(
      () => clientModule.createDio(),
      instanceName: 'kGapoWorkDio',
    );
    gh.singleton<_i11.Dio>(
      () => clientModule.createGPUserDio(),
      instanceName: 'kGapoWorkUserDio',
    );
    gh.singleton<_i11.Dio>(
      () => clientModule.createGPUploadDio(),
      instanceName: 'kGapoWorkUploadDio',
    );
    gh.lazySingleton<_i12.DownloadService>(
      () => _i12.DownloadService(),
      instanceName: 'kDownloadService',
    );
    gh.lazySingleton<_i13.FaceBookService>(
      () => _i13.FaceBookService(
        gh<_i14.Dio>(instanceName: 'kWorkPlaceDio'),
        baseUrl: gh<String>(instanceName: 'kFaceBookUrl'),
      ),
      instanceName: 'kFaceBookService',
    );
    gh.lazySingleton<_i15.GroupService>(
      () => _i15.GroupService(
        gh<_i14.Dio>(instanceName: 'kGapoWorkDio'),
        baseUrl: gh<String>(instanceName: 'kGapoWorkDomain'),
      ),
      instanceName: 'kGapoWorkGroupService',
    );
    gh.lazySingleton<_i16.UploadService>(
      () => _i16.UploadService(
        gh<_i11.Dio>(instanceName: 'kGapoWorkUploadDio'),
        baseUrl: gh<String>(instanceName: 'kGapoWorkUploadDomain'),
      ),
      instanceName: 'kGapoWorkUploadService',
    );
    gh.lazySingleton<_i17.UserService>(
      () => _i17.UserService(
        gh<_i14.Dio>(instanceName: 'kGapoWorkUserDio'),
        baseUrl: gh<String>(instanceName: 'kGapoWorkDomain'),
      ),
      instanceName: 'kGapoWorkUserService',
    );
    gh.lazySingleton<_i18.WorkPlaceLocalService>(
      () => _i18.WorkPlaceLocalService(gh<_i9.Isar>(instanceName: 'kIsar')),
      instanceName: 'kWorkPlaceLocalService',
    );
    gh.lazySingleton<_i19.WorkPlaceService>(
      () => _i19.WorkPlaceService(
        gh<_i14.Dio>(instanceName: 'kWorkPlaceDio'),
        baseUrl: gh<String>(instanceName: 'kWorkPlaceUrl'),
      ),
      instanceName: 'kWorkPlaceService',
    );
    gh.lazySingleton<_i20.WorkSpaceService>(
      () => _i20.WorkSpaceService(
        gh<_i14.Dio>(instanceName: 'kGapoWorkDio'),
        baseUrl: gh<String>(instanceName: 'kGapoWorkDomain'),
      ),
      instanceName: 'kGapoWorkWorkspaceService',
    );
    gh.lazySingleton<_i21.AuthService>(
      () => _i21.AuthService(
        gh<_i14.Dio>(instanceName: 'kGapoWorkDio'),
        baseUrl: gh<String>(instanceName: 'kGapoWorkDomain'),
      ),
      instanceName: 'kGapoWorkAuthService',
    );
    gh.lazySingleton<_i22.DownloadRepository>(
      () => _i23.DownloadRepositoryImpl(
          gh<_i24.DownloadService>(instanceName: 'kDownloadService')),
      instanceName: 'kDownloadRepository',
    );
    gh.lazySingleton<_i25.FaceBookRepository>(
      () => _i26.FaceBookRepoImpl(
          gh<_i27.FaceBookService>(instanceName: 'kFaceBookService')),
      instanceName: 'kFaceBookRepository',
    );
    gh.lazySingleton<_i25.GapoRepository>(
      () => _i28.GapoRepoImpl(
        gh<_i27.AuthService>(instanceName: 'kGapoWorkAuthService'),
        gh<_i27.UploadService>(instanceName: 'kGapoWorkUploadService'),
        gh<_i27.GroupService>(instanceName: 'kGapoWorkGroupService'),
        gh<_i27.WorkSpaceService>(instanceName: 'kGapoWorkWorkspaceService'),
        gh<_i27.UserService>(instanceName: 'kGapoWorkUserService'),
      ),
      instanceName: 'kGapoRepository',
    );
    gh.lazySingleton<_i25.WorkPlaceRepository>(
      () => _i29.WorkPlaceRepoImpl(
        gh<_i27.WorkPlaceService>(instanceName: 'kWorkPlaceService'),
        gh<_i27.WorkPlaceLocalService>(instanceName: 'kWorkPlaceLocalService'),
      ),
      instanceName: 'kWorkPlaceRepository',
    );
    gh.singleton<_i30.GPMapper>(
      () => _i30.GPMapper(),
      instanceName: 'kGPMapper',
    );
    gh.factory<_i31.InitGPAdminUseCase>(() => const _i31.InitGPAdminUseCase());
    gh.factory<_i32.GapoSignupUseCase>(() => _i32.GapoSignupUseCase(
        gh<_i25.GapoRepository>(instanceName: 'kGapoRepository')));
    gh.factory<_i33.WorkPlaceUserConversationsUseCase>(() =>
        _i33.WorkPlaceUserConversationsUseCase(gh<_i25.WorkPlaceRepository>(
            instanceName: 'kWorkPlaceRepository')));
    gh.factory<_i34.WorkPlaceUserFeedsUseCase>(() =>
        _i34.WorkPlaceUserFeedsUseCase(gh<_i25.WorkPlaceRepository>(
            instanceName: 'kWorkPlaceRepository')));
    gh.factory<_i35.WorkPlaceUserMessagesUseCase>(() =>
        _i35.WorkPlaceUserMessagesUseCase(gh<_i25.WorkPlaceRepository>(
            instanceName: 'kWorkPlaceRepository')));
    gh.factory<_i36.AuthCheckMailUseCase>(() => _i36.AuthCheckMailUseCase(
        gh<_i25.GapoRepository>(instanceName: 'kGapoRepository')));
    gh.factory<_i37.DownloadFileUseCase>(() => _i37.DownloadFileUseCase(
        gh<_i22.DownloadRepository>(instanceName: 'kDownloadRepository')));
    gh.factory<_i38.FaceBookGetCommunityUseCase>(() =>
        _i38.FaceBookGetCommunityUseCase(
            gh<_i25.FaceBookRepository>(instanceName: 'kFaceBookRepository')));
    gh.factory<_i39.GPAdjustMemberRoleGroupUseCase>(() =>
        _i39.GPAdjustMemberRoleGroupUseCase(
            gh<_i25.GapoRepository>(instanceName: 'kGapoRepository')));
    gh.factory<_i40.GPAuthSetPasswordUseCase>(() =>
        _i40.GPAuthSetPasswordUseCase(
            gh<_i25.GapoRepository>(instanceName: 'kGapoRepository')));
    gh.factory<_i41.GPAuthUseCase>(() => _i41.GPAuthUseCase(
        gh<_i25.GapoRepository>(instanceName: 'kGapoRepository')));
    gh.factory<_i42.GPCreateGroupUseCase>(() => _i42.GPCreateGroupUseCase(
        gh<_i25.GapoRepository>(instanceName: 'kGapoRepository')));
    gh.factory<_i43.GPInviteEmailWorkspaceUseCase>(() =>
        _i43.GPInviteEmailWorkspaceUseCase(
            gh<_i25.GapoRepository>(instanceName: 'kGapoRepository')));
    gh.factory<_i44.GPInviteGroupUseCase>(() => _i44.GPInviteGroupUseCase(
        gh<_i25.GapoRepository>(instanceName: 'kGapoRepository')));
    gh.factory<_i45.GPLeaveGroupUseCase>(() => _i45.GPLeaveGroupUseCase(
        gh<_i25.GapoRepository>(instanceName: 'kGapoRepository')));
    gh.factory<_i46.GPUploadUseCase>(() => _i46.GPUploadUseCase(
        gh<_i25.GapoRepository>(instanceName: 'kGapoRepository')));
    gh.factory<_i47.GPUserProfileUseCase>(() => _i47.GPUserProfileUseCase(
        gh<_i25.GapoRepository>(instanceName: 'kGapoRepository')));
    gh.factory<_i48.WorkPlaceCommentReactionsUseCase>(() =>
        _i48.WorkPlaceCommentReactionsUseCase(gh<_i25.WorkPlaceRepository>(
            instanceName: 'kWorkPlaceRepository')));
    gh.factory<_i49.WorkPlaceCommunityMemberByIdUseCase>(() =>
        _i49.WorkPlaceCommunityMemberByIdUseCase(gh<_i25.WorkPlaceRepository>(
            instanceName: 'kWorkPlaceRepository')));
    gh.factory<_i50.WorkPlaceCommunityMembersUseCase>(() =>
        _i50.WorkPlaceCommunityMembersUseCase(gh<_i25.WorkPlaceRepository>(
            instanceName: 'kWorkPlaceRepository')));
    gh.factory<_i51.WorkPlaceGetAllGroupsByIdUseCase>(() =>
        _i51.WorkPlaceGetAllGroupsByIdUseCase(gh<_i25.WorkPlaceRepository>(
            instanceName: 'kWorkPlaceRepository')));
    gh.factory<_i52.WorkPlaceGetAllGroupsUseCase>(() =>
        _i52.WorkPlaceGetAllGroupsUseCase(gh<_i25.WorkPlaceRepository>(
            instanceName: 'kWorkPlaceRepository')));
    gh.factory<_i53.WorkPlaceGetAppConfigUseCase>(() =>
        _i53.WorkPlaceGetAppConfigUseCase(gh<_i25.WorkPlaceRepository>(
            instanceName: 'kWorkPlaceRepository')));
    gh.factory<_i54.WorkPlaceGroupFeedsUseCase>(() =>
        _i54.WorkPlaceGroupFeedsUseCase(gh<_i25.WorkPlaceRepository>(
            instanceName: 'kWorkPlaceRepository')));
    gh.factory<_i55.WorkPlaceGroupMembersUseCase>(() =>
        _i55.WorkPlaceGroupMembersUseCase(gh<_i25.WorkPlaceRepository>(
            instanceName: 'kWorkPlaceRepository')));
    gh.factory<_i56.WorkPlacePostAttachmentsUseCase>(() =>
        _i56.WorkPlacePostAttachmentsUseCase(gh<_i25.WorkPlaceRepository>(
            instanceName: 'kWorkPlaceRepository')));
    gh.factory<_i57.WorkPlacePostCommentsUseCase>(() =>
        _i57.WorkPlacePostCommentsUseCase(gh<_i25.WorkPlaceRepository>(
            instanceName: 'kWorkPlaceRepository')));
    gh.factory<_i58.WorkPlacePostReactionsUseCase>(() =>
        _i58.WorkPlacePostReactionsUseCase(gh<_i25.WorkPlaceRepository>(
            instanceName: 'kWorkPlaceRepository')));
    gh.factory<_i59.WorkPlacePostSeenUseCase>(() =>
        _i59.WorkPlacePostSeenUseCase(gh<_i25.WorkPlaceRepository>(
            instanceName: 'kWorkPlaceRepository')));
    gh.factory<_i60.WorkPlaceReplyCommentsUseCase>(() =>
        _i60.WorkPlaceReplyCommentsUseCase(gh<_i25.WorkPlaceRepository>(
            instanceName: 'kWorkPlaceRepository')));
    return this;
  }
}

class _$ClientModule extends _i61.ClientModule {}

class _$NavigatorModule extends _i62.NavigatorModule {}

class _$DatabaseModule extends _i63.DatabaseModule {}

class _$UrlModule extends _i64.UrlModule {}

class _$AuthModule extends _i65.AuthModule {}

class _$AppModule extends _i66.AppModule {}
