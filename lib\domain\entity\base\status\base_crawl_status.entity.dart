/*
 * Created Date: Saturday, 1st June 2024, 10:42:12
 * Author: ToanNM
 * -----
 * Last Modified: Saturday, 1st June 2024 15:22:22
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_fbwp_crawler/domain/entity/enums/base_crawl_status_enum.dart';
import 'package:isar/isar.dart';

part 'base_crawl_status.entity.g.dart';

/// Status of [BaseCrawlEntity]
final class BaseCrawlStatus {
  const BaseCrawlStatus({
    this.message,
    this.code,
  });

  final String? message;
  final String? code;

  @Ignore()
  bool get isDownloadStatus => this is BaseCrawlDownloadStatus;
  @Ignore()
  bool get isSyncStatus => this is BaseCrawlSyncStatus;
}

/// Download Status
@embedded
final class BaseCrawlDownloadStatus extends BaseCrawlStatus {
  const BaseCrawlDownloadStatus({
    this.status = BaseCrawlDownloadStatusEnum.none,
    super.code,
    super.message,
  });

  @enumerated
  final BaseCrawlDownloadStatusEnum status;

  bool get isDownloading => status == BaseCrawlDownloadStatusEnum.downloading;
  bool get isDownloaded => status == BaseCrawlDownloadStatusEnum.downloaded;
  bool get isFailed => status == BaseCrawlDownloadStatusEnum.downloadFailed;

  BaseCrawlDownloadStatus copyWith({
    required BaseCrawlDownloadStatusEnum status,
    String? code,
    String? message,
  }) {
    return BaseCrawlDownloadStatus(
      code: code,
      message: message,
      status: status,
    );
  }
}

/// Sync Status
@embedded
final class BaseCrawlSyncStatus extends BaseCrawlStatus {
  const BaseCrawlSyncStatus({
    this.status = BaseCrawlSyncStatusEnum.syncing,
    super.code,
    super.message,
  });

  @enumerated
  final BaseCrawlSyncStatusEnum status;

  BaseCrawlSyncStatus copyWith({
    required BaseCrawlSyncStatusEnum status,
    String? code,
    String? message,
  }) {
    return BaseCrawlSyncStatus(
      code: code,
      message: message,
      status: status,
    );
  }
}
