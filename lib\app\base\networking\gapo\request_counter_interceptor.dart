import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';

ValueNotifier<int> rxTotalRequests = ValueNotifier(0);
ValueNotifier<int> rxErrorRequests = ValueNotifier(0);

class RequestCounterInterceptor extends Interceptor {
  RequestCounterInterceptor();

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    rxTotalRequests.value++;

    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    rxErrorRequests.value++;

    handler.next(err);
  }
}
