{"editor.inlineSuggest.showToolbar": "onHover", "psi-header.config": {"forceToTop": true, "blankLinesAfter": 1, "spacesBetweenYears": false, "license": "GAPO 2023", "company": "GAPO", "copyrightHolder": "GAPO", "creationDateZero": "asIs", "hostname": "gapowork.vn"}, "psi-header.changes-tracking": {"isActive": true, "modAuthor": "Modified By:", "modDate": "Last Modified:", "modDateFormat": "date", "include": [], "includeGlob": ["**/data/*", "gp_shared/*", "/gp_shared/*", "**/*.service.dart", "**/*.service_v2.dart", "**/*.dto.dart", "**/*.configs.dart", "**/*.module.dart", "**/*.constants.dart", "**/*.component.dart", "**/*.components.dart", "**/*.mapper.dart", "**/mapper.dart", "**/*.interceptor.dart", "**/*.entity.dart", "**/entity/*", "**/main.dart", "**/model/*", "**/model/**/*.dart", "**/models/*", "**/usecase/*", "**/*.usecase.dart", "**.usecase.dart", "**/repository/*.repo.dart", "**/repository/*_repo.dart", "**/*.repo.dart", "**/*_repo.dart", "**/mapper/*_mapper.dart", "**/*_mapper.dart", "**/tus/*_.dart", "**/tus_*.dart", "**/*.route.dart", "**/*.popup.dart", "**/*_codec.dart", "**/*_behavior.dart", "**/*.behavior.dart"], "exclude": ["markdown", "json", "jsonc", "shellscript"], "excludeGlob": [], "autoHeader": "autoSave", "enforceHeader": true, "replace": ["Filename:", "Project", "Copyright"], "updateLicenseVariables": false}, "psi-header.variables": [["manager", "Gapo Flutter Team"], ["projectCreationYear", "2021"]], "psi-header.templates": [{"language": "*", "template": ["Created Date: <<filecreated('dddd, Do MMMM YYYY, HH:mm:ss')>>", "Author: <<author>>", "-----", "Last Modified: <<dateformat('dddd, Do MMMM YYYY HH:mm:ss')>>", "Modified By: <<author>>", "-----", "Copyright (c) <<projectCreationYear>> - <<year>> <<company>>"], "changeLogCaption": "HISTORY", "changeLogHeaderLineCount": 2, "changeLogEntryTemplate": ["", "<<author>>\t<date>\t"], "changeLogNaturalOrder": false, "changeLogFooterLineCount": 0}, {"language": "dart", "mapTo": "dart"}], "files.exclude": {".flutter-plugins": true, ".flutter-plugins-dependencies": true, ".metadata": true, "l10n.yaml": true, "makefile": true, "build": true, "**/.git": true, "**/.DS_Store": true, "**/node_modules": true, "node_modules": true, "**/bricks/**": true, "mason.yaml": true, "brick.yaml": true, "**/.mason/**": true, "mason-lock.json": true, "**/FG/**": true}, "search.exclude": {"/bricks/**": true, "**/bricks/**": true}, "files.watcherExclude": {"/bricks/**": true, "**/bricks/**": true}, "problems.decorations.enabled": false, "dart.analysisExcludedFolders": ["bricks"], "dart.flutterSdkPath": ".fvm/versions/3.13.6", "cmake.ignoreCMakeListsMissing": true}