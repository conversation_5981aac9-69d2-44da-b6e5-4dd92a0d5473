import 'package:json_annotation/json_annotation.dart';

part 'auth_reponse.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake, createToJson: false)
class AuthResponse {
  AuthResponse({
    this.userId,
    this.accessToken,
    this.refreshToken,
    this.salt,
  });

  final int? userId;

  final String? accessToken;

  final String? refreshToken;

  final String? salt;

  bool get hasData =>
      userId != null && accessToken != null && refreshToken != null;

  factory AuthResponse.fromJson(Map<String, dynamic> json) =>
      _$AuthResponseFromJson(json);
}
