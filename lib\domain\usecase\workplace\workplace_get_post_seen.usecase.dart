import 'dart:developer';

import 'package:gp_core/core.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';
import 'package:retry/retry.dart';

// ignore_for_file: public_member_api_docs

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class WorkPlacePostSeenUseCase extends GPBaseFutureUseCase<
    WorkPlacePostSeenInput,
    WorkPlaceListReponse<WorkPlaceUser>> with WorkPlaceFetchAllDataMixin {
  WorkPlacePostSeenUseCase(
    @Named('kWorkPlaceRepository') this._worplaceRepository,
  );

  final WorkPlaceRepository _worplaceRepository;

  @override
  Future<WorkPlaceListReponse<WorkPlaceUser>> buildUseCase(
    WorkPlacePostSeenInput input,
  ) async {
    return retry(
      () async {
        final params = WorkPlaceBaseParams(
          id: input.postId,
          fields: input.fields,
        );

        return await fetchAllData<WorkPlaceUser>(
          params: params,
          loadFunction: _worplaceRepository.feedSeen,
        );
      },
      maxAttempts: 1,
      retryIf: (e) {
        if (e is DioException && e.response?.statusCode == 400) {
          return false;
        }

        return true;
      },
      onRetry: (e) {
        log('ERROR: WorkPlacePostSeenUseCase error -> $e');
      },
    );
  }
}

class WorkPlacePostSeenInput extends GPBaseInput {
  const WorkPlacePostSeenInput({
    required this.postId,
    this.fields = '',
  });

  final String postId;
  final String? fields;
}
