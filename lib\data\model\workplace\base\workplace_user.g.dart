// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workplace_user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkPlaceUser _$WorkPlaceUserFromJson(Map<String, dynamic> json) =>
    WorkPlaceUser(
      id: json['id'] as String,
      name: json['name'] as String?,
      administrator: json['administrator'] as bool?,
      email: json['email'] as String?,
      organization: json['organization'] as String?,
      division: json['division'] as String?,
      department: json['department'] as String?,
      primaryPhone: json['primary_phone'] as String?,
      picture: json['picture'] == null
          ? null
          : UserPicture.fromJson(json['picture'] as Map<String, dynamic>),
      active: json['active'] as bool?,
      cover: json['cover'] == null
          ? null
          : UserCover.fromJson(json['cover'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$WorkPlaceUserToJson(WorkPlaceUser instance) =>
    <String, dynamic>{
      'name': instance.name,
      'id': instance.id,
      'administrator': instance.administrator,
      'email': instance.email,
      'organization': instance.organization,
      'division': instance.division,
      'department': instance.department,
      'primary_phone': instance.primaryPhone,
      'picture': instance.picture,
      'active': instance.active,
      'cover': instance.cover,
    };

UserPicture _$UserPictureFromJson(Map<String, dynamic> json) => UserPicture(
      data: PictureData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$UserPictureToJson(UserPicture instance) =>
    <String, dynamic>{
      'data': instance.data,
    };

PictureData _$PictureDataFromJson(Map<String, dynamic> json) => PictureData(
      url: json['url'] as String?,
      width: (json['width'] as num?)?.toInt(),
      height: (json['height'] as num?)?.toInt(),
    );

Map<String, dynamic> _$PictureDataToJson(PictureData instance) =>
    <String, dynamic>{
      'url': instance.url,
      'width': instance.width,
      'height': instance.height,
    };

UserCover _$UserCoverFromJson(Map<String, dynamic> json) => UserCover(
      source: json['source'] as String?,
      offsetX: (json['offset_x'] as num?)?.toInt(),
      offsetY: (json['offset_y'] as num?)?.toInt(),
    );

Map<String, dynamic> _$UserCoverToJson(UserCover instance) => <String, dynamic>{
      'source': instance.source,
      'offset_x': instance.offsetX,
      'offset_y': instance.offsetY,
    };
