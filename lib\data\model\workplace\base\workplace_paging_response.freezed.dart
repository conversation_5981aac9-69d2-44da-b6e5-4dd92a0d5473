// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'workplace_paging_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

WorkPlacePagingResponse _$WorkPlacePagingResponseFromJson(
    Map<String, dynamic> json) {
  return _WorkPlacePagingResponse.fromJson(json);
}

/// @nodoc
mixin _$WorkPlacePagingResponse {
  WorkPlacePagingCursor? get cursors => throw _privateConstructorUsedError;
  String? get previous => throw _privateConstructorUsedError;
  String? get next => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $WorkPlacePagingResponseCopyWith<WorkPlacePagingResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorkPlacePagingResponseCopyWith<$Res> {
  factory $WorkPlacePagingResponseCopyWith(WorkPlacePagingResponse value,
          $Res Function(WorkPlacePagingResponse) then) =
      _$WorkPlacePagingResponseCopyWithImpl<$Res, WorkPlacePagingResponse>;
  @useResult
  $Res call({WorkPlacePagingCursor? cursors, String? previous, String? next});
}

/// @nodoc
class _$WorkPlacePagingResponseCopyWithImpl<$Res,
        $Val extends WorkPlacePagingResponse>
    implements $WorkPlacePagingResponseCopyWith<$Res> {
  _$WorkPlacePagingResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cursors = freezed,
    Object? previous = freezed,
    Object? next = freezed,
  }) {
    return _then(_value.copyWith(
      cursors: freezed == cursors
          ? _value.cursors
          : cursors // ignore: cast_nullable_to_non_nullable
              as WorkPlacePagingCursor?,
      previous: freezed == previous
          ? _value.previous
          : previous // ignore: cast_nullable_to_non_nullable
              as String?,
      next: freezed == next
          ? _value.next
          : next // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WorkPlacePagingResponseImplCopyWith<$Res>
    implements $WorkPlacePagingResponseCopyWith<$Res> {
  factory _$$WorkPlacePagingResponseImplCopyWith(
          _$WorkPlacePagingResponseImpl value,
          $Res Function(_$WorkPlacePagingResponseImpl) then) =
      __$$WorkPlacePagingResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({WorkPlacePagingCursor? cursors, String? previous, String? next});
}

/// @nodoc
class __$$WorkPlacePagingResponseImplCopyWithImpl<$Res>
    extends _$WorkPlacePagingResponseCopyWithImpl<$Res,
        _$WorkPlacePagingResponseImpl>
    implements _$$WorkPlacePagingResponseImplCopyWith<$Res> {
  __$$WorkPlacePagingResponseImplCopyWithImpl(
      _$WorkPlacePagingResponseImpl _value,
      $Res Function(_$WorkPlacePagingResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cursors = freezed,
    Object? previous = freezed,
    Object? next = freezed,
  }) {
    return _then(_$WorkPlacePagingResponseImpl(
      cursors: freezed == cursors
          ? _value.cursors
          : cursors // ignore: cast_nullable_to_non_nullable
              as WorkPlacePagingCursor?,
      previous: freezed == previous
          ? _value.previous
          : previous // ignore: cast_nullable_to_non_nullable
              as String?,
      next: freezed == next
          ? _value.next
          : next // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable(createToJson: false)
class _$WorkPlacePagingResponseImpl implements _WorkPlacePagingResponse {
  _$WorkPlacePagingResponseImpl({this.cursors, this.previous, this.next});

  factory _$WorkPlacePagingResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorkPlacePagingResponseImplFromJson(json);

  @override
  final WorkPlacePagingCursor? cursors;
  @override
  final String? previous;
  @override
  final String? next;

  @override
  String toString() {
    return 'WorkPlacePagingResponse(cursors: $cursors, previous: $previous, next: $next)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorkPlacePagingResponseImpl &&
            (identical(other.cursors, cursors) || other.cursors == cursors) &&
            (identical(other.previous, previous) ||
                other.previous == previous) &&
            (identical(other.next, next) || other.next == next));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, cursors, previous, next);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WorkPlacePagingResponseImplCopyWith<_$WorkPlacePagingResponseImpl>
      get copyWith => __$$WorkPlacePagingResponseImplCopyWithImpl<
          _$WorkPlacePagingResponseImpl>(this, _$identity);
}

abstract class _WorkPlacePagingResponse implements WorkPlacePagingResponse {
  factory _WorkPlacePagingResponse(
      {final WorkPlacePagingCursor? cursors,
      final String? previous,
      final String? next}) = _$WorkPlacePagingResponseImpl;

  factory _WorkPlacePagingResponse.fromJson(Map<String, dynamic> json) =
      _$WorkPlacePagingResponseImpl.fromJson;

  @override
  WorkPlacePagingCursor? get cursors;
  @override
  String? get previous;
  @override
  String? get next;
  @override
  @JsonKey(ignore: true)
  _$$WorkPlacePagingResponseImplCopyWith<_$WorkPlacePagingResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
