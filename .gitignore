.DS_Store
.dart_tool/

.packages
.pub/

.idea/
.vagrant/
.sconsign.dblite
.svn/
.env

# Flutter/

*.swp
profile

DerivedData/

.generated/
.generated/*

app/windows/flutter/generated_plugin_registrant.cc
app/windows/flutter/generated_plugins.cmake

# macOS
**/Flutter/ephemeral/
**/Pods/
**/macos/Flutter/GeneratedPluginRegistrant.swift
**/macos/Flutter/ephemeral
**/xcuserdata/

# Windows
**/windows/flutter/generated_plugin_registrant.cc
**/windows/flutter/generated_plugin_registrant.h
**/windows/flutter/generated_plugins.cmake

# Linux
**/linux/flutter/generated_plugin_registrant.cc
**/linux/flutter/generated_plugin_registrant.h
**/linux/flutter/generated_plugins.cmake

*.pbxuser
*.mode1v3
*.mode2v3
*.perspectivev3

!default.pbxuser
!default.mode1v3
!default.mode2v3
!default.perspectivev3

xcuserdata

*.moved-aside

*.pyc
# *sync/
Icon?
.tags*

build/
.android/
.ios/
.flutter-plugins
.flutter-plugins-dependencies

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# fvm
.fvm/flutter_sdk
*/.fvm/flutter_sdk

# vscode
# .vscode/settings.json
script/build_staging/last_build_ios_on_this_device.txt

*.g.part

*/locales/gp_localization_generator.csv

app/pubspec_overrides.yaml

*/pubspec_overrides.yaml

*.iml