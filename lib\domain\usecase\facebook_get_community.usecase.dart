/*
 * Created Date: Sunday, 2nd June 2024, 16:26:36
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 4th June 2024 10:04:58
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';

// ignore_for_file: public_member_api_docs

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class FaceBookGetCommunityUseCase
    extends GPBaseFutureUseCase<GPBaseInput, FaceBookCommunityResponse> {
  const FaceBookGetCommunityUseCase(
    @Named('kFaceBookRepository') this._wfaceBookRepository,
  );

  final FaceBookRepository _wfaceBookRepository;

  @override
  Future<FaceBookCommunityResponse> buildUseCase(input) async {
    return _wfaceBookRepository.getCommunity();
  }
}

class FaceBookCommunityInput extends GPBaseInput {
  const FaceBookCommunityInput();
}
