// GENERATED CODE - DO NOT MODIFY MANUALLY 
// **************************************************************************
// Auto generated by <PERSON><PERSON> Developer
// **************************************************************************

import 'package:intl/intl.dart' as intl;

import 'app_localizations.dart';

/// The translations for Vietnamese (`vi`).
class SVi extends S {
  SVi([String locale = 'vi']) : super(locale);

  @override
  String get app_name => 'GapoCrawler';

  @override
  String get download_csv => 'Tải xuống CSV';

  @override
  String get download_csv_title => '• Chọn vị trí lưu trữ file CSV';

  @override
  String get handle_duration => 'Xử lý trong: ';

  @override
  String get crawl_run_time_start_header => 'Thời điểm bắt đầu';

  @override
  String get crawl_run_time_diff => 'Tool đã chạy trong';

  @override
  String get crawl_go_to_details => 'Xem chi tiết';

  @override
  String get crawl_user_init_admin_account => '• Khởi tạo tài khoản Admin ở GapoWork';

  @override
  String get crawl_user_get_wp_users => '• Lấy thông tin người dùng ở WorkPlace';

  @override
  String get crawl_user_save_csv => '• Lưu trữ thông tin người dùng vào file CSV';

  @override
  String total_crawl_files(int total) {
    final intl.NumberFormat totalNumberFormat = intl.NumberFormat.decimalPattern(localeName);
    final String totalString = totalNumberFormat.format(total);

    return '• Tống số dữ liệu cần crawl từ Facebook WorkPlace: $totalString bản ghi';
  }

  @override
  String crawl_user_save_members(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.decimalPattern(localeName);
    final String countString = countNumberFormat.format(count);

    return '• Lưu thông tin $countString người dùng';
  }

  @override
  String crawl_user_signup_users(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.decimalPattern(localeName);
    final String countString = countNumberFormat.format(count);

    return '• Đồng bộ thông tin $countString người dùng ở GapoWork';
  }

  @override
  String crawl_user_invite_to_ws(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.decimalPattern(localeName);
    final String countString = countNumberFormat.format(count);

    return '• Mời $countString người dùng vào workspace ở GapoWork';
  }

  @override
  String crawl_user_invite_user_to_ws(String name) {
    return '• Mời $name vào workspace ở GapoWork';
  }

  @override
  String crawl_user_update_gp_user_info(String name) {
    return '• Cập nhật thông tin $name ở GapoWork';
  }

  @override
  String get crawl_group_get_groups => '• Lấy thông tin nhóm ở WorkPlace';

  @override
  String crawl_group_upload_cover(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.decimalPattern(localeName);
    final String countString = countNumberFormat.format(count);

    return '• Lấy thông tin ảnh cover cho $countString ở WorkPlace';
  }

  @override
  String get crawl_group_member_wp => '• Lấy thông tin thành viên nhóm ở WorkPlace';

  @override
  String crawl_group_member(String group_name) {
    return '• Lấy thông tin thành viên nhóm $group_name';
  }

  @override
  String get crawling_community => 'Lấy thông tin Community ở WorkPlace';

  @override
  String crawl_community(String name) {
    return 'Đồng bộ dữ liệu từ $name ở WorkPlace';
  }

  @override
  String get crawl_group_save_group => '• Lưu trữ dữ liệu nhóm vào bộ nhớ';

  @override
  String get crawl_group_save_csv => '• Lưu trữ thông tin nhóm vào file CSV';

  @override
  String crawl_feed_from_a_groups(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.decimalPattern(localeName);
    final String countString = countNumberFormat.format(count);

    return '• Lấy thông tin bài viết từ $countString nhóm ở WorkPlace';
  }

  @override
  String get crawl_group_add_to_gp => 'Thêm nhóm vào GapoWork';

  @override
  String crawl_group_to_gp(String group_name) {
    return '• Thêm nhóm $group_name vào GapoWork';
  }

  @override
  String get crawl_group_invite_to_gp => 'Thêm thành viên vào nhóm ở GapoWork';

  @override
  String crawl_group_invite(String group_name) {
    return '• Thêm thành viên vào nhóm $group_name vào GapoWork';
  }

  @override
  String crawl_group_change_owner(String group_name) {
    return 'Chuyển quyền owner nhóm $group_name';
  }

  @override
  String crawl_group_leave(String group_name) {
    return 'Admin account rời nhóm $group_name';
  }

  @override
  String crawl_feed_from_a_group(String group_name) {
    return '• Lấy thông tin bài viết trên nhóm \"$group_name\" ở WorkPlace';
  }

  @override
  String crawl_feed_from_a_members(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.decimalPattern(localeName);
    final String countString = countNumberFormat.format(count);

    return '• Lấy thông tin bài viết từ $countString người dùng ở WorkPlace';
  }

  @override
  String crawl_feed_from_member(String user_name) {
    return '• Lấy thông tin bài viết từ người dùng \"$user_name\"';
  }

  @override
  String get crawl_feed_save_feed => '• Lưu trữ dữ liệu bài viết vào bộ nhớ';

  @override
  String crawl_feed_get_attachments(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.decimalPattern(localeName);
    final String countString = countNumberFormat.format(count);

    return '• Tải xuống các tệp đính kèm, hình ảnh, video từ $countString bài viết ở WorkPlace';
  }

  @override
  String crawl_gp_upload_comment_attachments(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.decimalPattern(localeName);
    final String countString = countNumberFormat.format(count);

    return '• Tải lên các tệp đính kèm, hình ảnh, video từ $countString comments ở WorkPlace lên GapoWork';
  }

  @override
  String crawl_feed_get_comments(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.decimalPattern(localeName);
    final String countString = countNumberFormat.format(count);

    return '• Tải xuống các tệp đính kèm, hình ảnh, video từ bình luận của $countString bài viết ở WorkPlace';
  }

  @override
  String crawl_feed_upload_attachments(String feed_name) {
    return '• Tải lên các tệp đính kèm, hình ảnh, video từ bài viết \"$feed_name\" ở WorkPlace';
  }

  @override
  String crawl_feed_upload_comments(String feed_name) {
    return '• Tải lên các tệp đính kèm, hình ảnh, video từ comment của bài viết $feed_name ở WorkPlace';
  }

  @override
  String get crawl_feed_save_feed_csv => '• Lưu trữ thông tin bài viết vào file CSV';

  @override
  String get crawl_thread_get_conversations => '• Lấy thông tin cuộc hội thoại ở WorkPlace';

  @override
  String crawl_thread_get_conversation_from_user(String username) {
    return '• Lấy thông tin cuộc hội thoại của người dùng \"$username\" ở WorkPlace';
  }

  @override
  String crawl_feed_get_reactions(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.decimalPattern(localeName);
    final String countString = countNumberFormat.format(count);

    return '• Lấy thông tin tương tác từ $countString bài viết ở WorkPlace';
  }

  @override
  String crawl_feed_get_reactions_from(String post_id) {
    return '• Lấy thông tin tương tác bài viết \"$post_id\"';
  }

  @override
  String crawl_feed_get_seen(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.decimalPattern(localeName);
    final String countString = countNumberFormat.format(count);

    return '• Lấy thông tin lượt xem từ $countString bài viết ở WorkPlace';
  }

  @override
  String crawl_feed_get_seen_from(String post_id) {
    return '• Lấy thông tin lượt xem bài viết \"$post_id\"';
  }

  @override
  String get crawl_feed_get_attachment => '• Lấy thông tin attachment bài viết';

  @override
  String get crawl_feed_get_cmt => '• Lấy thông tin attachment';

  @override
  String get crawl_feed_get_react => '• Lấy thông tin reaction bài viết';

  @override
  String get crawl_thread_save_thread => '• Lưu trữ dữ liệu hội thoại vào bộ nhớ';

  @override
  String get crawl_thread_get_messages => '• Lấy thông tin tin nhắn ở WorkPlace';

  @override
  String crawl_thread_get_message(String message_id) {
    return '• Lấy thông tin tin nhắn id = \"$message_id\" ở WorkPlace';
  }

  @override
  String get crawl_thread_get_conversation_attachment => '• Tải xuống các tệp đính kèm, hình ảnh, video của tin nhắn từ cuộc hội thoại';

  @override
  String crawl_thread_get_conversation_from_a_thread(String thread_name) {
    return '• Tải xuống các tệp đính kèm, hình ảnh, video của tin nhắn từ cuộc hội thoại \"$thread_name\" ở WorkPlace';
  }

  @override
  String crawl_thread_upload_attachment(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.decimalPattern(localeName);
    final String countString = countNumberFormat.format(count);

    return '• Tải lên GapoWork các tệp đính kèm, hình ảnh, video của tin nhắn từ $countString cuộc hội thoại';
  }

  @override
  String crawl_thread_upload_message_attachment(String thread_name) {
    return '• Tải lên GapoWork các tệp đính kèm, hình ảnh, video của tin nhắn từ cuộc hội thoại \"$thread_name\" ở WorkPlace';
  }

  @override
  String crawl_thread_upload_message_sticker(String thread_name) {
    return '• Tải lên GapoWork các sticker từ cuộc hội thoại \"$thread_name\" ở WorkPlace';
  }

  @override
  String crawl_thread_get_attachment_from_a_message(String message_id) {
    return '• Tải xuống các tệp đính kèm, hình ảnh, video của tin nhắn từ tin nhắn \"$message_id\" ở WorkPlace';
  }

  @override
  String crawl_thread_get_sticker_from_a_message(String message_id) {
    return '• Tải xuống sticker từ tin nhắn \"$message_id\" ở WorkPlace';
  }

  @override
  String get crawl_thread_update_thread => '• Cập nhật tin nhắn vào cuộc hội thoại';

  @override
  String get crawl_thread_save_message => '• Lưu trữ dữ liệu tin nhắn vào bộ nhớ';

  @override
  String get crawl_thread_save_thread_csv => '• Lưu trữ thông tin cuộc hội thoại vào file CSV';

  @override
  String get crawl_thread_save_message_csv => '• Lưu trữ thông tin tin nhắn vào file CSV';
}
