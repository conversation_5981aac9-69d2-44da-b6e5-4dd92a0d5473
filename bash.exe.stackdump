Stack trace:
Frame         Function      Args
0007FFFF9AE0  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF89E0) msys-2.0.dll+0x1FEBA
0007FFFF9AE0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9DB8) msys-2.0.dll+0x67F9
0007FFFF9AE0  000210046832 (000210285FF9, 0007FFFF9998, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9AE0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF9AE0  0002100690B4 (0007FFFF9AF0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF9DC0  00021006A49D (0007FFFF9AF0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFACA780000 ntdll.dll
7FFAC8DE0000 KERNEL32.DLL
7FFAC8170000 KERNELBASE.dll
7FFAC8570000 USER32.dll
7FFAC7E40000 win32u.dll
7FFACA600000 GDI32.dll
7FFAC7D00000 gdi32full.dll
7FFAC7C50000 msvcp_win.dll
7FFAC7910000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFAC8B90000 advapi32.dll
7FFAC8AB0000 msvcrt.dll
7FFAC9CB0000 sechost.dll
7FFAC9F90000 RPCRT4.dll
7FFAC7000000 CRYPTBASE.DLL
7FFAC7BB0000 bcryptPrimitives.dll
7FFAC8740000 IMM32.DLL
