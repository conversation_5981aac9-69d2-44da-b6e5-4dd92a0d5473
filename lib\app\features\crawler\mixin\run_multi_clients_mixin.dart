import 'dart:io';

import 'package:get_it/get_it.dart';
import 'package:gp_fbwp_crawler/app/constant/app_constant.dart';
import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:gp_fbwp_crawler/domain/entity/entity.dart';
import 'package:path_provider/path_provider.dart';

mixin RunMultiClients {
  final localService =
      GetIt.I<WorkPlaceLocalService>(instanceName: 'kWorkPlaceLocalService');
  final computerId = AppConstants.computerId;
  Future<List<WorkPlaceCommunityMemberEntity>> multiClientGetMembers() async {
    if (AppConstants.runInMultiClients == false) {
      return await localService.getAllMembers();
    }
    final document = await getApplicationDocumentsDirectory();
    final path = "${document.path}/multi-client/$computerId/users.txt";
    if (File(path).existsSync()) {
      print('Reading users from file $path...');
      final file = File(path).readAsStringSync();
      final userIds = file.split(',');
      final List<WorkPlaceCommunityMemberEntity> memberEntities = userIds
          .map<WorkPlaceCommunityMemberEntity>(
              (e) => WorkPlaceCommunityMemberEntity(id: e))
          .toList();
      return memberEntities;
    } else {
      print('File not found: $path');
      return [];
    }
  }

  Future<List<WorkPlaceGroupEntity>> multiClientGetGroups() async {
    final allGroups = await localService.getAllGroups();
    if (AppConstants.runInMultiClients == false) {
      return allGroups;
    }
    final document = await getApplicationDocumentsDirectory();
    final path = "${document.path}/multi-client/$computerId/groups.txt";
    if (File(path).existsSync()) {
      print('Reading groups from file $path...');
      final file = File(path).readAsStringSync();
      final groupIds = file.split(',');
      final groupEntities =
          allGroups.where((element) => groupIds.contains(element.id)).toList();
      return groupEntities;
    } else {
      print('File not found: $path');
      return [];
    }
  }

  Future<List<WorkPlaceConversationEntity>> multiClientGetThreads() async {
    final allThreads = await localService.getAllThreads();
    if (AppConstants.runInMultiClients == false) {
      return allThreads;
    }
    final document = await getApplicationDocumentsDirectory();
    final path = "${document.path}/multi-client/$computerId/threads.txt";
    if (File(path).existsSync()) {
      print('Reading threads from file $path...');
      final file = File(path).readAsStringSync();
      final threadIds = file.split(',');
      final threadEntities =
          allThreads.where((element) => threadIds.contains(element.id)).toList();
      return threadEntities;
    } else {
      print('File not found: $path');
      return [];
    }
  }
}
