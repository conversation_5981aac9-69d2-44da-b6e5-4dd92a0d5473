// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'go_router.route.dart';

// **************************************************************************
// GoRouterGenerator
// **************************************************************************

List<RouteBase> get $appRoutes => [
      $splashRouteData,
      $homeRouteData,
      $crawlRouteData,
    ];

RouteBase get $splashRouteData => GoRouteData.$route(
      path: '/',
      factory: $SplashRouteDataExtension._fromState,
    );

extension $SplashRouteDataExtension on SplashRouteData {
  static SplashRouteData _fromState(GoRouterState state) =>
      const SplashRouteData();

  String get location => GoRouteData.$location(
        '/',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $homeRouteData => GoRouteData.$route(
      path: '/home',
      factory: $HomeRouteDataExtension._fromState,
    );

extension $HomeRouteDataExtension on HomeRouteData {
  static HomeRouteData _fromState(GoRouterState state) => const HomeRouteData();

  String get location => GoRouteData.$location(
        '/home',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $crawlRouteData => GoRouteData.$route(
      path: '/crawl',
      factory: $CrawlRouteDataExtension._fromState,
    );

extension $CrawlRouteDataExtension on CrawlRouteData {
  static CrawlRouteData _fromState(GoRouterState state) =>
      const CrawlRouteData();

  String get location => GoRouteData.$location(
        '/crawl',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}
