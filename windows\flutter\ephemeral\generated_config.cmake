# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\fvm\\versions\\3.13.6" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\AndroidStudioProjects\\gapoflutter-crawler" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 0 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Users\\<USER>\\fvm\\versions\\3.13.6"
  "PROJECT_DIR=C:\\Users\\<USER>\\AndroidStudioProjects\\gapoflutter-crawler"
  "FLUTTER_ROOT=C:\\Users\\<USER>\\fvm\\versions\\3.13.6"
  "FLUTTER_EPHEMERAL_DIR=C:\\Users\\<USER>\\AndroidStudioProjects\\gapoflutter-crawler\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\Users\\<USER>\\AndroidStudioProjects\\gapoflutter-crawler"
  "FLUTTER_TARGET=C:\\Users\\<USER>\\AndroidStudioProjects\\gapoflutter-crawler\\lib\\main.staging.dart"
  "DART_DEFINES=RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==,RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC9hNzk0Y2YyNjgxYzZjOWZlN2IyNjBlMGU4NGRlOTYyOThkYzljMThiLw=="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\Users\\<USER>\\AndroidStudioProjects\\gapoflutter-crawler\\.dart_tool\\package_config.json"
)
