// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workplace_sticker.entity.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetWorkPlaceStickerEntityCollection on Isar {
  IsarCollection<WorkPlaceStickerEntity> get workPlaceStickerEntitys =>
      this.collection();
}

const WorkPlaceStickerEntitySchema = CollectionSchema(
  name: r'WorkPlaceStickerEntity',
  id: -4692467659169697260,
  properties: {
    r'crawlType': PropertySchema(
      id: 0,
      name: r'crawlType',
      type: IsarType.byte,
      enumMap: _WorkPlaceStickerEntitycrawlTypeEnumValueMap,
    ),
    r'gpLink': PropertySchema(
      id: 1,
      name: r'gpLink',
      type: IsarType.string,
    ),
    r'id': PropertySchema(
      id: 2,
      name: r'id',
      type: IsarType.string,
    ),
    r'localFilePath': PropertySchema(
      id: 3,
      name: r'localFilePath',
      type: IsarType.string,
    ),
    r'uploadResponse': PropertySchema(
      id: 4,
      name: r'uploadResponse',
      type: IsarType.object,
      target: r'UploadResponseEntity',
    ),
    r'url': PropertySchema(
      id: 5,
      name: r'url',
      type: IsarType.string,
    )
  },
  estimateSize: _workPlaceStickerEntityEstimateSize,
  serialize: _workPlaceStickerEntitySerialize,
  deserialize: _workPlaceStickerEntityDeserialize,
  deserializeProp: _workPlaceStickerEntityDeserializeProp,
  idName: r'dbId',
  indexes: {},
  links: {},
  embeddedSchemas: {
    r'UploadResponseEntity': UploadResponseEntitySchema,
    r'UploadFileURLResponseEntity': UploadFileURLResponseEntitySchema
  },
  getId: _workPlaceStickerEntityGetId,
  getLinks: _workPlaceStickerEntityGetLinks,
  attach: _workPlaceStickerEntityAttach,
  version: '3.1.0+1',
);

int _workPlaceStickerEntityEstimateSize(
  WorkPlaceStickerEntity object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.gpLink;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  bytesCount += 3 + object.id.length * 3;
  {
    final value = object.localFilePath;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.uploadResponse;
    if (value != null) {
      bytesCount += 3 +
          UploadResponseEntitySchema.estimateSize(
              value, allOffsets[UploadResponseEntity]!, allOffsets);
    }
  }
  {
    final value = object.url;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _workPlaceStickerEntitySerialize(
  WorkPlaceStickerEntity object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeByte(offsets[0], object.crawlType.index);
  writer.writeString(offsets[1], object.gpLink);
  writer.writeString(offsets[2], object.id);
  writer.writeString(offsets[3], object.localFilePath);
  writer.writeObject<UploadResponseEntity>(
    offsets[4],
    allOffsets,
    UploadResponseEntitySchema.serialize,
    object.uploadResponse,
  );
  writer.writeString(offsets[5], object.url);
}

WorkPlaceStickerEntity _workPlaceStickerEntityDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = WorkPlaceStickerEntity(
    crawlType: _WorkPlaceStickerEntitycrawlTypeValueEnumMap[
            reader.readByteOrNull(offsets[0])] ??
        GPBaseCrawlType.sticker,
    id: reader.readStringOrNull(offsets[2]) ?? '',
    url: reader.readStringOrNull(offsets[5]),
  );
  object.gpLink = reader.readStringOrNull(offsets[1]);
  object.localFilePath = reader.readStringOrNull(offsets[3]);
  object.uploadResponse = reader.readObjectOrNull<UploadResponseEntity>(
    offsets[4],
    UploadResponseEntitySchema.deserialize,
    allOffsets,
  );
  return object;
}

P _workPlaceStickerEntityDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (_WorkPlaceStickerEntitycrawlTypeValueEnumMap[
              reader.readByteOrNull(offset)] ??
          GPBaseCrawlType.sticker) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    case 2:
      return (reader.readStringOrNull(offset) ?? '') as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    case 4:
      return (reader.readObjectOrNull<UploadResponseEntity>(
        offset,
        UploadResponseEntitySchema.deserialize,
        allOffsets,
      )) as P;
    case 5:
      return (reader.readStringOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _WorkPlaceStickerEntitycrawlTypeEnumValueMap = {
  'user': 0,
  'group': 1,
  'thread': 2,
  'message': 3,
  'feed': 4,
  'userFeed': 5,
  'comment': 6,
  'attachment': 7,
  'community': 8,
  'sticker': 9,
  'reaction': 10,
  'commentReaction': 11,
  'groupMember': 12,
  'postSeen': 13,
  'messageAttachment': 14,
};
const _WorkPlaceStickerEntitycrawlTypeValueEnumMap = {
  0: GPBaseCrawlType.user,
  1: GPBaseCrawlType.group,
  2: GPBaseCrawlType.thread,
  3: GPBaseCrawlType.message,
  4: GPBaseCrawlType.feed,
  5: GPBaseCrawlType.userFeed,
  6: GPBaseCrawlType.comment,
  7: GPBaseCrawlType.attachment,
  8: GPBaseCrawlType.community,
  9: GPBaseCrawlType.sticker,
  10: GPBaseCrawlType.reaction,
  11: GPBaseCrawlType.commentReaction,
  12: GPBaseCrawlType.groupMember,
  13: GPBaseCrawlType.postSeen,
  14: GPBaseCrawlType.messageAttachment,
};

Id _workPlaceStickerEntityGetId(WorkPlaceStickerEntity object) {
  return object.dbId ?? Isar.autoIncrement;
}

List<IsarLinkBase<dynamic>> _workPlaceStickerEntityGetLinks(
    WorkPlaceStickerEntity object) {
  return [];
}

void _workPlaceStickerEntityAttach(
    IsarCollection<dynamic> col, Id id, WorkPlaceStickerEntity object) {}

extension WorkPlaceStickerEntityQueryWhereSort
    on QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QWhere> {
  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QAfterWhere>
      anyDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension WorkPlaceStickerEntityQueryWhere on QueryBuilder<
    WorkPlaceStickerEntity, WorkPlaceStickerEntity, QWhereClause> {
  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterWhereClause> dbIdEqualTo(Id dbId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: dbId,
        upper: dbId,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterWhereClause> dbIdNotEqualTo(Id dbId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: dbId, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: dbId, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: dbId, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: dbId, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterWhereClause> dbIdGreaterThan(Id dbId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: dbId, includeLower: include),
      );
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterWhereClause> dbIdLessThan(Id dbId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: dbId, includeUpper: include),
      );
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterWhereClause> dbIdBetween(
    Id lowerDbId,
    Id upperDbId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerDbId,
        includeLower: includeLower,
        upper: upperDbId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension WorkPlaceStickerEntityQueryFilter on QueryBuilder<
    WorkPlaceStickerEntity, WorkPlaceStickerEntity, QFilterCondition> {
  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> crawlTypeEqualTo(GPBaseCrawlType value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> crawlTypeGreaterThan(
    GPBaseCrawlType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> crawlTypeLessThan(
    GPBaseCrawlType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> crawlTypeBetween(
    GPBaseCrawlType lower,
    GPBaseCrawlType upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'crawlType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> dbIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'dbId',
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> dbIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'dbId',
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> dbIdEqualTo(Id? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> dbIdGreaterThan(
    Id? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> dbIdLessThan(
    Id? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> dbIdBetween(
    Id? lower,
    Id? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'dbId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> gpLinkIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'gpLink',
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> gpLinkIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'gpLink',
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> gpLinkEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'gpLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> gpLinkGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'gpLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> gpLinkLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'gpLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> gpLinkBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'gpLink',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> gpLinkStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'gpLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> gpLinkEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'gpLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
          QAfterFilterCondition>
      gpLinkContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'gpLink',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
          QAfterFilterCondition>
      gpLinkMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'gpLink',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> gpLinkIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'gpLink',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> gpLinkIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'gpLink',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> idEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> idGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> idLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> idBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> idStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> idEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
          QAfterFilterCondition>
      idContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
          QAfterFilterCondition>
      idMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'id',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> idIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> idIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> localFilePathIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'localFilePath',
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> localFilePathIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'localFilePath',
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> localFilePathEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localFilePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> localFilePathGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'localFilePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> localFilePathLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'localFilePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> localFilePathBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'localFilePath',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> localFilePathStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'localFilePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> localFilePathEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'localFilePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
          QAfterFilterCondition>
      localFilePathContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'localFilePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
          QAfterFilterCondition>
      localFilePathMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'localFilePath',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> localFilePathIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localFilePath',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> localFilePathIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'localFilePath',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> uploadResponseIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'uploadResponse',
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> uploadResponseIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'uploadResponse',
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> urlIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'url',
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> urlIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'url',
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> urlEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> urlGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> urlLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> urlBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'url',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> urlStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> urlEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
          QAfterFilterCondition>
      urlContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'url',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
          QAfterFilterCondition>
      urlMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'url',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> urlIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'url',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
      QAfterFilterCondition> urlIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'url',
        value: '',
      ));
    });
  }
}

extension WorkPlaceStickerEntityQueryObject on QueryBuilder<
    WorkPlaceStickerEntity, WorkPlaceStickerEntity, QFilterCondition> {
  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity,
          QAfterFilterCondition>
      uploadResponse(FilterQuery<UploadResponseEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.object(q, r'uploadResponse');
    });
  }
}

extension WorkPlaceStickerEntityQueryLinks on QueryBuilder<
    WorkPlaceStickerEntity, WorkPlaceStickerEntity, QFilterCondition> {}

extension WorkPlaceStickerEntityQuerySortBy
    on QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QSortBy> {
  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QAfterSortBy>
      sortByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QAfterSortBy>
      sortByCrawlTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QAfterSortBy>
      sortByGpLink() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpLink', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QAfterSortBy>
      sortByGpLinkDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpLink', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QAfterSortBy>
      sortById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QAfterSortBy>
      sortByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QAfterSortBy>
      sortByLocalFilePath() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localFilePath', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QAfterSortBy>
      sortByLocalFilePathDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localFilePath', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QAfterSortBy>
      sortByUrl() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'url', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QAfterSortBy>
      sortByUrlDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'url', Sort.desc);
    });
  }
}

extension WorkPlaceStickerEntityQuerySortThenBy on QueryBuilder<
    WorkPlaceStickerEntity, WorkPlaceStickerEntity, QSortThenBy> {
  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QAfterSortBy>
      thenByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QAfterSortBy>
      thenByCrawlTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QAfterSortBy>
      thenByDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dbId', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QAfterSortBy>
      thenByDbIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dbId', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QAfterSortBy>
      thenByGpLink() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpLink', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QAfterSortBy>
      thenByGpLinkDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpLink', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QAfterSortBy>
      thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QAfterSortBy>
      thenByLocalFilePath() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localFilePath', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QAfterSortBy>
      thenByLocalFilePathDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localFilePath', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QAfterSortBy>
      thenByUrl() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'url', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QAfterSortBy>
      thenByUrlDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'url', Sort.desc);
    });
  }
}

extension WorkPlaceStickerEntityQueryWhereDistinct
    on QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QDistinct> {
  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QDistinct>
      distinctByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'crawlType');
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QDistinct>
      distinctByGpLink({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'gpLink', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QDistinct>
      distinctById({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'id', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QDistinct>
      distinctByLocalFilePath({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'localFilePath',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, WorkPlaceStickerEntity, QDistinct>
      distinctByUrl({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'url', caseSensitive: caseSensitive);
    });
  }
}

extension WorkPlaceStickerEntityQueryProperty on QueryBuilder<
    WorkPlaceStickerEntity, WorkPlaceStickerEntity, QQueryProperty> {
  QueryBuilder<WorkPlaceStickerEntity, int, QQueryOperations> dbIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'dbId');
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, GPBaseCrawlType, QQueryOperations>
      crawlTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'crawlType');
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, String?, QQueryOperations>
      gpLinkProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'gpLink');
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, String, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, String?, QQueryOperations>
      localFilePathProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'localFilePath');
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, UploadResponseEntity?, QQueryOperations>
      uploadResponseProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'uploadResponse');
    });
  }

  QueryBuilder<WorkPlaceStickerEntity, String?, QQueryOperations>
      urlProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'url');
    });
  }
}
