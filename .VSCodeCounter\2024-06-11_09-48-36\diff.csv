"filename", "language", "<PERSON>AM<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "comment", "blank", "total"
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/app_config/bloc/app_config_bloc.dart", "Dart", 0, 28, 0, 0, 4, 32
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/app_config/bloc/app_config_event.dart", "Dart", 0, 5, 0, 0, 1, 6
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/app_config/bloc/app_config_state.freezed.dart", "Dart", 0, 2, 0, 0, 0, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/base/networking/logger_inteceptor.dart", "Dart", 0, 75, 0, 7, 18, 100
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/base/networking/networking.dart", "<PERSON><PERSON>", 0, 1, 0, 0, 0, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/base/networking/workplace_auth_inteceptor.dart", "Dart", 0, 2, 0, 0, 0, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/constant/app_constant.dart", "Dart", 0, 7, 0, 0, 2, 9
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/constant/constant.dart", "Dart", 0, 2, 0, 0, 0, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/constant/fb_wp_url.constants.dart", "Dart", 0, 4, 0, 0, 1, 5
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/constant/gapo_url.constants.dart", "Dart", 0, 6, 0, 11, 4, 21
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/bloc/bloc.dart", "Dart", 0, 3, 0, 0, 1, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/bloc/crawl_bloc.dart", "Dart", 0, 220, 0, 14, 33, 267
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/bloc/crawl_event.dart", "Dart", 0, 11, 0, 0, 4, 15
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/bloc/crawl_state.dart", "Dart", 0, 30, 0, 1, 6, 37
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/bloc/crawl_state.freezed.dart", "Dart", 0, 256, 0, 23, 44, 323
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/crawl.main.page.dart", "Dart", 0, 84, 0, 2, 0, 86
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/app/features/crawler/crawler.dart", "Dart", 0, 1, 0, 0, 0, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/local/workplace_local.service.dart", "Dart", 0, 2, 0, 0, -1, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/auth.service.dart", "Dart", 0, 21, 0, 9, 5, 35
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/auth.service.g.dart", "Dart", 0, 60, 0, 5, 13, 78
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/facebook.service.dart", "Dart", 0, 20, 0, 10, 6, 36
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/facebook.service.g.dart", "Dart", 0, 60, 0, 5, 13, 78
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/remote.dart", "Dart", 0, 2, 0, 0, 0, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/workplace.service.dart", "Dart", 0, 27, 0, 0, 5, 32
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/data_source/remote/workplace.service.g.dart", "Dart", 0, 166, 0, 0, 5, 171
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/datetime_converter.dart", "Dart", 0, 11, 0, 0, 3, 14
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/facebook/community_response.dart", "Dart", 0, 16, 0, 0, 5, 21
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/facebook/community_response.g.dart", "Dart", 0, 8, 0, 4, 4, 16
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/facebook/facebook.dart", "Dart", 0, 1, 0, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/auth_reponse.dart", "Dart", 0, 15, 0, 0, 7, 22
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/auth_reponse.g.dart", "Dart", 0, 6, 0, 4, 4, 14
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/gpw/gpw.dart", "Dart", 0, 1, 0, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/model.dart", "Dart", 0, 3, 0, 0, 0, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/base/base.dart", "Dart", 0, 1, 0, 0, 0, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/base/workplace_generic_data_converter.dart", "Dart", 0, 12, 0, 0, 0, 12
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/base/workplace_list_response.g.dart", "Dart", 0, 1, 0, 0, 0, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/base/workplace_paging_response.dart", "Dart", 0, 4, 0, 0, 0, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/base/workplace_paging_response.g.dart", "Dart", 0, 4, 0, 0, 0, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/base/workplace_user.dart", "Dart", 0, 31, 0, 5, 11, 47
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/base/workplace_user.g.dart", "Dart", 0, 1398, 0, 9, 115, 1522
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/community/community.dart", "Dart", 0, 1, 0, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/community/workplace_community_members_response.dart", "Dart", 0, 30, 0, 3, 9, 42
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/community/workplace_community_members_response.g.dart", "Dart", 0, 15, 0, 4, 4, 23
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/enums/enums.dart", "Dart", 0, 1, 0, 0, 1, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/enums/workplace_enums.dart", "Dart", 0, 16, 0, 0, 4, 20
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/group.dart", "Dart", 0, 2, 0, 0, 0, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_feeds_response.dart", "Dart", 0, 29, 0, 2, 7, 38
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_feeds_response.g.dart", "Dart", 0, 37, 0, 4, 6, 47
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_members_response.dart", "Dart", 0, 29, 0, 3, 9, 41
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_members_response.g.dart", "Dart", 0, 15, 0, 4, 4, 23
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_response.dart", "Dart", 0, 40, 0, 0, 3, 43
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/group/workplace_group_response.g.dart", "Dart", 0, 688, 0, 5, 58, 751
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/post/post.dart", "Dart", 0, 2, 0, 0, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/post/workplace_post_attachments_response.dart", "Dart", 0, 58, 0, 0, 12, 70
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/post/workplace_post_attachments_response.g.dart", "Dart", 0, 53, 0, 4, 9, 66
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/post/workplace_post_comments_response.dart", "Dart", 0, 19, 0, 0, 5, 24
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/post/workplace_post_comments_response.g.dart", "Dart", 0, 17, 0, 4, 5, 26
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/model/workplace/workplace.dart", "Dart", 0, 3, 0, 0, 0, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/repository/facebook_repo_impl.dart", "Dart", 0, 16, 0, 0, 4, 20
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/repository/repository.dart", "Dart", 0, 1, 0, 0, 0, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/data/repository/workplace_repo_impl.dart", "Dart", 0, 26, 0, 0, 5, 31
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/component/app.component.config.dart", "Dart", 0, 124, 0, 0, 1, 125
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/modules/auth.module.dart", "Dart", 0, 17, 0, 11, 6, 34
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/modules/client.module.dart", "Dart", 0, 10, 0, -9, 2, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/modules/database.module.dart", "Dart", 0, 5, 0, 0, 0, 5
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/modules/modules.dart", "Dart", 0, 2, 0, 0, 0, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/di/modules/url.module.dart", "Dart", 0, 11, 0, 0, 3, 14
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/app/app_config.entity.dart", "Dart", 0, 11, 0, 0, 1, 12
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/base/log/base_crawl_log.entity.g.dart", "Dart", 0, 2, 0, 0, 0, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/entity.dart", "Dart", 0, 2, 0, 0, 0, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/enums/base_crawl_type.dart", "Dart", 0, 1, 0, 1, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gapo.dart", "Dart", 0, 3, 0, 0, 1, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gp_auth.dart", "Dart", 0, 10, 0, 0, 4, 14
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gp_auth_params.dart", "Dart", 0, 17, 0, 1, 4, 22
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gp_auth_params.freezed.dart", "Dart", 0, 202, 0, 15, 23, 240
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gp_auth_params.g.dart", "Dart", 0, 9, 0, 4, 4, 17
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gpuser.dart", "Dart", 0, 25, 0, 0, 8, 33
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/gapo/gpuser.g.dart", "Dart", 0, 1350, 0, 9, 147, 1506
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace.dart", "Dart", 0, 7, 0, 0, 1, 8
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_attachment.entity.dart", "Dart", 0, 46, 0, 12, 18, 76
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_attachment.entity.g.dart", "Dart", 0, 2060, 0, 14, 165, 2239
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_comment.entity.dart", "Dart", 0, 16, 0, 9, 5, 30
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_comment.entity.g.dart", "Dart", 0, 501, 0, 6, 42, 549
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_community_member.entity.dart", "Dart", 0, 28, 0, 12, 10, 50
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_community_member.entity.g.dart", "Dart", 0, 1960, 0, 6, 192, 2158
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_group.entity.dart", "Dart", 0, 40, 0, 9, 7, 56
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_group.entity.g.dart", "Dart", 0, 2335, 0, 11, 215, 2561
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_group_feed.entity.dart", "Dart", 0, 30, 0, 10, 8, 48
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_group_feed.entity.g.dart", "Dart", 0, 1784, 0, 6, 160, 1950
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_group_member.entity.dart", "Dart", 0, 30, 0, 12, 10, 52
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_group_member.entity.g.dart", "Dart", 0, 2143, 0, 6, 210, 2359
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_user.entity.dart", "Dart", 0, 27, 0, 12, 9, 48
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/entity/workplace/workplace_user.entity.g.dart", "Dart", 0, 1376, 0, 6, 112, 1494
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/repository/facebook_repo.dart", "Dart", 0, 4, 0, 1, 2, 7
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/repository/gapo_repo.dart", "Dart", 0, 17, 0, 11, 10, 38
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/repository/repository.dart", "Dart", 0, 2, 0, 0, 0, 2
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/repository/workplace_repo.dart", "Dart", 0, 10, 0, 0, 5, 15
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/facebook_get_community.usecase.dart", "Dart", 0, 19, 0, 10, 7, 36
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/usecase.dart", "Dart", 0, 6, 0, 0, 0, 6
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/workplace_get_all_groups.usecase.dart", "Dart", 0, 3, 0, 0, 0, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/workplace_get_community_members.usecase.dart", "Dart", 0, 28, 0, 1, 7, 36
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/workplace_get_group_feeds.usecase.dart", "Dart", 0, 28, 0, 1, 7, 36
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/workplace_get_group_members.usecase.dart", "Dart", 0, 27, 0, 1, 7, 35
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/workplace_get_post_attachments.usecase.dart", "Dart", 0, 27, 0, 1, 7, 35
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/domain/usecase/workplace_get_post_comments.usecase.dart", "Dart", 0, 27, 0, 1, 7, 35
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/l10n/app_en.arb", "JSON", 0, 0, 1, 0, 0, 1
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/entity/entity.dart", "Dart", 0, 2, 0, 0, 1, 3
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/entity/workplace_entity_mapper.auto_mappr.dart", "Dart", 0, 660, 0, 51, 41, 752
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/entity/workplace_entity_mapper.dart", "Dart", 0, 101, 0, 12, 12, 125
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/gp_mapper.auto_mappr.dart", "Dart", 0, 188, 0, 60, 28, 276
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/gp_mapper.dart", "Dart", 0, 61, 0, 13, 10, 84
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/lib/mapper/mapper.dart", "Dart", 0, 3, 0, 0, 1, 4
"/Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler/pubspec.yaml", "YAML", 2, 0, 0, 0, 0, 2
"Total", "-", 2, 19102, 1, 468, 1983, 21556