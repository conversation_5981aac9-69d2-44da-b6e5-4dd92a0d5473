// ignore_for_file: library_private_types_in_public_api

import 'dart:developer';
import 'dart:io';
import 'package:async_task/async_task_extension.dart';
import 'package:circular_countdown_timer/circular_countdown_timer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_dio_log/gp_dio_log.dart';
import 'package:gp_fbwp_crawler/app/features/crawler/crawler.dart';
import 'package:gp_fbwp_crawler/domain/entity/gapo/gp_migrate.dart';
import 'package:gp_fbwp_crawler/helpers/list.dart';
import 'package:intl/intl.dart';
import 'package:isar/isar.dart';
import 'package:path_provider/path_provider.dart';
import 'package:talker_flutter/talker_flutter.dart';
import 'package:flutter/services.dart' show rootBundle;

import '../../../data/data_source/local/workplace_local.service.dart';
import '../../../data/model/workplace/enums/workplace_enums.dart';
import '../../../domain/domain.dart';
import '../../../helpers/csv.dart';
import '../../../helpers/file_helper.dart';
import '../../../mapper/gp_mapper.dart';
import '../../base/networking/gapo/request_counter_interceptor.dart';
import '../../constant/app_constant.dart';
import '../home/<USER>';

const _tickSeconds = 10;
const _itemWidth = 350.0;

class CrawlItemsPage extends StatelessWidget {
  CrawlItemsPage({super.key});

  final _Action action = _Action();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _TopWidget(
              action: action,
            ),
            _BottomWidget(
              action: action,
            )
          ],
        ),
      ),
    );
  }
}

class _TopWidget extends StatelessWidget {
  const _TopWidget({
    required this.action,
  });

  final _Action action;

  void goToDetails(BuildContext context, _CrawlType e) {
    final queueBlocs = RepositoryProvider.of<List<CrawlQueueBloc>>(context);

    CrawlQueueBloc? queueBloc;

    switch (e) {
      case _CrawlType.member:
        queueBloc = queueBlocs.firstWhereOrNull((p0) => p0 is CrawlMemberBloc);
        break;
      case _CrawlType.group:
        queueBloc = queueBlocs.firstWhereOrNull((p0) => p0 is CrawlGroupBloc);
        break;
      case _CrawlType.feed:
        queueBloc = queueBlocs.firstWhereOrNull((p0) => p0 is CrawlFeedBloc);
        break;
      case _CrawlType.conversation:
        queueBloc = queueBlocs.firstWhereOrNull((p0) => p0 is CrawlThreadBloc);
        break;
      default:
    }

    if (queueBloc != null) {
      Navigator.of(context).push(
        MaterialPageRoute<void>(
          builder: (BuildContext context) => CrawlHeader(
            bloc: queueBloc!,
            headerStr: queueBloc.displayName,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        StreamBuilder(
          stream: Stream.periodic(const Duration(seconds: _tickSeconds)),
          builder: (context, snapshot) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Padding(
                  padding: EdgeInsets.only(left: 20),
                  child: _CountDownTimerWidget(),
                ),
                ..._CrawlType.values.map((e) {
                  final childWidget = Padding(
                    padding: const EdgeInsets.only(top: 6),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(width: _itemWidth, child: Text('${e.hint}:')),
                        SizedBox(
                          width: 270,
                          child: FutureBuilder(
                            future: action.countByCrawlType(e),
                            builder: (context, snapshot) {
                              return Text(
                                '${snapshot.data}',
                              );
                            },
                          ),
                        ),
                        if (e.hasDetails)
                          TextButton(
                            onPressed: () {
                              goToDetails(context, e);
                            },
                            child: Text(
                              l10n.crawl_go_to_details,
                            ),
                          ),
                      ],
                    ),
                  );

                  if (e.isHeader) {
                    return Column(
                      children: [const Divider(), childWidget],
                    );
                  }

                  return childWidget;
                }),
              ],
            );
          },
        )
      ],
    );
  }
}

class _BottomWidget extends StatelessWidget {
  const _BottomWidget({
    required this.action,
  });

  final _Action action;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Divider(),
        Row(
          children: [
            const SizedBox(width: _itemWidth, child: Text('Total Requests:')),
            ValueListenableBuilder(
              valueListenable: rxTotalRequests,
              builder: (context, value, child) {
                return Text('$value');
              },
            ),
          ],
        ),
        Row(
          children: [
            const SizedBox(
                width: _itemWidth, child: Text('total Error Requests:')),
            ValueListenableBuilder(
              valueListenable: rxErrorRequests,
              builder: (context, value, child) {
                return Text('$value');
              },
            ),
          ],
        ),
        const SizedBox(height: 10),
        Row(
          children: [
            TextButton(
                onPressed: action.migrateInsertedAt,
                child: const Text('Migrate insertedAt')),
            TextButton(
                onPressed: action.saveGroupCSV,
                child: const Text('Save group to CSV')),
            TextButton(
                onPressed: action.saveFeedCSV,
                child: const Text('Save feed to CSV')),
            TextButton(
                onPressed: action.saveConversationCSV,
                child: const Text('Save conversation to CSV')),
            TextButton(
                onPressed: action.saveMessageCSV,
                child: const Text('Save message to CSV')),
            TextButton(
                onPressed: action.saveSplitMessageCSV,
                child: const Text('Save thread and message split CSV')),
            TextButton(
                onPressed: action.importWorkplaceData,
                child: const Text('Import Workplace Data')),
            TextButton(
                onPressed: action.processGroupedSummary,
                child: const Text('Group Summary')),
            TextButton(
                onPressed: action.processUsersSummary,
                child: const Text('Users Summary')),
          ],
        ),
        Row(
          children: [
            TextButton(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (conext) {
                      return const HttpLogListWidget();
                    },
                  ),
                );
              },
              child: const Text('Go to DioLog'),
            ),
            TextButton(
                onPressed: action.splitUserAndGroup,
                child: const Text('Split user and group')),
            TextButton(
                onPressed: action.mergeDatabase,
                child: const Text('Merge database')),
            TextButton(
                onPressed: action.processUsersSummaryAll,
                child: const Text('Summary all')),
          ],
        ),
      ],
    );
  }
}

enum _CrawlType {
  member('Tổng số member', hasDetails: true),
  group('Tổng số group', hasDetails: true),
  feed('Tổng số bài post', isHeader: true, hasDetails: true),
  feedSeen('Tổng số lượt view'),
  feedReaction('Tổng số lượt react'),
  comment('Tổng số comment'),
  attachment('Tổng số attachment'),
  conversation('Tổng số nhóm chat', isHeader: true, hasDetails: true),
  message('Tổng số tin nhắn'),
  conversationAttactment('Tổng số attachment'),
  sticker('Tổng số sticker'),
  numberOfUserWithoutId('Tổng số user chưa có id', isHeader: true),
  numberOfFeedWithoutGroup('Tổng số feed chưa có group'),
  numberOfDownloadedFiles('Tổng số file đã tải xuống'),
  numberOfUploadFiles('Tổng số file đã tải lên'),
  ;

  const _CrawlType(
    this.hint, {
    this.isHeader = false,
    this.hasDetails = false,
  });
  final String hint;
  final bool isHeader, hasDetails;
}

mixin _DbHandlerMixin {
  final Isar isar = GetIt.I<Isar>(instanceName: 'kIsar');
  final localService =
      GetIt.I<WorkPlaceLocalService>(instanceName: 'kWorkPlaceLocalService');

  Future<String> countByCrawlType(_CrawlType crawlType) async {
    List data = [];
    NumberFormat numberFormat = NumberFormat.decimalPattern();

    switch (crawlType) {
      case _CrawlType.member:
        data = await localService.getAllMembers();
      case _CrawlType.group:
        data = await localService.getAllGroups();
      case _CrawlType.feed:
      case _CrawlType.feedReaction:
      case _CrawlType.feedSeen:
        final feeds = await localService.getAllFeeds();

        if (crawlType == _CrawlType.feed) {
          return numberFormat.format(feeds.length);
        } else if (crawlType == _CrawlType.feedReaction) {
          int totalReactions = 0;

          for (var feed in feeds) {
            totalReactions += feed.reactions?.length ?? 0;
          }

          return numberFormat.format(totalReactions);
        } else if (crawlType == _CrawlType.feedSeen) {
          int totalSeens = 0;

          for (var feed in feeds) {
            totalSeens += feed.seen.length;
          }

          return numberFormat.format(totalSeens);
        }
      case _CrawlType.comment:
        data = await localService.getAllComments();
      case _CrawlType.attachment:
        data = await localService.getAllAttachments();
      case _CrawlType.conversation:
        data = await localService.getAllConversations();
      case _CrawlType.conversationAttactment:
        data = await localService.getAllConversationAttachments();
      case _CrawlType.message:
        data = await localService.getAllMessages();
      case _CrawlType.sticker:
        data = await localService.getAllStickers();
      case _CrawlType.numberOfUserWithoutId:
        final result = await localService.getMemberWithoutGPUserId();

        return numberFormat.format(result);
      case _CrawlType.numberOfFeedWithoutGroup:
        final result = await localService.getFeedWithoutGroup();

        return numberFormat.format(result);
      case _CrawlType.numberOfDownloadedFiles:
        final dashboardEntity = await localService.getGPDashBoardEntity();
        if (dashboardEntity != null) {
          return '${dashboardEntity.totalDownloadFile} - ${dashboardEntity.totalDownloadSize.toHumanReadableFileSize()}';
        }

        return '0';
      case _CrawlType.numberOfUploadFiles:
        final dashboardEntity = await localService.getGPDashBoardEntity();
        if (dashboardEntity != null) {
          return '${dashboardEntity.totalUploadFile} - ${dashboardEntity.totalUploadSize.toHumanReadableFileSize()}';
        }

        return '0';

      default:
    }

    return numberFormat.format(data.length);
  }
}

final class _Action with GPMapperMixin, _DbHandlerMixin, MergeDataBase {
  Future migrateInsertedAt() async {
    print("starting migration");
    final allMembers = await localService.getAllMembers();
    final allGroups = await localService.getAllGroups();
    final allFeeds = await localService.getAllFeeds();
    final allThreads = await localService.getAllThreads();

    await localService.saveMembers(allMembers);
    await localService.saveGroups(allGroups);
    await localService.saveFeeds(allFeeds);
    await localService.saveThreads(allThreads);
    print("migration completed");
  }

  Future<void> processUsersSummaryAll() async {
    Map<String, GPUser> groupsUser = await readUserFromCSV();
    Map<String, Groups> groupsMap = await readGroupsFromCSV();
    Map<String, Members> UserSummary =
        await readAndMergeCSVFilesUserAll(groupsUser, groupsMap);

    await exportMembersAllSummaryToCSV(UserSummary, groupsMap);
    print("Group summary has been exported to CSV successfully.");
  }

  Future<void> processUsersSummary() async {
    Map<String, GPUser> groupsUser = await readUserFromCSV();
    Map<String, Members> UserSummary =
        await readAndMergeCSVFilesUser(groupsUser);

    await exportMembersSummaryToCSV(UserSummary);
    print("Group summary has been exported to CSV successfully.");
  }

  Future<void> processGroupedSummary() async {
    Map<String, Groups> groupsMap = await readGroupsFromCSV();

    Map<String, GroupSummaryNew> groupedSummary =
        await readAndMergeCSVFiles2(groupsMap);

    await exportGroupSummaryNewToCSV(groupedSummary);

    print("Group summary has been exported to CSV successfully.");
  }

  Future<void> importWorkplaceData() async {
    try {
      List<GPPost> mergedPosts = await readAndMergeCSVFiles();

      if (mergedPosts.isEmpty) {
        log("Không có dữ liệu để xử lý.");
        return;
      }

      log("Đã đọc và gộp ${mergedPosts.length} bài đăng từ các file CSV.");

      String timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
      String exportFilename = 'exported_posts_$timestamp.csv';

      await exportPostsToCSV(mergedPosts, exportFilename);

      log("Quá trình hoàn tất.");
    } catch (e) {
      log("Đã xảy ra lỗi trong quá trình xử lý và xuất dữ liệu: $e");
    }
  }

  Future saveMemberCSV() async {
    final members = await localService.getAllMembers();

    final List<GPUser> gapoUsers =
        convertList<WorkPlaceCommunityMemberEntity, GPUser>(members);
    final csv = CsvHelper.toCSV(gapoUsers.map((e) => e.toJson()).toList());

    return FileHelper.saveCSV('member.csv', csv);
  }

  Future saveGroupCSV() async {
    final groups = await localService.getAllGroups();
    final gpGroup = groups.map((e) {
      final group = convert<WorkPlaceGroupEntity, GPGroup>(e);
      final json = group.toJson();
      return json;
    }).toList();

    final csv = CsvHelper.toCSV(gpGroup);

    return FileHelper.saveCSV('group.csv', csv);
  }

  Future updateFeedReactions(List<WorkPlaceFeedEntity> feeds) async {
    for (var element in feeds) {
      if (element.reactions?.isNotEmpty ?? false) {
        for (var react in element.reactions!) {
          if (react.gpUserId == null) {
            final user = await localService.getUserById(react.userId);
            react.gpUserId = user?.gpUserId;
            await localService.saveFeeds([element]);
          }
        }
      }
    }
  }

  Future updateCommentReactions() async {
    final comments = await localService.getAllComments();
    for (var element in comments) {
      await localService.reloadComment(element);
      if (element.reactions?.isNotEmpty ?? false) {
        for (var react in element.reactions!) {
          if (react.gpUserId == null) {
            final user = await localService.getUserById(react.userId);
            react.gpUserId = user?.gpUserId;
            await localService.saveComment(element);
          }
        }
      }
    }
  }

  Future saveFeedCSV() async {
    print('start export feed');
    final feeds = await localService.getAllFeeds();
    for (var element in feeds) {
      await localService.reload(element);
    }

    // // update reaction bi thieu gpUserId
    // await updateFeedReactions(feeds);
    // await updateCommentReactions();

    if (AppConstants.filterEmails.isNotEmpty) {
      feeds.removeWhere((element) {
        if (AppConstants.filterEmails.contains(element.from.value?.email) ==
            false) return true;
        return false;
      });
    }

    if (AppConstants.groupIds.isNotEmpty) {
      feeds.removeWhere((element) =>
          AppConstants.groupIds.contains(element.group.value?.id) != true ||
          element.isUserFeed);
    }

    feeds.removeWhere((element) => element.isPollVote);
    feeds.sort(((a, b) => a.createdTime!.compareTo(b.createdTime!)));

    // check post no comment, no reaction, comment no reaction
    final postNoComment = feeds.where((element) => element.comments.isEmpty);
    final postNoReaction =
        feeds.where((element) => element.reactions?.isEmpty ?? true);
    final postNoAttachment =
        feeds.where((element) => element.attachments.isEmpty);
    final List<WorkPlaceCommentEntity> allComments = [];
    for (var element in feeds) {
      allComments.addAll(element.comments);
    }
    final commentNoReaction =
        allComments.where((element) => element.reactions?.isEmpty ?? true);
    print(
        "Post No Comment: ${postNoComment.length}/${feeds.length} - ${(postNoComment.length / feeds.length * 100).toStringAsFixed(1)}%");
    print(
        "Post No Reaction: ${postNoReaction.length}/${feeds.length} - ${(postNoReaction.length / feeds.length * 100).toStringAsFixed(1)}%");
    print(
        "Post No Attachment: ${postNoAttachment.length}/${feeds.length} - ${(postNoAttachment.length / feeds.length * 100).toStringAsFixed(1)}%");
    print(
        "Comment No Reaction: ${commentNoReaction.length}/${allComments.length} - ${(commentNoReaction.length / allComments.length * 100).toStringAsFixed(1)}%");

    int offset = 0;
    int count = 1;
    const int itemPerFile = 1000;
    const bool isOneFile = true;
    final downloadPath = await getDownloadsDirectory();

    while (true) {
      if (offset >= feeds.length) break;
      final end = offset + itemPerFile > feeds.length
          ? feeds.length
          : offset + itemPerFile;
      final splitFeed = isOneFile ? feeds : feeds.sublist(offset, end);
      offset += splitFeed.length;
      final gpPosts = convertList<WorkPlaceFeedEntity, GPPost>(feeds);

      gpPosts.removeWhere((element) {
        if ((element.content?.isEmpty ?? true) &&
            (element.media?.isEmpty ?? true)) {
          return true;
        }
        if (element.userId?.isEmpty ?? true) return true;
        return false;
      });
      final gpGroupPosts = gpPosts.map((e) {
        return e.toJson();
      }).toList();
      final csv = CsvHelper.toCSV(gpGroupPosts);
      final path =
          "${downloadPath?.path}/feed_${DateFormat('ddMMyyyy').format(DateTime.now())}/feed_split_part$count.csv";
      File feedFile = await File(path).create(recursive: true);
      await feedFile.writeAsString(csv);
      log("File $path exported successfully!");
      print('$offset/${feeds.length} ===> ${offset / feeds.length * 100}%');
      count++;
    }
    print('done export feed');
  }

  Future saveConversationCSV() async {
    final conversationEntities = await localService.getAllConversations();

    for (var element in conversationEntities) {
      await localService.reloadThread(element);
    }

    conversationEntities.removeWhere((element) {
      final participants = element.participants
          .where((element) => element.email == null)
          .toList();
      return participants.length == 1 &&
          element.type == WorkPlaceThreadType.direct;
    });

    conversationEntities.removeWhere((element) => element.participants.isEmpty);

    if (AppConstants.filterEmails.isNotEmpty) {
      conversationEntities.removeWhere((element) {
        bool needToRemove = true;

        for (var element in element.participants) {
          if (AppConstants.filterEmails.contains(element.email)) {
            needToRemove = false;
            break;
          }
        }

        return needToRemove;
      });
    }

    // log('conversationEntities -> $conversationEntities');

    final List<GPThread> threads =
        convertList<WorkPlaceConversationEntity, GPThread>(
            conversationEntities);
    final csv = CsvHelper.toCSV(threads.map((e) => e.toJson()).toList());

    return FileHelper.saveCSV('thread.csv', csv);
  }

  Future saveMessageCSV() async {
    final messageEntities = await localService.getAllMessages();
    for (var element in messageEntities) {
      await localService.reloadMessage(element);
    }
    final List<GPMessage> gpMessages =
        convertList<WorkPlaceMessagesEntity, GPMessage>(messageEntities);

    gpMessages.removeWhere((element) => element.isInvalid);

    final reversedMessage = gpMessages.reversed;

    final csv =
        CsvHelper.toCSV(reversedMessage.map((e) => e.toJson()).toList());

    return FileHelper.saveCSV('message.csv', csv);
  }

  Future saveSplitMessageCSV() async {
    print('start export thread and message');
    int offset = 0;
    int count = 1;
    const int itemPerFile = 1;
    const bool isOneFile = true;
    final downloadPath = await getDownloadsDirectory();
    final allMessages = await localService.getAllMessages();
    final allThreads = await localService.getAllThreads();

    allThreads.removeWhere((element) {
      final participants = element.participants
          .where((element) => element.email == null)
          .toList();
      return participants.length == 1 &&
          element.type == WorkPlaceThreadType.direct;
    });

    allThreads.removeWhere((element) => element.participants.length <= 1);

    if (AppConstants.threadIds.isNotEmpty) {
      allThreads.removeWhere(
          (element) => AppConstants.threadIds.contains(element.id) == false);
    }

    if (AppConstants.userIds.isNotEmpty) {
      allThreads.removeWhere((element) {
        final participants = element.participants.map((e) => e.id);
        for (var id in AppConstants.userIds) {
          if (participants.contains(id)) {
            return false;
          } else {
            return true;
          }
        }
        return false;
      });
    }

    while (true) {
      if (offset >= allThreads.length) break;
      print('running export thread');
      final end = offset + itemPerFile > allThreads.length
          ? allThreads.length
          : offset + itemPerFile;
      final threadEntities =
          isOneFile ? allThreads : allThreads.sublist(offset, end);
      offset += threadEntities.length;

      // Messages
      print('running export message');
      final threadEntitiesCopy =
          List<WorkPlaceConversationEntity>.from(threadEntities);
      final List<GPMessage> allGPMessages = [];
      for (var element in threadEntities) {
        final messages =
            allMessages.where((e) => e.conversation.value?.id == element.id);
        final List<GPMessage> gpMessages =
            convertList<WorkPlaceMessagesEntity, GPMessage>(messages.toList());
        gpMessages.removeWhere((element) => element.isInvalid);
        final reversedMessage = gpMessages.reversed;
        if (reversedMessage.isEmpty) {
          threadEntitiesCopy.removeWhere((thread) => thread.id == element.id);
        } else {
          allGPMessages.addAll(reversedMessage);
        }
      }

      final csv = CsvHelper.toCSV(
          allGPMessages.isEmpty
              ? [GPMessage().toJson()]
              : allGPMessages.map((e) => e.toJson()).toList(),
          isEmpty: allGPMessages.isEmpty);
      if (allGPMessages.isNotEmpty) {
        final List<GPThread> threads =
            convertList<WorkPlaceConversationEntity, GPThread>(
                threadEntitiesCopy);
        final threadCSV =
            CsvHelper.toCSV(threads.map((e) => e.toJson()).toList());
        final threadPath =
            "${downloadPath?.path}/${DateFormat('ddMMyyyy').format(DateTime.now())}/thread_split_part$count.csv";
        File threadFile = await File(threadPath).create(recursive: true);
        await threadFile.writeAsString(threadCSV);
        log("File $threadPath exported successfully!");

        final path =
            "${downloadPath?.path}/${DateFormat('ddMMyyyy').format(DateTime.now())}/message_split_part$count.csv";
        File returnedFile = await File(path).create(recursive: true);
        await returnedFile.writeAsString(csv);
        log("File $path exported successfully!");
        count++;
      }
      print(
          '$offset/${allThreads.length} ===> ${offset / allThreads.length * 100}%');
    }
    print('Done export csv');
  }

  Future splitUserAndGroup() async {
    final data = await rootBundle.loadString(AppConstants.listIdFilePath);
    List<String> computerIds = [];
    if (data.isEmpty) {
      log("Can't open computer ids file or file is empty");
      return;
    } else {
      computerIds = data.split(',');
    }
    final allUsers = await localService.getAllMembers();
    final allGroups = await localService.getAllGroups();
    final allThreads = await localService.getAllThreads();

    final splitedUsers = allUsers.splitToNumList(computerIds.length);
    final splitedGroups = allGroups.splitToNumList(computerIds.length);
    final splitedThreads = allThreads.splitToNumList(computerIds.length);

    await saveFileSplitedData(
        input: splitedUsers, fileName: "users", computerIds: computerIds);
    await saveFileSplitedData(
        input: splitedGroups, fileName: "groups", computerIds: computerIds);
    await saveFileSplitedData(
        input: splitedThreads, fileName: "threads", computerIds: computerIds);

    print('done');
  }

  Future saveFileSplitedData(
      {required List<List<BaseCrawlEntity>> input,
      required String fileName,
      required List<String> computerIds}) async {
    final documentPath = await getApplicationDocumentsDirectory();
    for (var i = 0; i < input.length; i++) {
      final path =
          "${documentPath.path}/multi-client/${computerIds[i]}/$fileName.txt";
      final listId = input[i].map((e) => e.id).toList();
      final data = listId.join(',');
      final File returnedFile = await File(path).create(recursive: true);
      await returnedFile.writeAsString(data);
      log("File $path exported successfully!");
    }
  }

  Future mergeDatabase() async {
    log("Starting merge database...");
    final document = await getApplicationDocumentsDirectory();
    final mergedPath = "${document.path}/multi-client/merged";
    final data = await rootBundle.loadString(AppConstants.listIdFilePath);
    List<String> computerIds = [];
    if (data.isEmpty) {
      log("Can't open computer ids file or file is empty");
      return;
    } else {
      computerIds = data.split(',');
    }
    for (var i = 0; i < computerIds.length; i++) {
      log("folder: ${computerIds[i]}");
      final db = await Isar.open(
        AppConstants.dbSchemas,
        directory: "${document.path}/multi-client/${computerIds[i]}",
        name: computerIds[i],
      );
      final mergeDb = await Isar.open(
        AppConstants.dbSchemas,
        directory: mergedPath,
        name: "merge-db",
      );

      // attachment, sticker
      await mergeAttachment(db, mergeDb);

      // comment
      await mergeComment(db, mergeDb);

      // feed
      await mergeFeed(db, mergeDb);

      // Messages
      await mergeMessage(db, mergeDb);

      // Conversation
      await mergeConversation(db, mergeDb);

      db.close();
      mergeDb.close();
    }
    log("Done merge database");
  }
}

class _CountDownTimerWidget extends StatefulWidget {
  const _CountDownTimerWidget();

  @override
  State<_CountDownTimerWidget> createState() => _CountDownTimerState();
}

class _CountDownTimerState extends State<_CountDownTimerWidget> {
  final controller = CountDownController();

  final ValueNotifier<bool> rxStateChanged = ValueNotifier(false);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 30),
      child: Row(
        children: [
          CircularCountDownTimer(
            duration: _tickSeconds,
            initialDuration: 0,
            controller: controller,
            width: 120,
            height: 120,
            ringColor: Colors.grey[300]!,
            ringGradient: null,
            fillColor: Colors.greenAccent[100]!,
            fillGradient: null,
            backgroundColor: Colors.green[500],
            backgroundGradient: null,
            strokeWidth: 20.0,
            strokeCap: StrokeCap.round,
            textStyle: const TextStyle(
              fontSize: 20.0,
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
            textFormat: CountdownTextFormat.S,
            isReverse: false,
            isReverseAnimation: false,
            isTimerTextShown: true,
            autoStart: true,
            onStart: () {},
            onComplete: () {
              controller.restart(duration: _tickSeconds);
            },
            onChange: (String timeStamp) {},
            timeFormatterFunction: (defaultFormatterFunction, duration) {
              if (duration.inSeconds == 0) {
                return "Updated";
              } else {
                Future.delayed(Duration.zero).then(
                  (value) => rxStateChanged.value = !rxStateChanged.value,
                );
                return Function.apply(defaultFormatterFunction, [duration]);
              }
            },
          ),
          const SizedBox(width: 20),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                l10n.crawl_run_time_start_header,
                style:
                    const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              Text(currentDateTimeStr),
              if (startDateTime != null) ...{
                const SizedBox(height: 10),
                Text(
                  l10n.crawl_run_time_diff,
                  style: const TextStyle(
                      fontSize: 20, fontWeight: FontWeight.bold),
                ),
                ValueListenableBuilder(
                  valueListenable: rxStateChanged,
                  builder: (context, value, child) {
                    return Text(diffDateTimeStr);
                  },
                )
              }
            ],
          )
        ],
      ),
    );
  }

  String get currentDateTimeStr {
    if (startDateTime != null) {
      return DateFormat('dd/MM/yyyy hh:mm:ss').format(startDateTime!);
    }

    return DateFormat('dd/MM/yyyy hh:mm:ss').format(DateTime.now());
  }

  String get diffDateTimeStr {
    final StringBuffer buffer = StringBuffer();
    if (startDateTime != null) {
      final diff = DateTime.now().difference(startDateTime!);
      final days = diff.inDays;
      final hours = diff.inHours.remainder(24);
      final minutes = diff.inMinutes.remainder(60);
      final seconds = diff.inSeconds.remainder(60);

      if (days > 0) buffer.write('${days}d ');
      if (hours > 0) buffer.write('${hours}h ');
      if (minutes > 0) buffer.write('${minutes}m ');
      if (seconds > 0) buffer.write('${seconds}s ');

      return buffer.toString().trim(); // remove trailing space
    }

    return '';
  }
}
