/*
 * Created Date: Monday, 10th June 2024, 15:57:40
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 11th June 2024 17:59:22
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:dio/dio.dart' hide Headers;
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/app/constant/gapo_url.constants.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:injectable/injectable.dart';
import 'package:retrofit/retrofit.dart';

part 'group.service.g.dart';

@LazySingleton(order: DiConstants.kDataServiceOrder)
@Named('kGapoWorkGroupService')
@RestApi()
abstract class GroupService {
  @FactoryMethod()
  factory GroupService(
    @Named('kGapoWorkDio') Dio dio, {
    @Named('kGapoWorkDomain') String? baseUrl,
  }) = _GroupService;

  @POST(GPConstants.kGapoWorkGroup)
  Future<ApiResponseV2<GroupResponse>> createGroup({
    @Body() required GPGroupParams groupRequest,
  });

  @POST('${GPConstants.kGapoWorkMembershipGroup}/{group_id}/invite')
  Future<ApiResponseV2WithoutData> inviteToGroup({
    @Path('group_id') required String groupId,
    @Body() required GPGroupInviteParams groupInviteRequest,
  });

  @POST(
      '${GPConstants.kGapoWorkMembershipGroup}/{group_id}/${GPConstants.kGapoWorkGroupAdjustMemberRole}')
  Future<ApiResponseV2WithoutData> adjustMemberRole({
    @Path('group_id') required String groupId,
    @Body() required GPGroupAdjustMemberRoleParams groupAdjustMemberRoleRequest,
  });

  @POST(
      '${GPConstants.kGapoWorkMembershipGroup}/{group_id}/${GPConstants.kGapoWorkGroupLeave}')
  Future<ApiResponseV2WithoutData> leaveGroup({
    @Path('group_id') required String groupId,
    @Body() required GPGroupLeaveParams groupLeaveRequest,
  });
}
