name: 'gp_fbwp_crawler'
version: 1.0.0
environment: 
  sdk: '>=3.1.3 <4.0.0'
  flutter: 3.13.6
description: GapoFlutter project for syncing data from Facebook WorkPlace to GapoWork.
dependencies: 
  bloc: ^8.1.3
  bloc_test: ^9.1.7
  build_runner: ^2.1.10
  async_task: '^1.0.20'
  crypto: ^3.0.3
  csv: ^5.1.1
  cupertino_icons: '^1.0.2'
  file_picker: ^8.0.0+1
  flutter: 
    sdk: flutter
  flutter_bloc: '^8.1.3'
  flutter_gen: '^5.5.0+1'
  flutter_localizations: 
    sdk: flutter
  freezed_annotation: '^2.4.1'
  get_it: '^7.6.4'
  go_router: '^13.2.0'
  # gp_core: 
  #   git: 
  #     url: '**********************:flutter/core/gp_core.git'
  #     ref: develop
  gp_core_v2: 
    git: 
      url: '**********************:flutter/core/gp_core_v2.git'
      ref: fix/exception
      path: 'gp_core_v2'
  injectable: '^2.3.5'
  intl: any
  isar: '^3.1.0+1'
  isar_flutter_libs: '^3.1.0+1'
  json_annotation: '^4.9.0'
  lottie: ^2.7.0
  path_provider: '^2.1.3'
  responsive_sizer: ^3.3.1
  retrofit: '^4.1.0'
  rxdart: ^0.27.7
  talker: '^4.2.0'
  talker_bloc_logger: '^4.2.0'
  talker_flutter: '^4.1.2'
  retry: ^3.1.2
  async_queue: ^2.0.0
  strings: ^3.1.2
  gp_dio_log:
    git:
      url: **********************:flutter/gp_dio_log.git
      ref: "feat/3.13.6"
  circular_countdown_timer: ^0.2.3
  flutter_dotenv: ^5.1.0
dev_dependencies: 
  auto_mappr: '^2.2.0'
  build_runner: '^2.4.9'
  flutter_gen_runner: any
  flutter_launcher_icons: '^0.13.1'
  flutter_lints: '^4.0.0'
  flutter_test: 
    sdk: flutter
  freezed: '^2.4.6'
  go_router_builder: '^2.6.2'
  injectable_generator: 2.4.2
  isar_generator: '^3.1.0+1'
  json_serializable: '^6.7.1'
  package_rename_plus: '^1.0.1'
  pubspec_dependency_sorter: '^1.0.4'
  retrofit_generator: '^8.0.6'
depdendencies_override: 
  device_info_plus: 9.0.2
flutter_gen: 
  output: 'lib/flutter_gen/'
  line_length: 80
  integrations: 
    flutter_svg: true
    flare_flutter: true
    rive: true
    lottie: true
flutter: 
  generate: true
  uses-material-design: true
  assets: 
    - assets/images/
    # không hiểu sao windows khai báo folder khác images = lỗi
    # - assets/lotties
    - .env
    - data/
