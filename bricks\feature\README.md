## gp_brick_feature

For generate <PERSON><PERSON><PERSON><PERSON><PERSON> 's feature

### How to use?
```sh
mason make gp_brick_feature --feature_name YourFeature
```

### Params:

| Variables    | Description           | Default | Type    | Conditional | When                                                                                |
|--------------|-----------------------|---------|---------|-------------|-------------------------------------------------------------------------------------|
| feature_name | Your feature name     | login   | string  | false       | N/A                                                                                 |
| use_frezeed  | Use frezeed for state | false   | boolean | true        | Generate State with Frezeed. You will need run `dart run build_runner build` later. |
