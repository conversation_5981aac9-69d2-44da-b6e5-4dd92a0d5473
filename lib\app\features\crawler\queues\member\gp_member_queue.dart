import 'dart:async';

import 'package:gp_core_v2/base/base.dart';
import 'package:gp_fbwp_crawler/app/app.dart';
import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:gp_fbwp_crawler/helpers/helpers.dart';

final class GPMemberQueue extends BaseMemberQueue with AttachmentHandlerMixin {
  GPMemberQueue({required super.crawlQueueBloc, required super.commonBloc});

  Future<List<WorkPlaceCommunityMemberEntity>> signupUsers(
    List<WorkPlaceCommunityMemberEntity> members,
  ) async {
    return await addToQueue<List<WorkPlaceCommunityMemberEntity>>(
          message: l10n.crawl_user_signup_users(members.length),
          skipCatch: true,
          job: () async {
            for (var member in members) {
              if (member.gpUserId != null || member.email == null) continue;

              final userId = await _checkEmailRegistered(member);

              if (userId != null) {
                // email đã đăng ký trên gapo
                member.gpUserId = userId;
              } else {
                member.changeToDownloadStatus(
                  status: BaseCrawlDownloadStatusEnum.downloaded,
                );

                final authResponse = await _signupAnUser(member);

                final token = await _setDefaultPassword(authResponse);

                member.gpUserId = authResponse.data.userId;

                await _setGPUserInfo(member, token);

                member.changeToDownloadStatus(
                    status: BaseCrawlDownloadStatusEnum.downloaded);
              }

              await saveMembers([member]);
            }

            return members;
          },
        ) ??
        members;
  }

  Future<void> inviteToWorkspace(
    List<WorkPlaceCommunityMemberEntity> members,
  ) async {
    for (var member in members) {
      await addToQueue(
        message: l10n.crawl_user_invite_user_to_ws(member.name ?? ''),
        level: 2,
        job: () async {
          await gpInviteWorkspaceUseCase.execute(
            GPInviteEmailWorkspaceInput(
              params: GPInviteWsParams(email: member.email ?? ''),
            ),
          );
        },
      );
    }
  }

  Future<ApiResponseV2<AuthResponse>> _signupAnUser(
      WorkPlaceCommunityMemberEntity member) async {
    return await gpSignupUseCase.execute(
      GPSignupInput(
        params: GPSignupParams(email: member.email ?? ''),
        onRetry: (e) {
          member.changeToDownloadStatus(
            status: BaseCrawlDownloadStatusEnum.downloadFailed,
            code: kExceptionCanNotSignup,
            message: kExceptionCanNotSignupMessage,
          );
        },
      ),
    );
  }

  Future<int?> _checkEmailRegistered(
      WorkPlaceCommunityMemberEntity member) async {
    try {
      // limit 100 lần trong 10p => 6s 1 lần
      // await Future.delayed(const Duration(milliseconds: 6300));
      final response = await gpAuthCheckMailUseCase
          .execute(AuthCheckEmailRequest(member.email ?? '', ''));
      return response.data.userId;
    } catch (e) {
      return null;
    }
  }

  Future<String> _setDefaultPassword(
    ApiResponseV2<AuthResponse> authResponse,
  ) async {
    final token = "Bearer ${authResponse.data.accessToken}";

    final password = AppConstants.useRandomPassword
        ? StringHelper.generateRandomString(10)
            .passwordSHA256(authResponse.data.salt ?? "")
        : AppConstants.kDefaultPassword
            .passwordSHA256(authResponse.data.salt ?? "");

    await gpSetPasswordUseCase.execute(GPAuthSetPasswordInput(
        params: GPSetPasswordParams(
          password: password,
        ),
        token: token));

    return token;
  }

  Future<void> _setGPUserInfo(
    WorkPlaceCommunityMemberEntity member,
    String userToken,
  ) async {
    if (member.name != null) {
      await addToQueue(
        message: l10n.crawl_user_update_gp_user_info(member.name ?? ''),
        level: 2,
        job: () async {
          GPUserProfilePictureData? avatarData;
          GPUserProfilePictureData? coverData;

          if (member.picture != null) {
            avatarData = await _uploadUserPicture(member.picture!);
          }

          if (member.cover != null) {
            coverData = await _uploadUserPicture(member.cover!);
          }

          await gpUserProfileUseCase.execute(
            GPUserProfileInput(
              token: userToken,
              params: GPUserProfileParams(
                displayName: member.name!,
                avatar: avatarData?.source,
                avatarData: avatarData?.id == null ? null : avatarData,
                coverData: coverData?.id == null ? null : coverData,
              ),
              userId: member.gpUserId.toString(),
            ),
          );
        },
      );
    }
  }

  Future<GPUserProfilePictureData?> _uploadUserPicture(String url) async {
    final attachment = WorkPlaceAttachmentEntity(
      url: url,
      type: AttachmentType.photo,
      media: AttachmentMediaEntity(image: AttachmentMediaImageEntity(src: url)),
    );

    return await _uploadPicture(attachment);
  }

  Future<GPUserProfilePictureData?> _uploadPicture(
    WorkPlaceAttachmentEntity attachmentEntity,
  ) async {
    final wpUrl = attachmentEntity.urlDownload();

    final downloadOutput =
        await downloadAttachment(entity: attachmentEntity, wpUrl: wpUrl);

    if (downloadOutput == null) return null;

    final response = await uploadAttachment(
      wpUrl: wpUrl,
      entity: attachmentEntity,
      downloadOutput: downloadOutput,
      onUploaded: (uploadUrl, uploadId) {
        attachmentEntity.gpLink = uploadUrl;
        attachmentEntity.gpId = uploadId;
      },
      onDownloadFileSuccess: (path) {
        attachmentEntity.localFilePath = path;
      },
    );

    if (response != null) {
      uploadResponseHandler.updateUploadResponse(attachmentEntity, response);
    }

    return GPUserProfilePictureData(
        source: attachmentEntity.gpLink,
        src: attachmentEntity.gpLink,
        id: attachmentEntity.gpId,
        width: attachmentEntity.uploadResponse?.width,
        height: attachmentEntity.uploadResponse?.height);
  }
}
