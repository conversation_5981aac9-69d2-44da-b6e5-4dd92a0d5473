// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'gp_invite_workspace_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$GPInviteWsParams {
  String get email => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GPInviteWsParamsCopyWith<GPInviteWsParams> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GPInviteWsParamsCopyWith<$Res> {
  factory $GPInviteWsParamsCopyWith(
          GPInviteWsParams value, $Res Function(GPInviteWsParams) then) =
      _$GPInviteWsParamsCopyWithImpl<$Res, GPInviteWsParams>;
  @useResult
  $Res call({String email});
}

/// @nodoc
class _$GPInviteWsParamsCopyWithImpl<$Res, $Val extends GPInviteWsParams>
    implements $GPInviteWsParamsCopyWith<$Res> {
  _$GPInviteWsParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = null,
  }) {
    return _then(_value.copyWith(
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GPInviteWsParamsImplCopyWith<$Res>
    implements $GPInviteWsParamsCopyWith<$Res> {
  factory _$$GPInviteWsParamsImplCopyWith(_$GPInviteWsParamsImpl value,
          $Res Function(_$GPInviteWsParamsImpl) then) =
      __$$GPInviteWsParamsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String email});
}

/// @nodoc
class __$$GPInviteWsParamsImplCopyWithImpl<$Res>
    extends _$GPInviteWsParamsCopyWithImpl<$Res, _$GPInviteWsParamsImpl>
    implements _$$GPInviteWsParamsImplCopyWith<$Res> {
  __$$GPInviteWsParamsImplCopyWithImpl(_$GPInviteWsParamsImpl _value,
      $Res Function(_$GPInviteWsParamsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = null,
  }) {
    return _then(_$GPInviteWsParamsImpl(
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _$GPInviteWsParamsImpl implements _GPInviteWsParams {
  const _$GPInviteWsParamsImpl({required this.email});

  @override
  final String email;

  @override
  String toString() {
    return 'GPInviteWsParams(email: $email)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GPInviteWsParamsImpl &&
            (identical(other.email, email) || other.email == email));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, email);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GPInviteWsParamsImplCopyWith<_$GPInviteWsParamsImpl> get copyWith =>
      __$$GPInviteWsParamsImplCopyWithImpl<_$GPInviteWsParamsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GPInviteWsParamsImplToJson(
      this,
    );
  }
}

abstract class _GPInviteWsParams implements GPInviteWsParams {
  const factory _GPInviteWsParams({required final String email}) =
      _$GPInviteWsParamsImpl;

  @override
  String get email;
  @override
  @JsonKey(ignore: true)
  _$$GPInviteWsParamsImplCopyWith<_$GPInviteWsParamsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
