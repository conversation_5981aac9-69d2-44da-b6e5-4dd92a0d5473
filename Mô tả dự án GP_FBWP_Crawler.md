# MÔ TẢ DỰ ÁN GP_FBWP_CRAWLER

## 1. TỔNG QUAN DỰ ÁN

### 1.1 Tên dự án
**gp_fbwp_crawler** - Ứng dụng Flutter để đồng bộ dữ liệu từ Facebook Workplace sang GapoWork

### 1.2 Mục đích
Dự án này được phát triển để:
- <PERSON>lone dữ liệu từ Facebook Workplace về client
- Lưu trữ dữ liệu vào local database
- Map dữ liệu để tạo entity tương ứng ở GapoWork
- Gọi API để tạo dữ liệu phía GapoWork

### 1.3 Phiên bản
- **Version**: 1.0.0
- **Flutter SDK**: 3.13.6
- **Dart SDK**: >=3.1.3 <4.0.0

## 2. KIẾN TRÚC DỰ ÁN

### 2.1 Cấu trú<PERSON> thư mụ<PERSON> ch<PERSON>
```
lib/
├── app/           # Cấu hình ứng dụng
├── config/        # C<PERSON>u hình môi trường
├── data/          # Tầng dữ liệu
├── di/            # Dependency Injection
├── domain/        # Tầng business logic
├── helpers/       # Các helper utilities
├── l10n/          # Đa ngôn ngữ
├── mapper/        # Mapping dữ liệu
└── route/         # Định tuyến
```

### 2.2 Công nghệ sử dụng
- **Framework**: Flutter 3.13.6
- **State Management**: BLoC Pattern
- **Database**: Isar (NoSQL)
- **Networking**: Retrofit + Dio
- **Dependency Injection**: GetIt + Injectable
- **Routing**: GoRouter

## 3. FILE HELPER CHÍNH - lib/helpers/file_helper.dart

### 3.1 Mô tả chức năng
File `file_helper.dart` là thành phần cốt lõi của dự án, chứa các utility functions để:
- Quản lý file và thư mục
- Xử lý CSV import/export
- Parse dữ liệu từ Facebook Workplace
- Chuyển đổi định dạng dữ liệu

### 3.2 Các class và function chính

#### 3.2.1 Class FileHelper
```dart
class FileHelper {
  // Lấy thư mục download
  static Future<Directory> getDownloadDirectory()
  
  // Lấy đường dẫn file download
  static Future<String> downloadDirectoryPath(String fileName)
  
  // Trích xuất tên file từ URL
  static String? getFileNameFromUrl(String url)
  
  // Lấy đường dẫn file từ URL
  static Future<String> getFilePathFromUrl(String url)
  
  // Xác định loại upload API
  static GPApiUploadType apiUploadType(String fileName)
  
  // Lưu file CSV
  static Future saveCSV(String filename, String csv)
}
```

#### 3.2.2 Extension FileSizeExtensions
```dart
extension FileSizeExtensions on num {
  // Chuyển đổi kích thước file sang định dạng dễ đọc
  String toHumanReadableFileSize({int round = 2, bool useBase1024 = true})
}
```

#### 3.2.3 Các function xử lý CSV

**Đọc và merge file CSV:**
```dart
// Đọc và merge nhiều file CSV thành danh sách GPPost
Future<List<GPPost>> readAndMergeCSVFiles()

// Đọc dữ liệu migrate từ CSV
Future<List<GPMigrate>> readAndMergeCSVFilesMigrateData()

// Đọc dữ liệu group từ CSV
Future<Map<String, Groups>> readGroupsFromCSV()

// Đọc dữ liệu user từ CSV
Future<Map<String, local.GPUser>> readUserFromCSV()
```

**Export dữ liệu ra CSV:**
```dart
// Export posts ra CSV
Future<void> exportPostsToCSV(List<GPPost> posts, String filename)

// Export group summary ra CSV
Future<void> exportGroupSummaryNewToCSV(Map<String, GroupSummaryNew> userPosts)

// Export member summary ra CSV
Future<void> exportMembersSummaryToCSV(Map<String, Members> userPosts)
```

#### 3.2.4 Các function parse dữ liệu

**Parse các loại dữ liệu từ string:**
```dart
// Parse media data
List<GPPostMedia>? parseMedia(String mediaString)

// Parse privacy setting
GPPostPrivacy? parsePrivacy(String privacyString)

// Parse mention data
List<GPPostMention>? parseMention(String mentionString)

// Parse seen users
List<local.GPUser>? parseSeen(String seenString)

// Parse comments
List<GPComment>? parseComments(String commentsString)

// Parse reactions
List<GPReaction>? parseReactions(String reactionsString)
```

#### 3.2.5 Các class model dữ liệu

**Class Groups:**
```dart
class Groups {
  String id;
  String name;
  int totalmembers;
}
```

**Class GroupSummaryNew:**
```dart
class GroupSummaryNew {
  String id;
  Groups groups;
  DateTime firstPostTime;
  DateTime lastPostTime;
  int totalPosts;
  int totalComments;
  int totalReactions;
  int totalMedia;
}
```

**Class Members:**
```dart
class Members {
  String id;
  local.GPUser user;
  DateTime firstPostTimeUser;
  DateTime lastPostTimeUser;
  int totalPostsUser;
  int totalCommentsUser;
  int totalReactionsUser;
  int totalMediaUser;
  // ... các field khác cho group data
}
```

### 3.3 Tính năng đặc biệt

#### 3.3.1 Xử lý CSV với encoding UTF-8
- Hỗ trợ đọc file CSV với encoding UTF-8
- Xử lý các ký tự đặc biệt và dấu tiếng Việt
- Clean dữ liệu CSV (loại bỏ ký tự thừa, escape characters)

#### 3.3.2 Parse JSON phức tạp
- Xử lý JSON string được escape trong CSV
- Parse các object phức tạp như media, comments, reactions
- Xử lý lỗi và fallback khi parse thất bại

#### 3.3.3 Thống kê dữ liệu
- Tính toán thống kê cho group (tổng posts, comments, reactions, media)
- Tính toán thống kê cho user (hoạt động cá nhân và trong group)
- Theo dõi thời gian đầu tiên và cuối cùng của hoạt động

## 4. DEPENDENCIES CHÍNH

### 4.1 Core Dependencies
- **flutter**: SDK chính
- **bloc/flutter_bloc**: State management
- **get_it/injectable**: Dependency injection
- **go_router**: Navigation
- **isar**: Local database

### 4.2 File & Data Processing
- **file_picker**: Chọn file từ hệ thống
- **csv**: Xử lý file CSV
- **path_provider**: Truy cập thư mục hệ thống
- **crypto**: Mã hóa dữ liệu

### 4.3 Network & API
- **retrofit**: HTTP client generator
- **json_annotation/json_serializable**: JSON serialization
- **gp_core_v2**: Core library của Gapo

### 4.4 UI & UX
- **responsive_sizer**: Responsive design
- **lottie**: Animation
- **cupertino_icons**: iOS style icons

## 5. WORKFLOW ĐA CLIENT

### 5.1 Quy trình làm việc với nhiều máy
1. **Khởi tạo môi trường** (`init_env.sh`)
   - Tạo ID cho tất cả các máy
   - Ghi vào file `computer_ids.txt`

2. **Chia dữ liệu** (Máy chính)
   - Kéo dữ liệu user và group
   - Chia danh sách theo ID máy
   - Push file đã chia (`copy_splited_data.sh`)

3. **Chuẩn bị database** (`copy_base_db.sh`)
   - Copy database vào project folder
   - Push lên git

4. **Phân phối dữ liệu** (`copy_data_to_local.sh`)
   - Mỗi máy copy database và file chia về local

5. **Xử lý dữ liệu**
   - Mỗi máy đọc file theo computer ID
   - Lấy posts, chats, messages theo user/group ID

6. **Thu thập kết quả** (`copy_db.sh`)
   - Copy DB local vào project folder
   - Push lên git theo thư mục `data/{computer_id}`

7. **Merge dữ liệu** (`merge_db.sh`)
   - Máy chính pull các DB về
   - Merge dữ liệu từ tất cả các máy

## 6. TÍNH NĂNG CHÍNH

### 6.1 Đồng bộ dữ liệu
- **Chat**: Tin nhắn và cuộc trò chuyện
- **Members**: Thành viên và thông tin cá nhân
- **Posts**: Bài viết và nội dung
- **Comments**: Bình luận và phản hồi
- **Groups**: Nhóm và thông tin nhóm
- **Attachments**: File đính kèm

### 6.2 Quản lý tiến trình
- Hiển thị progress đồng bộ
- Theo dõi trạng thái sync
- Báo cáo lỗi và thành công

### 6.3 Mapping dữ liệu
- Chuyển đổi từ Facebook Workplace format sang GapoWork format
- Đánh dấu dữ liệu không thể map
- Validation dữ liệu trước khi gửi API

## 7. TESTING

### 7.1 Unit Testing
- Test cho Mapper functions
- Test cho data parsing
- Test cho business logic

### 7.2 Integration Testing
- Test workflow đồng bộ
- Test API calls
- Test database operations

## 8. DEPLOYMENT

### 8.1 Môi trường
- **Production**: `main.production.dart`
- **Staging**: `main.staging.dart`
- **UAT**: `main.uat.dart`

### 8.2 Platform hỗ trợ
- **Desktop**: Windows, macOS, Linux
- **Mobile**: Android, iOS
- **Web**: Browser support

## 9. KẾT LUẬN

Dự án `gp_fbwp_crawler` là một ứng dụng Flutter phức tạp được thiết kế để đồng bộ dữ liệu từ Facebook Workplace sang GapoWork. File `file_helper.dart` đóng vai trò trung tâm trong việc xử lý file, parse dữ liệu CSV và thống kê. Dự án sử dụng kiến trúc clean architecture với BLoC pattern, hỗ trợ đa platform và có khả năng mở rộng cao.

Các tính năng chính bao gồm:
- Xử lý file CSV phức tạp với encoding UTF-8
- Parse và validate dữ liệu từ Facebook Workplace
- Thống kê và báo cáo chi tiết
- Hỗ trợ workflow đa client cho xử lý dữ liệu lớn
- Export dữ liệu ra nhiều định dạng khác nhau

Dự án này thể hiện sự chuyên nghiệp trong việc xây dựng ứng dụng enterprise với yêu cầu xử lý dữ liệu phức tạp và hiệu suất cao.
