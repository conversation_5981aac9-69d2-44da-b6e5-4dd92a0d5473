// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'gp_upload_file_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GPUploadFileResponseModel _$GPUploadFileResponseModelFromJson(
        Map<String, dynamic> json) =>
    GPUploadFileResponseModel(
      id: json['id'] as String?,
      name: json['name'] as String?,
      userId: json['user_id'] as String?,
      size: (json['size'] as num?)?.toInt(),
      fileType: json['file_type'] as String?,
      url: json['url'] == null
          ? null
          : UploadFileURLResponseModel.fromJson(
              json['url'] as Map<String, dynamic>),
      thumbUrl: json['thumb_url'] == null
          ? null
          : UploadFileURLResponseModel.fromJson(
              json['thumb_url'] as Map<String, dynamic>),
      fileLink: json['file_link'] as String?,
      quality: json['quality'] as String?,
      source: json['source'] as String?,
    )
      ..src = json['src'] as String?
      ..type = json['type'] as String?;

Map<String, dynamic> _$GPUploadFileResponseModelToJson(
        GPUploadFileResponseModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'user_id': instance.userId,
      'size': instance.size,
      'file_type': instance.fileType,
      'url': instance.url,
      'thumb_url': instance.thumbUrl,
      'src': instance.src,
      'file_link': instance.fileLink,
      'quality': instance.quality,
      'source': instance.source,
      'type': instance.type,
    };
