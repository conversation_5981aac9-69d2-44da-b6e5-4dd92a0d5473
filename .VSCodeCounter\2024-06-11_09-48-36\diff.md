# Diff Summary

Date : 2024-06-11 09:48:36

Directory /Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler

Total : 113 files,  19105 codes, 468 comments, 1983 blanks, all 21556 lines

[Summary](results.md) / [Details](details.md) / Diff Summary / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| Dart | 111 | 19,102 | 468 | 1,983 | 21,553 |
| YAML | 1 | 2 | 0 | 0 | 2 |
| JSON | 1 | 1 | 0 | 0 | 1 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 113 | 19,105 | 468 | 1,983 | 21,556 |
| . (Files) | 1 | 2 | 0 | 0 | 2 |
| lib | 112 | 19,103 | 468 | 1,983 | 21,554 |
| lib/app | 17 | 737 | 58 | 118 | 913 |
| lib/app/app_config | 3 | 35 | 0 | 5 | 40 |
| lib/app/app_config/bloc | 3 | 35 | 0 | 5 | 40 |
| lib/app/base | 3 | 78 | 7 | 18 | 103 |
| lib/app/base/networking | 3 | 78 | 7 | 18 | 103 |
| lib/app/constant | 4 | 19 | 11 | 7 | 37 |
| lib/app/features | 7 | 605 | 40 | 88 | 733 |
| lib/app/features/crawler | 7 | 605 | 40 | 88 | 733 |
| lib/app/features/crawler (Files) | 2 | 85 | 2 | 0 | 87 |
| lib/app/features/crawler/bloc | 5 | 520 | 38 | 88 | 646 |
| lib/data | 44 | 2,968 | 84 | 344 | 3,396 |
| lib/data/data_source | 8 | 358 | 29 | 46 | 433 |
| lib/data/data_source/local | 1 | 2 | 0 | -1 | 1 |
| lib/data/data_source/remote | 7 | 356 | 29 | 47 | 432 |
| lib/data/model | 33 | 2,567 | 55 | 289 | 2,911 |
| lib/data/model (Files) | 2 | 14 | 0 | 3 | 17 |
| lib/data/model/facebook | 3 | 25 | 4 | 10 | 39 |
| lib/data/model/gpw | 3 | 22 | 4 | 12 | 38 |
| lib/data/model/workplace | 25 | 2,506 | 47 | 264 | 2,817 |
| lib/data/model/workplace (Files) | 1 | 3 | 0 | 0 | 3 |
| lib/data/model/workplace/base | 7 | 1,451 | 14 | 126 | 1,591 |
| lib/data/model/workplace/community | 3 | 46 | 7 | 14 | 67 |
| lib/data/model/workplace/enums | 2 | 17 | 0 | 5 | 22 |
| lib/data/model/workplace/group | 7 | 840 | 18 | 87 | 945 |
| lib/data/model/workplace/post | 5 | 149 | 8 | 32 | 189 |
| lib/data/repository | 3 | 43 | 0 | 9 | 52 |
| lib/di | 6 | 169 | 2 | 12 | 183 |
| lib/di/component | 1 | 124 | 0 | 1 | 125 |
| lib/di/modules | 5 | 45 | 2 | 11 | 58 |
| lib/domain | 38 | 14,213 | 188 | 1,416 | 15,817 |
| lib/domain/entity | 26 | 14,015 | 161 | 1,357 | 15,533 |
| lib/domain/entity (Files) | 1 | 2 | 0 | 0 | 2 |
| lib/domain/entity/base | 2 | 13 | 0 | 1 | 14 |
| lib/domain/entity/base/app | 1 | 11 | 0 | 1 | 12 |
| lib/domain/entity/base/log | 1 | 2 | 0 | 0 | 2 |
| lib/domain/entity/enums | 1 | 1 | 1 | 1 | 3 |
| lib/domain/entity/gapo | 7 | 1,616 | 29 | 191 | 1,836 |
| lib/domain/entity/workplace | 15 | 12,383 | 131 | 1,164 | 13,678 |
| lib/domain/repository | 4 | 33 | 12 | 17 | 62 |
| lib/domain/usecase | 8 | 165 | 15 | 42 | 222 |
| lib/l10n | 1 | 1 | 0 | 0 | 1 |
| lib/mapper | 6 | 1,015 | 136 | 93 | 1,244 |
| lib/mapper (Files) | 3 | 252 | 73 | 39 | 364 |
| lib/mapper/entity | 3 | 763 | 63 | 54 | 880 |

[Summary](results.md) / [Details](details.md) / Diff Summary / [Diff Details](diff-details.md)