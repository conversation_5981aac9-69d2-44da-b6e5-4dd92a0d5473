import 'dart:async';
import 'dart:developer';

import 'package:async_task/async_task_extension.dart';
import 'package:gp_fbwp_crawler/app/app.dart';
import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:gp_fbwp_crawler/helpers/csv.dart';
import 'package:gp_fbwp_crawler/mapper/mapper.dart';

final class FeedQueue extends BaseFeedQueue with _FilterEmail {
  FeedQueue({
    required super.crawlQueueBloc,
    required super.commonBloc,
  });

  late final GPFeedQueue gpQueue = GPFeedQueue(
    crawlQueueBloc: crawlQueueBloc,
    commonBloc: commonBloc,
  );

  Future<List<WorkPlaceFeedEntity>> getWorkPlaceFeeds() async {
    final List<WorkPlaceGroupEntity> groupsEntities =
        await multiClientGetGroups();
    // bỏ các group tạo bởi bot
    // groupsEntities.removeWhere((element) => element.owner.value == null);
    final List<WorkPlaceCommunityMemberEntity> membersEntities =
        await multiClientGetMembers();

    if (groupsEntities.isEmpty && membersEntities.isEmpty) return [];

    await addToQueue(
          message: l10n.crawl_feed_from_a_groups(groupsEntities.length),
          job: () async {
            return await _getFeedsFromGroups(groupsEntities);
          },
        ) ??
        [];

    print("Done get group feeds");

    await addToQueue(
          message: l10n.crawl_feed_from_a_members(membersEntities.length),
          job: () async {
            return await _getFeedsFromMembers(membersEntities);
          },
        ) ??
        [];

    print("Done get member feeds");

    final feeds = await getAllFeeds();

    for (var element in feeds) {
      await reloadFeed(element);
    }

    return feeds;
  }

  // Future<List<WorkPlaceFeedEntity>> downloadFeedAttachment(
  //   List<WorkPlaceFeedEntity> feeds,
  // ) async {
  //   await addToQueue(
  //     message: l10n.crawl_feed_get_attachment,
  //     job: () async {
  //       await _updateAttachments(feeds);
  //     },
  //   );

  //   return feeds;
  // }

  Future<List<WorkPlaceFeedEntity>> getFeedComments(
    List<WorkPlaceFeedEntity> feeds,
  ) async {
    await _updateComments(feeds);
    print("Done get feed comments");

    return feeds;
  }

  Future<List<WorkPlaceFeedEntity>> getFeedReactions(
    List<WorkPlaceFeedEntity> feeds,
  ) async {
    await _updateReactions(feeds);
    print("Done get feed reactions");

    return feeds;
  }

  Future<List<WorkPlaceFeedEntity>> saveAllFeeds(
    List<WorkPlaceFeedEntity> feeds,
  ) async {
    await saveFeeds(feeds);

    _saveCSV(feeds);

    return feeds;
  }

  String _saveCSV(List<WorkPlaceFeedEntity> feeds) {
    return addToQueueSync<String>(
          exportCsv: true,
          message: l10n.crawl_feed_save_feed_csv,
          job: () {
            final gpPosts = convertList<WorkPlaceFeedEntity, GPPost>(feeds);

            final gpGroupPosts = gpPosts.map((e) {
              return e.toJson();
            }).toList();

            return CsvHelper.toCSV(gpGroupPosts);
          },
        ) ??
        '';
  }
}

mixin _FilterEmail {
  void filterEmails(dynamic input) {
    // input.removeWhere((element) =>
    //     AppConstants.filterEmails.contains(element.from.value?.email) == false);
  }
}

extension _FeedExt on FeedQueue {
  Future<List<WorkPlaceFeedEntity>> _getFeedsFromGroups(
    List<WorkPlaceGroupEntity> groupsEntities,
  ) async {
    // final List<WorkPlaceGroupEntity> groups =
    //     await inputByCheckpoint<WorkPlaceGroupEntity>(
    //         GPBaseCrawlType.feed, groupsEntities);
    final List<WorkPlaceFeedEntity> output = [];

    Future actionPerThread(List<WorkPlaceGroupEntity> groups, int index) async {
      final groupCheckpoint =
          await inputByCheckpointMultiThread<WorkPlaceGroupEntity>(
              index: index, input: groups, type: GPBaseCrawlType.feed);
      final since = await getLastDateCheckpoint(GPBaseCrawlType.feed, index);
      final checkpointNextQuery =
          await getNextQuery(GPBaseCrawlType.feed, index);
      int itemIndex = groups
          .indexWhere((element) => element.id == groupCheckpoint.first.id);
      await Future.forEach(
        groupCheckpoint,
        (element) async {
          element.changeToDownloadStatus(
            status: BaseCrawlDownloadStatusEnum.downloading,
          );
          await saveCheckpointMultiThread(
              type: GPBaseCrawlType.feed,
              index: index,
              itemLength: groups.length,
              checkpointId: element.id,
              indexCurrentItem: itemIndex);

          await addToQueue(
            level: 2,
            message: l10n.crawl_feed_from_a_group(element.name ?? ''),
            job: () async {
              final result = await _getFeedByGroup(element,
                  since: since, nextQuery: checkpointNextQuery,
                  onSaveCheckpoint: (nextQuery) async {
                await saveCheckpointMultiThread(
                    type: GPBaseCrawlType.feed,
                    index: index,
                    itemLength: groups.length,
                    checkpointId: element.id,
                    indexCurrentItem: itemIndex,
                    nextQuery: nextQuery);
              });
              output.addAll(result);
            },
          );
        },
      );
      await setDoneCheckpointMultiThread(
          type: GPBaseCrawlType.feed, index: index);
    }

    await runInThreads(
      inputs: groupsEntities,
      actionPerThread: (inputs, index) async {
        return await actionPerThread(inputs, index);
      },
    );

    return output;

    // await setDoneCheckpoint(GPBaseCrawlType.feed);
  }

  Future _getFeedsFromMembers(
    List<WorkPlaceCommunityMemberEntity> memberEntities,
  ) async {
    // final List<WorkPlaceCommunityMemberEntity> members =
    //     await inputByCheckpoint<WorkPlaceCommunityMemberEntity>(
    //         GPBaseCrawlType.userFeed, memberEntities);

    Future actionPerThread(
        List<WorkPlaceCommunityMemberEntity> members, int index) async {
      final membersCheckpoint =
          await inputByCheckpointMultiThread<WorkPlaceCommunityMemberEntity>(
              index: index, input: members, type: GPBaseCrawlType.userFeed);
      final since =
          await getLastDateCheckpoint(GPBaseCrawlType.userFeed, index);
      final checkpointNextQuery =
          await getNextQuery(GPBaseCrawlType.userFeed, index);
      int itemIndex = members
          .indexWhere((element) => element.id == membersCheckpoint.first.id);
      await Future.forEach(
        membersCheckpoint,
        (element) async {
          element.changeToDownloadStatus(
            status: BaseCrawlDownloadStatusEnum.downloading,
          );
          await saveCheckpointMultiThread(
              type: GPBaseCrawlType.userFeed,
              index: index,
              itemLength: members.length,
              checkpointId: element.id,
              indexCurrentItem: itemIndex);

          await addToQueue(
            level: 2,
            message: l10n
                .crawl_feed_from_member(element.name ?? element.email ?? ''),
            job: () async {
              await _getFeedByMember(element,
                  since: since, nextQuery: checkpointNextQuery,
                  onSaveCheckpoint: (nextQuery) async {
                await saveCheckpointMultiThread(
                    type: GPBaseCrawlType.userFeed,
                    index: index,
                    itemLength: members.length,
                    checkpointId: element.id,
                    indexCurrentItem: itemIndex,
                    nextQuery: nextQuery);
              });
            },
          );
        },
      );
      await setDoneCheckpointMultiThread(
          type: GPBaseCrawlType.userFeed, index: index);
    }

    await runInThreads(
      inputs: memberEntities,
      actionPerThread: (inputs, index) async {
        return await actionPerThread(inputs, index);
      },
    );

    // await setDoneCheckpoint(GPBaseCrawlType.userFeed);
  }

  Future<List<WorkPlaceFeedEntity>> _getFeedByGroup(WorkPlaceGroupEntity group,
      {int? since,
      required Function(Map<String, String>?) onSaveCheckpoint,
      Map<String, String>? nextQuery}) async {
    final List<WorkPlaceFeedEntity> output = [];

    await addToQueue(
      message: l10n.crawl_feed_from_a_group(group.name ?? ''),
      job: () async {
        await wpGroupFeedsUseCase.execute(
          WorkPlaceGroupFeedsInput(
            groupId: group.id,
            since: since,
            nextQuery: nextQuery,
            saveData: (data, nextQuery) async {
              for (var element in data) {
                final postAttachments =
                    WorkPlaceEntityMapper.mapToWorkPlaceAttachmentEntity(
                        element);
                if (postAttachments != null) {
                  await saveAttachments(postAttachments);
                }
              }

              output.addAll(
                convertList<WorkPlaceFeedsResponse, WorkPlaceFeedEntity>(
                  data,
                ),
              );

              // bỏ bài post do bot đăng
              output.removeWhere((element) =>
                  element.from.value?.email == null || element.isPollVote);
              // output.removeWhere((element) => element.isPollVote);

              await saveFeeds(output);

              await _updateAttachments(output);

              for (var feed in output) {
                feed.group.value = group;

                // 15/09 ToanNM comment, du phan nay
                // await updateFeed(feed);
                // await reloadFeed(feed);
              }

              if (AppConstants.needRunOnGapoWork) {
                await gpQueue.uploadFeedAttachments(feeds: output);
              }

              await saveFeeds(output);
              onSaveCheckpoint.call(nextQuery);
            },
          ),
        );

        return output;
      },
    );

    return output;
  }

  Future<List<WorkPlaceFeedEntity>> _getFeedByMember(
      WorkPlaceCommunityMemberEntity user,
      {int? since,
      required Function(Map<String, String>?) onSaveCheckpoint,
      Map<String, String>? nextQuery}) async {
    final List<WorkPlaceFeedEntity> output = [];

    await addToQueue(
      message: l10n.crawl_feed_save_feed,
      job: () async {
        await wpUserFeedsUseCase.execute(
          WorkPlaceUserFeedsInput(
            userId: user.id,
            since: since,
            nextQuery: nextQuery,
            saveData: (data, nextQuery) async {
              final timelineFeeds =
                  data.where((element) => element.isPostInTimeline).toList();

              for (var element in timelineFeeds) {
                final postAttachments =
                    WorkPlaceEntityMapper.mapToWorkPlaceAttachmentEntity(
                        element);
                if (postAttachments != null) {
                  await saveAttachments(postAttachments);
                }
              }

              output.addAll(
                convertList<WorkPlaceFeedsResponse, WorkPlaceFeedEntity>(
                  timelineFeeds,
                ),
              );

              if (output.isEmpty) return;

              for (var feed in output) {
                feed.gpUserId = user.gpUserId;
                feed.isUserFeed = true;
              }

              await saveFeeds(output);

              await _updateAttachments(output);

              // 15/09 ToanNM comment, du phan nay
              // for (var element in output) {
              //   await updateFeed(element);
              //   await reloadFeed(element);
              // }
              if (AppConstants.needRunOnGapoWork) {
                await gpQueue.uploadFeedAttachments(feeds: output);
              }
              onSaveCheckpoint.call(nextQuery);
            },
          ),
        );
      },
    );

    return output;
  }
}

extension _AttachmentExt on FeedQueue {
  Future _updateAttachments(List<WorkPlaceFeedEntity> feeds) async {
    // final List<WorkPlaceFeedEntity> feedsGetData =
    //     await inputByCheckpoint<WorkPlaceFeedEntity>(
    //         GPBaseCrawlType.attachment, feeds);

    Future actionPerThread(
        List<WorkPlaceFeedEntity> feedsGetData, int index) async {
      // attachments
      await addToQueue(
        message: l10n.crawl_feed_get_attachments(feeds.length),
        job: () async {
          await Future.forEach(feedsGetData, (feed) async {
            await addToQueue(
              level: 2,
              message: l10n.crawl_feed_upload_attachments(feed.id),
              job: () async {
                final attachments = await _downloadAttachments(feed);

                feed.attachments.addAll(attachments);
              },
            );

            await updateFeed(feed);
            await reloadFeed(feed);
          });
        },
      );
    }

    await runInThreads(
      inputs: feeds,
      actionPerThread: (inputs, index) async {
        return await actionPerThread(inputs, index);
      },
      numberOfThreads: 20,
    );

    // await setDoneCheckpoint(GPBaseCrawlType.attachment);
  }

  Future<List<WorkPlaceAttachmentEntity>> _downloadAttachments(
    WorkPlaceFeedEntity feed,
  ) async {
    final List<WorkPlaceAttachmentEntity> flatAttachments =
        await flatternAttachments(feed);

    if (flatAttachments.isEmpty) return [];

    for (var attachmentEntity in flatAttachments) {
      try {
        if (attachmentEntity.gpLink?.isNotEmpty == true ||
            attachmentEntity.invalidAttachmentType) continue;

        final wpUrl = attachmentEntity.urlDownload();

        final downloadOutput =
            await downloadAttachment(entity: attachmentEntity, wpUrl: wpUrl);

        attachmentEntity.localFilePath = downloadOutput?.localFilePath;
      } catch (e) {
        saveLog('Error download attachment feed: $e', GPLogType.wpAttachments);
      }
    }

    await saveAttachments(flatAttachments);

    return flatAttachments;
  }
}

extension _CommentExt on FeedQueue {
  Future _updateComments(List<WorkPlaceFeedEntity> feeds) async {
    // final List<WorkPlaceFeedEntity> feedsGetData =
    //     await inputByCheckpoint<WorkPlaceFeedEntity>(
    //         GPBaseCrawlType.comment, feeds);

    Future actionPerThread(
        List<WorkPlaceFeedEntity> feedsGetData, int index) async {
      final feedCheckpoint =
          await inputByCheckpointMultiThread<WorkPlaceFeedEntity>(
              index: index, input: feedsGetData, type: GPBaseCrawlType.comment);
      int itemIndex = feedsGetData
          .indexWhere((element) => element.id == feedCheckpoint.first.id);
      // comments
      await addToQueue(
        message: l10n.crawl_feed_get_comments(feeds.length),
        job: () async {
          await Future.forEach(feedCheckpoint, (feed) async {
            await addToQueue(
              level: 2,
              message: l10n.crawl_feed_upload_comments(feed.id),
              job: () async {
                await saveCheckpointMultiThread(
                    type: GPBaseCrawlType.comment,
                    index: index,
                    itemLength: feedsGetData.length,
                    checkpointId: feed.id,
                    indexCurrentItem: itemIndex);
                final comments = await _getCommentsById(feed.id);
                feed.comments.addAll(comments);
              },
            );

            await updateFeedComment(feed);
            await reloadFeed(feed);
            itemIndex++;
          });
          await setDoneCheckpointMultiThread(
              type: GPBaseCrawlType.comment, index: index);
        },
      );
    }

    await runInThreads(
      inputs: feeds,
      actionPerThread: (inputs, index) async {
        return await actionPerThread(inputs, index);
      },
      numberOfThreads: 1,
    );

    if (AppConstants.needRunOnGapoWork) {
      await gpQueue.uploadCommentAttachments();
    }

    // await setDoneCheckpoint(GPBaseCrawlType.comment);
    // await setDoneCheckpoint(GPBaseCrawlType.commentReaction);
  }

  Future<List<WorkPlaceCommentEntity>> _getCommentsById(
    String id, {
    bool isGetReplies = false,
  }) async {
    List<WorkPlaceCommentEntity> output = [];

    if (isGetReplies) {
      await replyCommentsUseCase.execute(
        WorkPlaceReplyCommentsInput(
            cmtId: id,
            saveData: (data, _) async {
              output.addAll(await _saveCommentsToLocal(data));
            }),
      );
    } else {
      await commentsUseCase.execute(
        WorkPlacePostCommentsInput(
            postId: id,
            saveData: (data, _) async {
              output.addAll(await _saveCommentsToLocal(data));
            }),
      );
    }

    final List<WorkPlaceCommentEntity> result = [];

    for (var comment in output) {
      try {
        comment.attachment.value = await _downloadCommentAttachment(comment);
        comment.reactions = await _getCommentReactions(comment.id);
      } catch (e) {
        saveLog('Error download attachment, reaction comment: $e',
            GPLogType.wpComments);
      }

      // nếu đang lấy reply thì sẽ ko gọi lấy reply nữa
      if (isGetReplies == false) {
        try {
          final replies =
              await _getCommentsById(comment.id, isGetReplies: true);
          comment.replies.addAll(replies);
        } catch (e) {
          log(e.toString());
        }
      }

      await updateComment(comment);
      await reloadComment(comment);

      // replies + comment cha
      numComments += comment.replies.length + 1;

      result.add(comment);
    }

    await saveComments(result);

    return result;
  }

  Future<WorkPlaceAttachmentEntity?> _downloadCommentAttachment(
    WorkPlaceCommentEntity commentEntity,
  ) async {
    final WorkPlaceAttachmentEntity? attachmentEntity =
        commentEntity.attachment.value;

    if (attachmentEntity == null) return null;

    if (attachmentEntity.gpLink?.isNotEmpty == true) return attachmentEntity;

    final wpUrl = attachmentEntity.urlDownload();

    if (wpUrl.isEmpty) return null;

    final downloadOutput = await downloadAttachment(
      wpUrl: wpUrl,
      entity: commentEntity,
    );

    attachmentEntity.localFilePath = downloadOutput?.localFilePath;

    // if (downloadOutput == null) return null;aaaa

    // final response = await uploadAttachment(
    //   wpUrl: wpUrl,
    //   entity: commentEntity,
    //   downloadOutput: downloadOutput,
    //   onUploaded: (uploadUrl, uploadId) async {
    //     attachmentEntity.gpLink = uploadUrl;
    //     attachmentEntity.gpId = uploadId;
    //   },
    // );

    // if (response != null) {
    //   uploadResponseHandler.updateUploadResponse(attachmentEntity, response);
    // }

    await saveAttachment(attachmentEntity);

    return attachmentEntity;
  }

  Future<List<WorkPlaceCommentEntity>> _saveCommentsToLocal(
    List<WorkPlaceCommentsResponse> comments,
  ) async {
    final List<WorkPlaceCommentEntity> output =
        convertList<WorkPlaceCommentsResponse, WorkPlaceCommentEntity>(
            comments);

    for (var element in output) {
      final commentAttachment = element.attachment.value;

      if (commentAttachment != null) {
        await saveAttachments([commentAttachment]);
      }
    }

    await saveComments(output);

    for (var element in output) {
      await updateComment(element);
      await reloadComment(element);
    }

    return output;
  }
}

extension _ReactExt on FeedQueue {
  Future _updateReactions(List<WorkPlaceFeedEntity> feeds) async {
    // final List<WorkPlaceFeedEntity> feedsGetData =
    //     await inputByCheckpoint<WorkPlaceFeedEntity>(
    //         GPBaseCrawlType.reaction, feeds);

    Future actionPerThread(
        List<WorkPlaceFeedEntity> feedsGetData, int index) async {
      final feedCheckpoint =
          await inputByCheckpointMultiThread<WorkPlaceFeedEntity>(
              index: index,
              input: feedsGetData,
              type: GPBaseCrawlType.reaction);
      await addToQueue(
        message: l10n.crawl_feed_get_reactions(feeds.length),
        job: () async {
          int itemIndex = feedsGetData
              .indexWhere((element) => element.id == feedCheckpoint.first.id);
          await Future.forEach(feedCheckpoint, (feed) async {
            await addToQueue(
              level: 2,
              message: l10n.crawl_feed_get_reactions_from(feed.id),
              job: () async {
                await saveCheckpointMultiThread(
                    type: GPBaseCrawlType.reaction,
                    index: index,
                    itemLength: feedsGetData.length,
                    checkpointId: feed.id,
                    indexCurrentItem: itemIndex);
                final reactions = await _getReactions(feed.id);
                feed.reactions = reactions;
              },
            );
            await reloadFeed(feed);
            await saveFeeds([feed]);
            itemIndex++;
          });
          await setDoneCheckpointMultiThread(
              type: GPBaseCrawlType.reaction, index: index);
        },
      );
    }

    await runInThreads(
      inputs: feeds,
      actionPerThread: (inputs, index) async {
        return await actionPerThread(inputs, index);
      },
      numberOfThreads: 50,
    );

    // await setDoneCheckpoint(GPBaseCrawlType.reaction);
  }

  Future<List<WorkPlaceReactionEntity>> _getReactions<T>(String id) async {
    final WorkPlaceListReponse<WorkPlaceReaction> reactions =
        await reactionUseCase.execute(
      WorkPlacePostReactionsInput(postId: id),
    );

    final List<WorkPlaceReactionEntity> output =
        convertList<WorkPlaceReaction, WorkPlaceReactionEntity>(reactions.data);

    await _updateGPUserId(output);

    return output;
  }

  Future<List<WorkPlaceReactionEntity>> _getCommentReactions<T>(
      String id) async {
    final WorkPlaceListReponse<WorkPlaceReaction> reactions =
        await commentReactionUseCase.execute(
      WorkPlaceCommentReactionsInput(commentId: id),
    );

    final List<WorkPlaceReactionEntity> output =
        convertList<WorkPlaceReaction, WorkPlaceReactionEntity>(reactions.data);

    await _updateGPUserId(output);

    return output;
  }

  Future _updateGPUserId(List<WorkPlaceReactionEntity> reactions) async {
    final users = await getAllMembers();

    for (var reaction in reactions) {
      final user =
          users.where((element) => element.id == reaction.userId).toList();
      // reaction.gpUserId = user.isNotEmpty ? user.first.gpUserId : null;
      reaction.gpUserId = user.firstOrNull?.gpUserId;
    }
  }
}
