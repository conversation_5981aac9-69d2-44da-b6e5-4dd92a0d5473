/*
 * Created Date: Saturday, 1st June 2024, 16:52:07
 * Author: ToanN<PERSON>
 * -----
 * Last Modified: Saturday, 1st June 2024 16:52:19
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';

@module
abstract class NavigatorModule {
  @singleton
  @Named('kNavigatorKey')
  GlobalKey<NavigatorState> get kNavigatorKey =>
      GlobalKey(debugLabel: 'kNavigatorKey');

  @singleton
  @Named('kRootScaffoldKey')
  GlobalKey<ScaffoldState> get kRootScaffoldKey =>
      GlobalKey(debugLabel: 'kRootScaffoldKey');

  @singleton
  @Named('kScaffoldMessengerState')
  GlobalKey<ScaffoldMessengerState> get kRootScaffoldMessengerKey =>
      GlobalKey(debugLabel: 'kScaffoldMessengerState');
}
