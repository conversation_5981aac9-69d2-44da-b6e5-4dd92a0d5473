import 'package:gp_fbwp_crawler/domain/entity/enums/enums.dart';
import 'package:json_annotation/json_annotation.dart';

part 'gp_thread.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class GPThread {
  GPThread({
    this.userId,
    this.threadId,
    this.name,
    this.participantIds,
    this.type,
    this.isRead,
  });

  final int? userId;
  final String? threadId;
  final String? name;
  final List<int>? participantIds;
  final GPThreadType? type;
  final int? isRead;

  factory GPThread.fromJson(Map<String, dynamic> json) =>
      _$GPThreadFromJson(json);
  Map<String, dynamic> toJson() => _$GPThreadToJson(this);
}
