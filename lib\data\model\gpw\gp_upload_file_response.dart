import 'package:gp_core/core.dart';

part 'gp_upload_file_response.g.dart';

@JsonSerializable()
class GPUploadFileResponseModel {
  GPUploadFileResponseModel({
    this.id,
    this.name,
    this.userId,
    this.size,
    this.fileType,
    this.url,
    this.thumbUrl,
    this.fileLink,
    this.quality,
    this.source,
  });

  @<PERSON>son<PERSON><PERSON>(name: 'id')
  String? id;

  @Json<PERSON>ey(name: 'name')
  String? name;

  @<PERSON>son<PERSON>ey(name: 'user_id')
  String? userId;

  @J<PERSON><PERSON><PERSON>(name: 'size')
  int? size;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'file_type')
  String? fileType;

  @J<PERSON><PERSON><PERSON>(name: 'url')
  UploadFileURLResponseModel? url;

  @Json<PERSON><PERSON>(name: 'thumb_url')
  UploadFileURLResponseModel? thumbUrl;

  @JsonKey(name: 'src')
  String? src;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'file_link')
  String? fileLink;

  @Json<PERSON>ey(name: 'quality')
  String? quality;

  @Json<PERSON>ey(name: 'source')
  String? source;

  @JsonKey(name: 'type')
  String? type;

  factory GPUploadFileResponseModel.fromJson(Map<String, dynamic> json) =>
      _$GPUploadFileResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$GPUploadFileResponseModelToJson(this);
}
