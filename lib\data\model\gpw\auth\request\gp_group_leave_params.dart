// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'gp_group_leave_params.freezed.dart';
part 'gp_group_leave_params.g.dart';

@Freezed(
  fromJson: false,
  copyWith: true,
  toJson: true,
)
class GPGroupLeaveParams with _$GPGroupLeaveParams {
  const factory GPGroupLeaveParams({
    @JsonKey(name: 'group_id') required String groupId,
  }) = _GPGroupLeaveParams;
}
