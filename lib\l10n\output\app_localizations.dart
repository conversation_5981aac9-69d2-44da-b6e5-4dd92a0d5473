// GENERATED CODE - DO NOT MODIFY MANUALLY
// **************************************************************************
// Auto generated by <PERSON><PERSON> Developer
// **************************************************************************
import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_vi.dart';

/// Callers can lookup localized strings with an instance of S
/// returned by `S.of(context)`.
///
/// Applications need to include `S.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'output/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: S.localizationsDelegates,
///   supportedLocales: S.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the S.supportedLocales
/// property.
abstract class S {
  S(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static S? of(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  static const LocalizationsDelegate<S> delegate = _SDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('vi')
  ];

  /// No description provided for @app_name.
  ///
  /// In vi, this message translates to:
  /// **'GapoCrawler'**
  String get app_name;

  /// No description provided for @download_csv.
  ///
  /// In vi, this message translates to:
  /// **'Tải xuống CSV'**
  String get download_csv;

  /// No description provided for @download_csv_title.
  ///
  /// In vi, this message translates to:
  /// **'• Chọn vị trí lưu trữ file CSV'**
  String get download_csv_title;

  /// No description provided for @handle_duration.
  ///
  /// In vi, this message translates to:
  /// **'Xử lý trong: '**
  String get handle_duration;

  /// No description provided for @crawl_run_time_start_header.
  ///
  /// In vi, this message translates to:
  /// **'Thời điểm bắt đầu'**
  String get crawl_run_time_start_header;

  /// No description provided for @crawl_run_time_diff.
  ///
  /// In vi, this message translates to:
  /// **'Tool đã chạy trong'**
  String get crawl_run_time_diff;

  /// No description provided for @crawl_go_to_details.
  ///
  /// In vi, this message translates to:
  /// **'Xem chi tiết'**
  String get crawl_go_to_details;

  /// No description provided for @crawl_user_init_admin_account.
  ///
  /// In vi, this message translates to:
  /// **'• Khởi tạo tài khoản Admin ở GapoWork'**
  String get crawl_user_init_admin_account;

  /// No description provided for @crawl_user_get_wp_users.
  ///
  /// In vi, this message translates to:
  /// **'• Lấy thông tin người dùng ở WorkPlace'**
  String get crawl_user_get_wp_users;

  /// No description provided for @crawl_user_save_csv.
  ///
  /// In vi, this message translates to:
  /// **'• Lưu trữ thông tin người dùng vào file CSV'**
  String get crawl_user_save_csv;

  /// • Tống số dữ liệu cần crawl từ Facebook WorkPlace
  ///
  /// In vi, this message translates to:
  /// **'• Tống số dữ liệu cần crawl từ Facebook WorkPlace: {total} bản ghi'**
  String total_crawl_files(int total);

  /// • Lưu thông tin XXX người dùng
  ///
  /// In vi, this message translates to:
  /// **'• Lưu thông tin {count} người dùng'**
  String crawl_user_save_members(int count);

  /// • Đồng bộ thông tin XXX người dùng ở GapoWork
  ///
  /// In vi, this message translates to:
  /// **'• Đồng bộ thông tin {count} người dùng ở GapoWork'**
  String crawl_user_signup_users(int count);

  /// • Mời XXX người dùng vào workspace ở GapoWork
  ///
  /// In vi, this message translates to:
  /// **'• Mời {count} người dùng vào workspace ở GapoWork'**
  String crawl_user_invite_to_ws(int count);

  /// • Mời XXX vào workspace ở GapoWork
  ///
  /// In vi, this message translates to:
  /// **'• Mời {name} vào workspace ở GapoWork'**
  String crawl_user_invite_user_to_ws(String name);

  /// • Cập nhật thông tin XXX ở GapoWork
  ///
  /// In vi, this message translates to:
  /// **'• Cập nhật thông tin {name} ở GapoWork'**
  String crawl_user_update_gp_user_info(String name);

  /// No description provided for @crawl_group_get_groups.
  ///
  /// In vi, this message translates to:
  /// **'• Lấy thông tin nhóm ở WorkPlace'**
  String get crawl_group_get_groups;

  /// • Lấy thông tin ảnh cover cho XXX ở WorkPlace
  ///
  /// In vi, this message translates to:
  /// **'• Lấy thông tin ảnh cover cho {count} ở WorkPlace'**
  String crawl_group_upload_cover(int count);

  /// No description provided for @crawl_group_member_wp.
  ///
  /// In vi, this message translates to:
  /// **'• Lấy thông tin thành viên nhóm ở WorkPlace'**
  String get crawl_group_member_wp;

  /// • Lấy thông tin thành viên nhóm ở WorkPlace
  ///
  /// In vi, this message translates to:
  /// **'• Lấy thông tin thành viên nhóm {group_name}'**
  String crawl_group_member(String group_name);

  /// No description provided for @crawling_community.
  ///
  /// In vi, this message translates to:
  /// **'Lấy thông tin Community ở WorkPlace'**
  String get crawling_community;

  /// Lấy thông tin comunity ở WorkPlace
  ///
  /// In vi, this message translates to:
  /// **'Đồng bộ dữ liệu từ {name} ở WorkPlace'**
  String crawl_community(String name);

  /// No description provided for @crawl_group_save_group.
  ///
  /// In vi, this message translates to:
  /// **'• Lưu trữ dữ liệu nhóm vào bộ nhớ'**
  String get crawl_group_save_group;

  /// No description provided for @crawl_group_save_csv.
  ///
  /// In vi, this message translates to:
  /// **'• Lưu trữ thông tin nhóm vào file CSV'**
  String get crawl_group_save_csv;

  /// • Lấy thông tin bài viết từ XXX nhóm ở WorkPlace
  ///
  /// In vi, this message translates to:
  /// **'• Lấy thông tin bài viết từ {count} nhóm ở WorkPlace'**
  String crawl_feed_from_a_groups(int count);

  /// No description provided for @crawl_group_add_to_gp.
  ///
  /// In vi, this message translates to:
  /// **'Thêm nhóm vào GapoWork'**
  String get crawl_group_add_to_gp;

  /// • Thêm nhóm vào GapoWork
  ///
  /// In vi, this message translates to:
  /// **'• Thêm nhóm {group_name} vào GapoWork'**
  String crawl_group_to_gp(String group_name);

  /// No description provided for @crawl_group_invite_to_gp.
  ///
  /// In vi, this message translates to:
  /// **'Thêm thành viên vào nhóm ở GapoWork'**
  String get crawl_group_invite_to_gp;

  /// • Thêm thành viên vào nhóm GapoWork
  ///
  /// In vi, this message translates to:
  /// **'• Thêm thành viên vào nhóm {group_name} vào GapoWork'**
  String crawl_group_invite(String group_name);

  /// Chuyển quyền owner nhóm XXX ở WorkPlace
  ///
  /// In vi, this message translates to:
  /// **'Chuyển quyền owner nhóm {group_name}'**
  String crawl_group_change_owner(String group_name);

  /// Admin account rời nhóm XXX ở WorkPlace
  ///
  /// In vi, this message translates to:
  /// **'Admin account rời nhóm {group_name}'**
  String crawl_group_leave(String group_name);

  /// • Lấy thông tin bài viết trên nhóm XXX ở WorkPlace
  ///
  /// In vi, this message translates to:
  /// **'• Lấy thông tin bài viết trên nhóm \"{group_name}\" ở WorkPlace'**
  String crawl_feed_from_a_group(String group_name);

  /// • Lấy thông tin bài viết từ XXX người dùng ở WorkPlace
  ///
  /// In vi, this message translates to:
  /// **'• Lấy thông tin bài viết từ {count} người dùng ở WorkPlace'**
  String crawl_feed_from_a_members(int count);

  /// • Lấy thông tin bài viết từ người dùng XXX
  ///
  /// In vi, this message translates to:
  /// **'• Lấy thông tin bài viết từ người dùng \"{user_name}\"'**
  String crawl_feed_from_member(String user_name);

  /// No description provided for @crawl_feed_save_feed.
  ///
  /// In vi, this message translates to:
  /// **'• Lưu trữ dữ liệu bài viết vào bộ nhớ'**
  String get crawl_feed_save_feed;

  /// • Tải xuống các tệp đính kèm, hình ảnh, video từ XXX bài viết ở WorkPlace
  ///
  /// In vi, this message translates to:
  /// **'• Tải xuống các tệp đính kèm, hình ảnh, video từ {count} bài viết ở WorkPlace'**
  String crawl_feed_get_attachments(int count);

  /// • Tải xuống các tệp đính kèm, hình ảnh, video từ bình luận của XXX bài viết ở WorkPlace
  ///
  /// In vi, this message translates to:
  /// **'• Tải xuống các tệp đính kèm, hình ảnh, video từ bình luận của {count} bài viết ở WorkPlace'**
  String crawl_feed_get_comments(int count);

  /// • Tải xuống các tệp đính kèm, hình ảnh, video từ bài viết XXX ở WorkPlace
  ///
  /// In vi, this message translates to:
  /// **'• Tải lên các tệp đính kèm, hình ảnh, video từ bài viết \"{feed_name}\" ở WorkPlace'**
  String crawl_feed_upload_attachments(String feed_name);

  /// • Tải xuống các tệp đính kèm, hình ảnh, video từ  comment của bài viết XXX ở WorkPlace
  ///
  /// In vi, this message translates to:
  /// **'• Tải lên các tệp đính kèm, hình ảnh, video từ comment của bài viết {feed_name} ở WorkPlace'**
  String crawl_feed_upload_comments(String feed_name);

  /// No description provided for @crawl_feed_save_feed_csv.
  ///
  /// In vi, this message translates to:
  /// **'• Lưu trữ thông tin bài viết vào file CSV'**
  String get crawl_feed_save_feed_csv;

  /// No description provided for @crawl_thread_get_conversations.
  ///
  /// In vi, this message translates to:
  /// **'• Lấy thông tin cuộc hội thoại ở WorkPlace'**
  String get crawl_thread_get_conversations;

  /// • Tải xuống các tệp đính kèm, hình ảnh, video từ comment của bài viết XXX ở WorkPlace
  ///
  /// In vi, this message translates to:
  /// **'• Lấy thông tin cuộc hội thoại của người dùng \"{username}\" ở WorkPlace'**
  String crawl_thread_get_conversation_from_user(String username);

  /// • Lấy thông tin tương tác từ XXX bài viết ở WorkPlace
  ///
  /// In vi, this message translates to:
  /// **'• Lấy thông tin tương tác từ {count} bài viết ở WorkPlace'**
  String crawl_feed_get_reactions(int count);

  /// • Lấy thông tin tương tác từ bài viết XXX ở WorkPlace
  ///
  /// In vi, this message translates to:
  /// **'• Lấy thông tin tương tác bài viết \"{post_id}\"'**
  String crawl_feed_get_reactions_from(String post_id);

  /// • Lấy thông tin lượt xem từ XXX bài viết ở WorkPlace
  ///
  /// In vi, this message translates to:
  /// **'• Lấy thông tin lượt xem từ {count} bài viết ở WorkPlace'**
  String crawl_feed_get_seen(int count);

  /// • Lấy thông tin lượt xem từ bài viết XXX ở WorkPlace
  ///
  /// In vi, this message translates to:
  /// **'• Lấy thông tin lượt xem bài viết \"{post_id}\"'**
  String crawl_feed_get_seen_from(String post_id);

  /// No description provided for @crawl_feed_get_attachment.
  ///
  /// In vi, this message translates to:
  /// **'• Lấy thông tin attachment bài viết'**
  String get crawl_feed_get_attachment;

  /// No description provided for @crawl_feed_get_cmt.
  ///
  /// In vi, this message translates to:
  /// **'• Lấy thông tin attachment'**
  String get crawl_feed_get_cmt;

  /// No description provided for @crawl_feed_get_react.
  ///
  /// In vi, this message translates to:
  /// **'• Lấy thông tin reaction bài viết'**
  String get crawl_feed_get_react;

  /// No description provided for @crawl_thread_save_thread.
  ///
  /// In vi, this message translates to:
  /// **'• Lưu trữ dữ liệu hội thoại vào bộ nhớ'**
  String get crawl_thread_save_thread;

  /// No description provided for @crawl_thread_get_messages.
  ///
  /// In vi, this message translates to:
  /// **'• Lấy thông tin tin nhắn ở WorkPlace'**
  String get crawl_thread_get_messages;

  /// • Lấy thông tin tin nhắn id XXX ở WorkPlace ở WorkPlace
  ///
  /// In vi, this message translates to:
  /// **'• Lấy thông tin tin nhắn id = \"{message_id}\" ở WorkPlace'**
  String crawl_thread_get_message(String message_id);

  /// No description provided for @crawl_thread_get_conversation_attachment.
  ///
  /// In vi, this message translates to:
  /// **'• Tải xuống các tệp đính kèm, hình ảnh, video của tin nhắn từ cuộc hội thoại'**
  String get crawl_thread_get_conversation_attachment;

  /// • Tải xuống các tệp đính kèm, hình ảnh, video của tin nhắn từ cuộc hội thoại XXX ở WorkPlace
  ///
  /// In vi, this message translates to:
  /// **'• Tải xuống các tệp đính kèm, hình ảnh, video của tin nhắn từ cuộc hội thoại \"{thread_name}\" ở WorkPlace'**
  String crawl_thread_get_conversation_from_a_thread(String thread_name);

  /// • Tải xuống các tệp đính kèm, hình ảnh, video của tin nhắn từ tin nhắn XXX ở WorkPlace
  ///
  /// In vi, this message translates to:
  /// **'• Tải xuống các tệp đính kèm, hình ảnh, video của tin nhắn từ tin nhắn \"{message_id}\" ở WorkPlace'**
  String crawl_thread_get_attachment_from_a_message(String message_id);

  /// • Tải xuống sticker từ tin nhắn XXX ở WorkPlace
  ///
  /// In vi, this message translates to:
  /// **'• Tải xuống sticker từ tin nhắn \"{message_id}\" ở WorkPlace'**
  String crawl_thread_get_sticker_from_a_message(String message_id);

  /// No description provided for @crawl_thread_update_thread.
  ///
  /// In vi, this message translates to:
  /// **'• Cập nhật tin nhắn vào cuộc hội thoại'**
  String get crawl_thread_update_thread;

  /// No description provided for @crawl_thread_save_message.
  ///
  /// In vi, this message translates to:
  /// **'• Lưu trữ dữ liệu tin nhắn vào bộ nhớ'**
  String get crawl_thread_save_message;

  /// No description provided for @crawl_thread_save_thread_csv.
  ///
  /// In vi, this message translates to:
  /// **'• Lưu trữ thông tin cuộc hội thoại vào file CSV'**
  String get crawl_thread_save_thread_csv;

  /// No description provided for @crawl_thread_save_message_csv.
  ///
  /// In vi, this message translates to:
  /// **'• Lưu trữ thông tin tin nhắn vào file CSV'**
  String get crawl_thread_save_message_csv;
}

class _SDelegate extends LocalizationsDelegate<S> {
  const _SDelegate();

  @override
  Future<S> load(Locale locale) {
    return SynchronousFuture<S>(lookupS(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en', 'vi'].contains(locale.languageCode);

  @override
  bool shouldReload(_SDelegate old) => false;
}

S lookupS(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en': return SEn();
    case 'vi': return SVi();
  }

  throw FlutterError(
    'S.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
