import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'workplace_conversations_response.g.dart';

@JsonSerializable(createToJson: false)
class WorkPlaceUserConversationsResponse {
  WorkPlaceUserConversationsResponse({
    required this.id,
    this.messages,
    this.participants,
    this.userId,
    this.name,
    this.link,
  });

  final String id;

  final WorkPlaceListReponse<Messages>? messages;
  final WorkPlaceListReponse<WorkPlaceUser>? participants;

  final String? name;
  final String? link;

  String? userId;

  factory WorkPlaceUserConversationsResponse.fromJson(
          Map<String, dynamic> json) =>
      _$WorkPlaceUserConversationsResponseFromJson(json);
}

@JsonSerializable(fieldRename: FieldRename.snake, createToJson: false)
@DateTimeConverter()
class Messages {
  Messages({
    required this.id,
    this.attachments,
    this.message,
    this.from,
    this.to,
    this.createdTime,
    this.sticker,
  });

  final String id;

  final WorkPlaceListReponse<WorkPlaceConversationAttachmentsResponse>?
      attachments;

  final String? message;

  final WorkPlaceUser? from;
  final WorkPlaceListReponse<WorkPlaceUser>? to;
  final DateTime? createdTime;
  final String? sticker;

  factory Messages.fromJson(Map<String, dynamic> json) =>
      _$MessagesFromJson(json);
}
