// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workplace_conversations.entity.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetWorkPlaceConversationEntityCollection on Isar {
  IsarCollection<WorkPlaceConversationEntity>
      get workPlaceConversationEntitys => this.collection();
}

const WorkPlaceConversationEntitySchema = CollectionSchema(
  name: r'WorkPlaceConversationEntity',
  id: -3520045778021810140,
  properties: {
    r'crawlType': PropertySchema(
      id: 0,
      name: r'crawlType',
      type: IsarType.byte,
      enumMap: _WorkPlaceConversationEntitycrawlTypeEnumValueMap,
    ),
    r'gpUserId': PropertySchema(
      id: 1,
      name: r'gpUserId',
      type: IsarType.long,
    ),
    r'hashCode': PropertySchema(
      id: 2,
      name: r'hashCode',
      type: IsarType.long,
    ),
    r'id': PropertySchema(
      id: 3,
      name: r'id',
      type: IsarType.string,
    ),
    r'insertedAt': PropertySchema(
      id: 4,
      name: r'insertedAt',
      type: IsarType.dateTime,
    ),
    r'link': PropertySchema(
      id: 5,
      name: r'link',
      type: IsarType.string,
    ),
    r'name': PropertySchema(
      id: 6,
      name: r'name',
      type: IsarType.string,
    ),
    r'threadId': PropertySchema(
      id: 7,
      name: r'threadId',
      type: IsarType.string,
    ),
    r'wpUserId': PropertySchema(
      id: 8,
      name: r'wpUserId',
      type: IsarType.string,
    )
  },
  estimateSize: _workPlaceConversationEntityEstimateSize,
  serialize: _workPlaceConversationEntitySerialize,
  deserialize: _workPlaceConversationEntityDeserialize,
  deserializeProp: _workPlaceConversationEntityDeserializeProp,
  idName: r'dbId',
  indexes: {},
  links: {
    r'messages': LinkSchema(
      id: -4370805618360265333,
      name: r'messages',
      target: r'WorkPlaceMessagesEntity',
      single: false,
    ),
    r'participants': LinkSchema(
      id: -8822074970783523809,
      name: r'participants',
      target: r'WorkPlaceCommunityMemberEntity',
      single: false,
    )
  },
  embeddedSchemas: {},
  getId: _workPlaceConversationEntityGetId,
  getLinks: _workPlaceConversationEntityGetLinks,
  attach: _workPlaceConversationEntityAttach,
  version: '3.1.0+1',
);

int _workPlaceConversationEntityEstimateSize(
  WorkPlaceConversationEntity object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  bytesCount += 3 + object.id.length * 3;
  {
    final value = object.link;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.name;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.threadId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.wpUserId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _workPlaceConversationEntitySerialize(
  WorkPlaceConversationEntity object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeByte(offsets[0], object.crawlType.index);
  writer.writeLong(offsets[1], object.gpUserId);
  writer.writeLong(offsets[2], object.hashCode);
  writer.writeString(offsets[3], object.id);
  writer.writeDateTime(offsets[4], object.insertedAt);
  writer.writeString(offsets[5], object.link);
  writer.writeString(offsets[6], object.name);
  writer.writeString(offsets[7], object.threadId);
  writer.writeString(offsets[8], object.wpUserId);
}

WorkPlaceConversationEntity _workPlaceConversationEntityDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = WorkPlaceConversationEntity(
    crawlType: _WorkPlaceConversationEntitycrawlTypeValueEnumMap[
            reader.readByteOrNull(offsets[0])] ??
        GPBaseCrawlType.thread,
    id: reader.readString(offsets[3]),
    insertedAt: reader.readDateTimeOrNull(offsets[4]),
    link: reader.readStringOrNull(offsets[5]),
    name: reader.readStringOrNull(offsets[6]),
  );
  object.gpUserId = reader.readLongOrNull(offsets[1]);
  object.threadId = reader.readStringOrNull(offsets[7]);
  object.wpUserId = reader.readStringOrNull(offsets[8]);
  return object;
}

P _workPlaceConversationEntityDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (_WorkPlaceConversationEntitycrawlTypeValueEnumMap[
              reader.readByteOrNull(offset)] ??
          GPBaseCrawlType.thread) as P;
    case 1:
      return (reader.readLongOrNull(offset)) as P;
    case 2:
      return (reader.readLong(offset)) as P;
    case 3:
      return (reader.readString(offset)) as P;
    case 4:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 5:
      return (reader.readStringOrNull(offset)) as P;
    case 6:
      return (reader.readStringOrNull(offset)) as P;
    case 7:
      return (reader.readStringOrNull(offset)) as P;
    case 8:
      return (reader.readStringOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _WorkPlaceConversationEntitycrawlTypeEnumValueMap = {
  'user': 0,
  'group': 1,
  'thread': 2,
  'message': 3,
  'feed': 4,
  'userFeed': 5,
  'comment': 6,
  'attachment': 7,
  'community': 8,
  'sticker': 9,
  'reaction': 10,
  'commentReaction': 11,
  'groupMember': 12,
  'postSeen': 13,
  'messageAttachment': 14,
};
const _WorkPlaceConversationEntitycrawlTypeValueEnumMap = {
  0: GPBaseCrawlType.user,
  1: GPBaseCrawlType.group,
  2: GPBaseCrawlType.thread,
  3: GPBaseCrawlType.message,
  4: GPBaseCrawlType.feed,
  5: GPBaseCrawlType.userFeed,
  6: GPBaseCrawlType.comment,
  7: GPBaseCrawlType.attachment,
  8: GPBaseCrawlType.community,
  9: GPBaseCrawlType.sticker,
  10: GPBaseCrawlType.reaction,
  11: GPBaseCrawlType.commentReaction,
  12: GPBaseCrawlType.groupMember,
  13: GPBaseCrawlType.postSeen,
  14: GPBaseCrawlType.messageAttachment,
};

Id _workPlaceConversationEntityGetId(WorkPlaceConversationEntity object) {
  return object.dbId ?? Isar.autoIncrement;
}

List<IsarLinkBase<dynamic>> _workPlaceConversationEntityGetLinks(
    WorkPlaceConversationEntity object) {
  return [object.messages, object.participants];
}

void _workPlaceConversationEntityAttach(
    IsarCollection<dynamic> col, Id id, WorkPlaceConversationEntity object) {
  object.messages.attach(
      col, col.isar.collection<WorkPlaceMessagesEntity>(), r'messages', id);
  object.participants.attach(
      col,
      col.isar.collection<WorkPlaceCommunityMemberEntity>(),
      r'participants',
      id);
}

extension WorkPlaceConversationEntityQueryWhereSort on QueryBuilder<
    WorkPlaceConversationEntity, WorkPlaceConversationEntity, QWhere> {
  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterWhere> anyDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension WorkPlaceConversationEntityQueryWhere on QueryBuilder<
    WorkPlaceConversationEntity, WorkPlaceConversationEntity, QWhereClause> {
  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterWhereClause> dbIdEqualTo(Id dbId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: dbId,
        upper: dbId,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterWhereClause> dbIdNotEqualTo(Id dbId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: dbId, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: dbId, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: dbId, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: dbId, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterWhereClause> dbIdGreaterThan(Id dbId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: dbId, includeLower: include),
      );
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterWhereClause> dbIdLessThan(Id dbId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: dbId, includeUpper: include),
      );
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterWhereClause> dbIdBetween(
    Id lowerDbId,
    Id upperDbId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerDbId,
        includeLower: includeLower,
        upper: upperDbId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension WorkPlaceConversationEntityQueryFilter on QueryBuilder<
    WorkPlaceConversationEntity,
    WorkPlaceConversationEntity,
    QFilterCondition> {
  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> crawlTypeEqualTo(GPBaseCrawlType value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> crawlTypeGreaterThan(
    GPBaseCrawlType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> crawlTypeLessThan(
    GPBaseCrawlType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> crawlTypeBetween(
    GPBaseCrawlType lower,
    GPBaseCrawlType upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'crawlType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> dbIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'dbId',
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> dbIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'dbId',
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> dbIdEqualTo(Id? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> dbIdGreaterThan(
    Id? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> dbIdLessThan(
    Id? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> dbIdBetween(
    Id? lower,
    Id? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'dbId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> gpUserIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'gpUserId',
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> gpUserIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'gpUserId',
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> gpUserIdEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'gpUserId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> gpUserIdGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'gpUserId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> gpUserIdLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'gpUserId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> gpUserIdBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'gpUserId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> hashCodeEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'hashCode',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> hashCodeGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'hashCode',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> hashCodeLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'hashCode',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> hashCodeBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'hashCode',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> idEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> idGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> idLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> idBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> idStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> idEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
          QAfterFilterCondition>
      idContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
          QAfterFilterCondition>
      idMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'id',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> idIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> idIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> insertedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'insertedAt',
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> insertedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'insertedAt',
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> insertedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'insertedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> insertedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'insertedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> insertedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'insertedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> insertedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'insertedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> linkIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'link',
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> linkIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'link',
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> linkEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'link',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> linkGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'link',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> linkLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'link',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> linkBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'link',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> linkStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'link',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> linkEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'link',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
          QAfterFilterCondition>
      linkContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'link',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
          QAfterFilterCondition>
      linkMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'link',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> linkIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'link',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> linkIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'link',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> nameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'name',
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> nameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'name',
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> nameEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> nameGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> nameLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> nameBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'name',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> nameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> nameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
          QAfterFilterCondition>
      nameContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
          QAfterFilterCondition>
      nameMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'name',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> nameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'name',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> nameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'name',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> threadIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'threadId',
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> threadIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'threadId',
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> threadIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'threadId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> threadIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'threadId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> threadIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'threadId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> threadIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'threadId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> threadIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'threadId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> threadIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'threadId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
          QAfterFilterCondition>
      threadIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'threadId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
          QAfterFilterCondition>
      threadIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'threadId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> threadIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'threadId',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> threadIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'threadId',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> wpUserIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'wpUserId',
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> wpUserIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'wpUserId',
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> wpUserIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'wpUserId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> wpUserIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'wpUserId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> wpUserIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'wpUserId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> wpUserIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'wpUserId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> wpUserIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'wpUserId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> wpUserIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'wpUserId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
          QAfterFilterCondition>
      wpUserIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'wpUserId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
          QAfterFilterCondition>
      wpUserIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'wpUserId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> wpUserIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'wpUserId',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> wpUserIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'wpUserId',
        value: '',
      ));
    });
  }
}

extension WorkPlaceConversationEntityQueryObject on QueryBuilder<
    WorkPlaceConversationEntity,
    WorkPlaceConversationEntity,
    QFilterCondition> {}

extension WorkPlaceConversationEntityQueryLinks on QueryBuilder<
    WorkPlaceConversationEntity,
    WorkPlaceConversationEntity,
    QFilterCondition> {
  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> messages(FilterQuery<WorkPlaceMessagesEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'messages');
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> messagesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'messages', length, true, length, true);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> messagesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'messages', 0, true, 0, true);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> messagesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'messages', 0, false, 999999, true);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> messagesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'messages', 0, true, length, include);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> messagesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'messages', length, include, 999999, true);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> messagesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(
          r'messages', lower, includeLower, upper, includeUpper);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
          QAfterFilterCondition>
      participants(FilterQuery<WorkPlaceCommunityMemberEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'participants');
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> participantsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'participants', length, true, length, true);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> participantsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'participants', 0, true, 0, true);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> participantsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'participants', 0, false, 999999, true);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> participantsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'participants', 0, true, length, include);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> participantsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'participants', length, include, 999999, true);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterFilterCondition> participantsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(
          r'participants', lower, includeLower, upper, includeUpper);
    });
  }
}

extension WorkPlaceConversationEntityQuerySortBy on QueryBuilder<
    WorkPlaceConversationEntity, WorkPlaceConversationEntity, QSortBy> {
  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> sortByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> sortByCrawlTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> sortByGpUserId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpUserId', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> sortByGpUserIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpUserId', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> sortByHashCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hashCode', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> sortByHashCodeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hashCode', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> sortById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> sortByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> sortByInsertedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'insertedAt', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> sortByInsertedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'insertedAt', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> sortByLink() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'link', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> sortByLinkDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'link', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> sortByName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> sortByNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> sortByThreadId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'threadId', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> sortByThreadIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'threadId', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> sortByWpUserId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'wpUserId', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> sortByWpUserIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'wpUserId', Sort.desc);
    });
  }
}

extension WorkPlaceConversationEntityQuerySortThenBy on QueryBuilder<
    WorkPlaceConversationEntity, WorkPlaceConversationEntity, QSortThenBy> {
  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> thenByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> thenByCrawlTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> thenByDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dbId', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> thenByDbIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dbId', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> thenByGpUserId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpUserId', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> thenByGpUserIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'gpUserId', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> thenByHashCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hashCode', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> thenByHashCodeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hashCode', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> thenByInsertedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'insertedAt', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> thenByInsertedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'insertedAt', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> thenByLink() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'link', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> thenByLinkDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'link', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> thenByName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> thenByNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> thenByThreadId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'threadId', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> thenByThreadIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'threadId', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> thenByWpUserId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'wpUserId', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QAfterSortBy> thenByWpUserIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'wpUserId', Sort.desc);
    });
  }
}

extension WorkPlaceConversationEntityQueryWhereDistinct on QueryBuilder<
    WorkPlaceConversationEntity, WorkPlaceConversationEntity, QDistinct> {
  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QDistinct> distinctByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'crawlType');
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QDistinct> distinctByGpUserId() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'gpUserId');
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QDistinct> distinctByHashCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'hashCode');
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QDistinct> distinctById({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'id', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QDistinct> distinctByInsertedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'insertedAt');
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QDistinct> distinctByLink({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'link', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QDistinct> distinctByName({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'name', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QDistinct> distinctByThreadId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'threadId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, WorkPlaceConversationEntity,
      QDistinct> distinctByWpUserId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'wpUserId', caseSensitive: caseSensitive);
    });
  }
}

extension WorkPlaceConversationEntityQueryProperty on QueryBuilder<
    WorkPlaceConversationEntity, WorkPlaceConversationEntity, QQueryProperty> {
  QueryBuilder<WorkPlaceConversationEntity, int, QQueryOperations>
      dbIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'dbId');
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, GPBaseCrawlType, QQueryOperations>
      crawlTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'crawlType');
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, int?, QQueryOperations>
      gpUserIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'gpUserId');
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, int, QQueryOperations>
      hashCodeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'hashCode');
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, String, QQueryOperations>
      idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, DateTime?, QQueryOperations>
      insertedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'insertedAt');
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, String?, QQueryOperations>
      linkProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'link');
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, String?, QQueryOperations>
      nameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'name');
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, String?, QQueryOperations>
      threadIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'threadId');
    });
  }

  QueryBuilder<WorkPlaceConversationEntity, String?, QQueryOperations>
      wpUserIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'wpUserId');
    });
  }
}

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetWorkPlaceMessagesEntityCollection on Isar {
  IsarCollection<WorkPlaceMessagesEntity> get workPlaceMessagesEntitys =>
      this.collection();
}

const WorkPlaceMessagesEntitySchema = CollectionSchema(
  name: r'WorkPlaceMessagesEntity',
  id: -1936082985370271562,
  properties: {
    r'crawlType': PropertySchema(
      id: 0,
      name: r'crawlType',
      type: IsarType.byte,
      enumMap: _WorkPlaceMessagesEntitycrawlTypeEnumValueMap,
    ),
    r'createdTime': PropertySchema(
      id: 1,
      name: r'createdTime',
      type: IsarType.dateTime,
    ),
    r'id': PropertySchema(
      id: 2,
      name: r'id',
      type: IsarType.string,
    ),
    r'message': PropertySchema(
      id: 3,
      name: r'message',
      type: IsarType.string,
    ),
    r'wpThreadId': PropertySchema(
      id: 4,
      name: r'wpThreadId',
      type: IsarType.string,
    )
  },
  estimateSize: _workPlaceMessagesEntityEstimateSize,
  serialize: _workPlaceMessagesEntitySerialize,
  deserialize: _workPlaceMessagesEntityDeserialize,
  deserializeProp: _workPlaceMessagesEntityDeserializeProp,
  idName: r'dbId',
  indexes: {},
  links: {
    r'from': LinkSchema(
      id: 4023367832721422675,
      name: r'from',
      target: r'WorkPlaceCommunityMemberEntity',
      single: true,
    ),
    r'to': LinkSchema(
      id: 8024744342531387809,
      name: r'to',
      target: r'WorkPlaceCommunityMemberEntity',
      single: false,
    ),
    r'attachments': LinkSchema(
      id: 8675468723805154983,
      name: r'attachments',
      target: r'WorkPlaceConversationAttachmentsEntity',
      single: false,
    ),
    r'sticker': LinkSchema(
      id: 4450019743424396411,
      name: r'sticker',
      target: r'WorkPlaceStickerEntity',
      single: true,
    ),
    r'conversation': LinkSchema(
      id: -6032584315905126669,
      name: r'conversation',
      target: r'WorkPlaceConversationEntity',
      single: true,
      linkName: r'messages',
    )
  },
  embeddedSchemas: {},
  getId: _workPlaceMessagesEntityGetId,
  getLinks: _workPlaceMessagesEntityGetLinks,
  attach: _workPlaceMessagesEntityAttach,
  version: '3.1.0+1',
);

int _workPlaceMessagesEntityEstimateSize(
  WorkPlaceMessagesEntity object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  bytesCount += 3 + object.id.length * 3;
  {
    final value = object.message;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.wpThreadId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _workPlaceMessagesEntitySerialize(
  WorkPlaceMessagesEntity object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeByte(offsets[0], object.crawlType.index);
  writer.writeDateTime(offsets[1], object.createdTime);
  writer.writeString(offsets[2], object.id);
  writer.writeString(offsets[3], object.message);
  writer.writeString(offsets[4], object.wpThreadId);
}

WorkPlaceMessagesEntity _workPlaceMessagesEntityDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = WorkPlaceMessagesEntity(
    crawlType: _WorkPlaceMessagesEntitycrawlTypeValueEnumMap[
            reader.readByteOrNull(offsets[0])] ??
        GPBaseCrawlType.message,
    createdTime: reader.readDateTimeOrNull(offsets[1]),
    id: reader.readString(offsets[2]),
    message: reader.readStringOrNull(offsets[3]),
    wpThreadId: reader.readStringOrNull(offsets[4]),
  );
  return object;
}

P _workPlaceMessagesEntityDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (_WorkPlaceMessagesEntitycrawlTypeValueEnumMap[
              reader.readByteOrNull(offset)] ??
          GPBaseCrawlType.message) as P;
    case 1:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 2:
      return (reader.readString(offset)) as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    case 4:
      return (reader.readStringOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

const _WorkPlaceMessagesEntitycrawlTypeEnumValueMap = {
  'user': 0,
  'group': 1,
  'thread': 2,
  'message': 3,
  'feed': 4,
  'userFeed': 5,
  'comment': 6,
  'attachment': 7,
  'community': 8,
  'sticker': 9,
  'reaction': 10,
  'commentReaction': 11,
  'groupMember': 12,
  'postSeen': 13,
  'messageAttachment': 14,
};
const _WorkPlaceMessagesEntitycrawlTypeValueEnumMap = {
  0: GPBaseCrawlType.user,
  1: GPBaseCrawlType.group,
  2: GPBaseCrawlType.thread,
  3: GPBaseCrawlType.message,
  4: GPBaseCrawlType.feed,
  5: GPBaseCrawlType.userFeed,
  6: GPBaseCrawlType.comment,
  7: GPBaseCrawlType.attachment,
  8: GPBaseCrawlType.community,
  9: GPBaseCrawlType.sticker,
  10: GPBaseCrawlType.reaction,
  11: GPBaseCrawlType.commentReaction,
  12: GPBaseCrawlType.groupMember,
  13: GPBaseCrawlType.postSeen,
  14: GPBaseCrawlType.messageAttachment,
};

Id _workPlaceMessagesEntityGetId(WorkPlaceMessagesEntity object) {
  return object.dbId ?? Isar.autoIncrement;
}

List<IsarLinkBase<dynamic>> _workPlaceMessagesEntityGetLinks(
    WorkPlaceMessagesEntity object) {
  return [
    object.from,
    object.to,
    object.attachments,
    object.sticker,
    object.conversation
  ];
}

void _workPlaceMessagesEntityAttach(
    IsarCollection<dynamic> col, Id id, WorkPlaceMessagesEntity object) {
  object.from.attach(
      col, col.isar.collection<WorkPlaceCommunityMemberEntity>(), r'from', id);
  object.to.attach(
      col, col.isar.collection<WorkPlaceCommunityMemberEntity>(), r'to', id);
  object.attachments.attach(
      col,
      col.isar.collection<WorkPlaceConversationAttachmentsEntity>(),
      r'attachments',
      id);
  object.sticker.attach(
      col, col.isar.collection<WorkPlaceStickerEntity>(), r'sticker', id);
  object.conversation.attach(col,
      col.isar.collection<WorkPlaceConversationEntity>(), r'conversation', id);
}

extension WorkPlaceMessagesEntityQueryWhereSort
    on QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QWhere> {
  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QAfterWhere>
      anyDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension WorkPlaceMessagesEntityQueryWhere on QueryBuilder<
    WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QWhereClause> {
  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterWhereClause> dbIdEqualTo(Id dbId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: dbId,
        upper: dbId,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterWhereClause> dbIdNotEqualTo(Id dbId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: dbId, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: dbId, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: dbId, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: dbId, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterWhereClause> dbIdGreaterThan(Id dbId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: dbId, includeLower: include),
      );
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterWhereClause> dbIdLessThan(Id dbId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: dbId, includeUpper: include),
      );
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterWhereClause> dbIdBetween(
    Id lowerDbId,
    Id upperDbId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerDbId,
        includeLower: includeLower,
        upper: upperDbId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension WorkPlaceMessagesEntityQueryFilter on QueryBuilder<
    WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QFilterCondition> {
  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> crawlTypeEqualTo(GPBaseCrawlType value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> crawlTypeGreaterThan(
    GPBaseCrawlType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> crawlTypeLessThan(
    GPBaseCrawlType value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'crawlType',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> crawlTypeBetween(
    GPBaseCrawlType lower,
    GPBaseCrawlType upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'crawlType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> createdTimeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdTime',
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> createdTimeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdTime',
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> createdTimeEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdTime',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> createdTimeGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdTime',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> createdTimeLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdTime',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> createdTimeBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdTime',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> dbIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'dbId',
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> dbIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'dbId',
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> dbIdEqualTo(Id? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> dbIdGreaterThan(
    Id? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> dbIdLessThan(
    Id? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'dbId',
        value: value,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> dbIdBetween(
    Id? lower,
    Id? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'dbId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> idEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> idGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> idLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> idBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> idStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> idEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
          QAfterFilterCondition>
      idContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
          QAfterFilterCondition>
      idMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'id',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> idIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> idIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> messageIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'message',
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> messageIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'message',
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> messageEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> messageGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> messageLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> messageBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'message',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> messageStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> messageEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
          QAfterFilterCondition>
      messageContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'message',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
          QAfterFilterCondition>
      messageMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'message',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> messageIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'message',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> messageIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'message',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> wpThreadIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'wpThreadId',
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> wpThreadIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'wpThreadId',
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> wpThreadIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'wpThreadId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> wpThreadIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'wpThreadId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> wpThreadIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'wpThreadId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> wpThreadIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'wpThreadId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> wpThreadIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'wpThreadId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> wpThreadIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'wpThreadId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
          QAfterFilterCondition>
      wpThreadIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'wpThreadId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
          QAfterFilterCondition>
      wpThreadIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'wpThreadId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> wpThreadIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'wpThreadId',
        value: '',
      ));
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> wpThreadIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'wpThreadId',
        value: '',
      ));
    });
  }
}

extension WorkPlaceMessagesEntityQueryObject on QueryBuilder<
    WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QFilterCondition> {}

extension WorkPlaceMessagesEntityQueryLinks on QueryBuilder<
    WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QFilterCondition> {
  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
          QAfterFilterCondition>
      from(FilterQuery<WorkPlaceCommunityMemberEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'from');
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> fromIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'from', 0, true, 0, true);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> to(FilterQuery<WorkPlaceCommunityMemberEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'to');
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> toLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'to', length, true, length, true);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> toIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'to', 0, true, 0, true);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> toIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'to', 0, false, 999999, true);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> toLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'to', 0, true, length, include);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> toLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'to', length, include, 999999, true);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> toLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'to', lower, includeLower, upper, includeUpper);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
          QAfterFilterCondition>
      attachments(FilterQuery<WorkPlaceConversationAttachmentsEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'attachments');
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> attachmentsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'attachments', length, true, length, true);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> attachmentsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'attachments', 0, true, 0, true);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> attachmentsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'attachments', 0, false, 999999, true);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> attachmentsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'attachments', 0, true, length, include);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> attachmentsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'attachments', length, include, 999999, true);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> attachmentsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(
          r'attachments', lower, includeLower, upper, includeUpper);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> sticker(FilterQuery<WorkPlaceStickerEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'sticker');
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> stickerIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'sticker', 0, true, 0, true);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
          QAfterFilterCondition>
      conversation(FilterQuery<WorkPlaceConversationEntity> q) {
    return QueryBuilder.apply(this, (query) {
      return query.link(q, r'conversation');
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity,
      QAfterFilterCondition> conversationIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.linkLength(r'conversation', 0, true, 0, true);
    });
  }
}

extension WorkPlaceMessagesEntityQuerySortBy
    on QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QSortBy> {
  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QAfterSortBy>
      sortByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QAfterSortBy>
      sortByCrawlTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QAfterSortBy>
      sortByCreatedTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdTime', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QAfterSortBy>
      sortByCreatedTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdTime', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QAfterSortBy>
      sortById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QAfterSortBy>
      sortByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QAfterSortBy>
      sortByMessage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'message', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QAfterSortBy>
      sortByMessageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'message', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QAfterSortBy>
      sortByWpThreadId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'wpThreadId', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QAfterSortBy>
      sortByWpThreadIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'wpThreadId', Sort.desc);
    });
  }
}

extension WorkPlaceMessagesEntityQuerySortThenBy on QueryBuilder<
    WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QSortThenBy> {
  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QAfterSortBy>
      thenByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QAfterSortBy>
      thenByCrawlTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'crawlType', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QAfterSortBy>
      thenByCreatedTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdTime', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QAfterSortBy>
      thenByCreatedTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdTime', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QAfterSortBy>
      thenByDbId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dbId', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QAfterSortBy>
      thenByDbIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'dbId', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QAfterSortBy>
      thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QAfterSortBy>
      thenByMessage() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'message', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QAfterSortBy>
      thenByMessageDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'message', Sort.desc);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QAfterSortBy>
      thenByWpThreadId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'wpThreadId', Sort.asc);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QAfterSortBy>
      thenByWpThreadIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'wpThreadId', Sort.desc);
    });
  }
}

extension WorkPlaceMessagesEntityQueryWhereDistinct on QueryBuilder<
    WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QDistinct> {
  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QDistinct>
      distinctByCrawlType() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'crawlType');
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QDistinct>
      distinctByCreatedTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdTime');
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QDistinct>
      distinctById({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'id', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QDistinct>
      distinctByMessage({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'message', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QDistinct>
      distinctByWpThreadId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'wpThreadId', caseSensitive: caseSensitive);
    });
  }
}

extension WorkPlaceMessagesEntityQueryProperty on QueryBuilder<
    WorkPlaceMessagesEntity, WorkPlaceMessagesEntity, QQueryProperty> {
  QueryBuilder<WorkPlaceMessagesEntity, int, QQueryOperations> dbIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'dbId');
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, GPBaseCrawlType, QQueryOperations>
      crawlTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'crawlType');
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, DateTime?, QQueryOperations>
      createdTimeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdTime');
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, String, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, String?, QQueryOperations>
      messageProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'message');
    });
  }

  QueryBuilder<WorkPlaceMessagesEntity, String?, QQueryOperations>
      wpThreadIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'wpThreadId');
    });
  }
}
