// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'gp_group_invite_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$GPGroupInviteParams {
  @JsonKey(name: 'group_id')
  String get groupId => throw _privateConstructorUsedError;
  List<String> get users => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GPGroupInviteParamsCopyWith<GPGroupInviteParams> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GPGroupInviteParamsCopyWith<$Res> {
  factory $GPGroupInviteParamsCopyWith(
          GPGroupInviteParams value, $Res Function(GPGroupInviteParams) then) =
      _$GPGroupInviteParamsCopyWithImpl<$Res, GPGroupInviteParams>;
  @useResult
  $Res call({@JsonKey(name: 'group_id') String groupId, List<String> users});
}

/// @nodoc
class _$GPGroupInviteParamsCopyWithImpl<$Res, $Val extends GPGroupInviteParams>
    implements $GPGroupInviteParamsCopyWith<$Res> {
  _$GPGroupInviteParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? groupId = null,
    Object? users = null,
  }) {
    return _then(_value.copyWith(
      groupId: null == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as String,
      users: null == users
          ? _value.users
          : users // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GPGroupInviteParamsImplCopyWith<$Res>
    implements $GPGroupInviteParamsCopyWith<$Res> {
  factory _$$GPGroupInviteParamsImplCopyWith(_$GPGroupInviteParamsImpl value,
          $Res Function(_$GPGroupInviteParamsImpl) then) =
      __$$GPGroupInviteParamsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({@JsonKey(name: 'group_id') String groupId, List<String> users});
}

/// @nodoc
class __$$GPGroupInviteParamsImplCopyWithImpl<$Res>
    extends _$GPGroupInviteParamsCopyWithImpl<$Res, _$GPGroupInviteParamsImpl>
    implements _$$GPGroupInviteParamsImplCopyWith<$Res> {
  __$$GPGroupInviteParamsImplCopyWithImpl(_$GPGroupInviteParamsImpl _value,
      $Res Function(_$GPGroupInviteParamsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? groupId = null,
    Object? users = null,
  }) {
    return _then(_$GPGroupInviteParamsImpl(
      groupId: null == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as String,
      users: null == users
          ? _value._users
          : users // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _$GPGroupInviteParamsImpl implements _GPGroupInviteParams {
  const _$GPGroupInviteParamsImpl(
      {@JsonKey(name: 'group_id') required this.groupId,
      required final List<String> users})
      : _users = users;

  @override
  @JsonKey(name: 'group_id')
  final String groupId;
  final List<String> _users;
  @override
  List<String> get users {
    if (_users is EqualUnmodifiableListView) return _users;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_users);
  }

  @override
  String toString() {
    return 'GPGroupInviteParams(groupId: $groupId, users: $users)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GPGroupInviteParamsImpl &&
            (identical(other.groupId, groupId) || other.groupId == groupId) &&
            const DeepCollectionEquality().equals(other._users, _users));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, groupId, const DeepCollectionEquality().hash(_users));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GPGroupInviteParamsImplCopyWith<_$GPGroupInviteParamsImpl> get copyWith =>
      __$$GPGroupInviteParamsImplCopyWithImpl<_$GPGroupInviteParamsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GPGroupInviteParamsImplToJson(
      this,
    );
  }
}

abstract class _GPGroupInviteParams implements GPGroupInviteParams {
  const factory _GPGroupInviteParams(
      {@JsonKey(name: 'group_id') required final String groupId,
      required final List<String> users}) = _$GPGroupInviteParamsImpl;

  @override
  @JsonKey(name: 'group_id')
  String get groupId;
  @override
  List<String> get users;
  @override
  @JsonKey(ignore: true)
  _$$GPGroupInviteParamsImplCopyWith<_$GPGroupInviteParamsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
