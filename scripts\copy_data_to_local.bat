@echo off
setlocal enabledelayedexpansion

rem Display the current path
echo Current path: %CD%
echo.

rem Set the source folder using a relative path
set "source_folder=..\database"

rem Get the user's Documents folder
for /f "tokens=2*" %%a in ('reg query "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders" /v "Personal"') do set "documents_folder=%%b"

echo Source folder: %source_folder%
echo Destination folder: %documents_folder%
echo.

rem Check if source folder exists
if not exist "%source_folder%" (
    echo Source folder does not exist.
    goto :end
)

set "destination_folder=%documents_folder%"

rem Copy all files and folders
echo Copying files and folders...
xcopy "%source_folder%" "%destination_folder%" /E /I /Y

set "data_source_folder=..\data"
echo Source folder: %data_source_folder%
echo Destination folder: %documents_folder%\data
echo.
if not exist "%data_source_folder%" (
    echo Source folder does not exist.
    goto :end
)
set "data_destination_folder=%documents_folder%\splited_data"
if not exist "%data_destination_folder%" mkdir "%data_destination_folder%"
xcopy "%data_source_folder%" "%data_destination_folder%" /E /I /Y

if !errorlevel! equ 0 (
    echo.
    echo Copy process completed successfully.
) else (
    echo.
    echo Copy process completed with errors. Error code: !errorlevel!
)

:end
echo.
echo Press any key to exit...
pause >nul