import 'dart:async';
import 'dart:developer';

import 'package:get_it/get_it.dart';
import 'package:gp_fbwp_crawler/app/features/home/<USER>';
import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';

import 'base_queue.dart';

final class GPGroupQueue extends BaseGroupQueue {
  GPGroupQueue({required super.crawlQueueBloc, required super.commonBloc});

  bool isUploadingCovers = false;
  bool isCreatingGroup = false;

  Future<List<WorkPlaceGroupEntity>> uploadCoverToGapoWork(
      List<WorkPlaceGroupEntity> groupEntitys) async {
    final checkPoint = await getCheckpoint(GPBaseCrawlType.group);

    // Chỉ action khi đã kéo hết data
    if (checkPoint != null && !checkPoint.isDone) return [];

    if (isUploadingCovers) return groupEntitys;

    isUploadingCovers = true;

    await addToQueue(
      message: l10n.crawl_group_upload_cover(groupEntitys.length),
      job: () async {
        for (WorkPlaceGroupEntity element in groupEntitys) {
          await _uploadCovers(element);
        }
      },
    );

    await _afterUpload(groupEntitys);

    isUploadingCovers = false;

    return groupEntitys;
  }

  Future<List<WorkPlaceGroupEntity>> createGapoWorkGroups(
      List<WorkPlaceGroupEntity> groupEntitys) async {
    final checkPoint = await getCheckpoint(GPBaseCrawlType.group);
    final memberCheckPoint = await getCheckpoint(GPBaseCrawlType.groupMember);

    // Chỉ action khi đã kéo hết data
    if (checkPoint != null && !checkPoint.isDone) return [];
    if (memberCheckPoint != null && !memberCheckPoint.isDone) return [];

    if (isCreatingGroup) return groupEntitys;

    isCreatingGroup = true;

    await addToQueue(
      message: l10n.crawl_group_add_to_gp,
      job: () async {
        for (WorkPlaceGroupEntity element in groupEntitys) {
          if (element.gpGroupId != null) continue;

          final _gpGroup = await _createGPGroup(element);

          if (_gpGroup?.id != null) {
            element.gpGroupId = _gpGroup?.id;
            await saveGroups([element]);
            try {
              await _inviteMemberToGroup(_gpGroup!.id, element);
              await _changeGroupAdmin(element);
              await _leaveGroup(element);
            } catch (ex) {
              log('group queue error $ex');
            }
          }
        }

        await saveGroups(groupEntitys);
      },
    );

    isCreatingGroup = false;

    return groupEntitys;
  }
}

extension _UploadExt on GPGroupQueue {
  Future<WorkPlaceGroupEntity> _uploadCovers(
      WorkPlaceGroupEntity groupEntity) async {
    if (needToDownloadOrUpload(groupEntity)) {
      final wpUrl = groupEntity.cover?.source ?? '';

      await uploadAttachment(
        wpUrl: wpUrl,
        entity: groupEntity,
        downloadOutput: DownloadFileOutput(
            isFileDownloaded: true,
            localFilePath: groupEntity.cover?.localFilePath ?? ''),
        onUploaded: (uploadUrl, uploadId) async {
          groupEntity.gpCoverLink = uploadUrl;
          groupEntity.cover?.gpCoverLink = uploadUrl;
        },
        onDownloadFileSuccess: (path) {
          groupEntity.cover?.localFilePath = path;
        },
      );
    }

    return groupEntity;
  }

  Future _afterUpload(List<WorkPlaceGroupEntity> groupEntitys) async {
    await addToQueue(
      message: l10n.crawl_group_save_group,
      job: () async {
        await updateGroup(groupEntitys);

        // save all changes after upload covers
        await saveGroups(groupEntitys);
      },
    );
  }
}

extension _CreateGroupExt on GPGroupQueue {
  Future<GroupResponse?> _createGPGroup(WorkPlaceGroupEntity group) async {
    if (group.gpGroupId?.isNotEmpty ?? false) return null;
    await Future.delayed(const Duration(seconds: 3));
    final GroupResponse? gpGroup = await addToQueue(
        message: l10n.crawl_group_to_gp(group.name ?? group.id),
        job: () async {
          final params = GPGroupParams(
            name: group.name ?? group.id,
            cover: group.cover?.gpCoverLink ?? '',
            privacy: group.gpPrivacy,
            description: group.description,
            discoverability: group.gpDiscoverability,
          );
          final groupResponse = await gpCreateGroupUseCase
              .execute(GPCreateGroupInput(params: params));
          return groupResponse.data;
        });
    return gpGroup;
  }

  Future<void> _inviteMemberToGroup(
      String gpGroupId, WorkPlaceGroupEntity group) async {
    if (group.members.isEmpty) return;
    await Future.delayed(const Duration(seconds: 3));
    await addToQueue(
      message: l10n.crawl_group_invite(group.name ?? group.id),
      level: 2,
      job: () async {
        final users = group.members.map((e) => e.gpUserId.toString()).toList();
        users.removeWhere((element) => element == "null");
        await gpInviteGroupUseCase.execute(GPInviteGroupInput(
            params: GPGroupInviteParams(groupId: gpGroupId, users: users)));
      },
    );
  }

  Future<void> _changeGroupAdmin(WorkPlaceGroupEntity group) async {
    if (group.admins.isEmpty) return;
    await Future.delayed(const Duration(seconds: 1));
    await addToQueue(
      message: l10n.crawl_group_change_owner(group.name ?? ''),
      level: 2,
      job: () async {
        final admins = group.admins.toList();
        await Future.forEach(admins, (admin) async {
          await gpAdjustMemberRoleGroupUseCase.execute(
              GPAdjustMemberRoleGroupInput(
                  groupId: group.gpGroupId ?? '',
                  params: GPGroupAdjustMemberRoleParams(
                      userId: admin.gpUserId.toString(),
                      role: GPGroupRole.admin)));
        });
      },
    );
  }

  Future<void> _leaveGroup(WorkPlaceGroupEntity group) async {
    final emails = group.members.map((e) => e.email);
    final adminEmail = GetIt.I<String>(instanceName: 'kAdminEmail');
    if (emails.contains(adminEmail)) return;
    await Future.delayed(const Duration(seconds: 3));
    await addToQueue(
      message: l10n.crawl_group_leave(group.name ?? ''),
      level: 2,
      job: () async {
        await gpLeaveGroupUseCase.execute(GPLeaveGroupInput(
            params: GPGroupLeaveParams(
          groupId: group.gpGroupId ?? '',
        )));
      },
    );
  }
}
