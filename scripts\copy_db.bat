@echo off
setlocal enabledelayedexpansion

rem Display the current path
echo Current path: %CD%
echo.

set "database_folder=database"

if "%~1"=="" (
    echo You need enter a database folder name.
    goto :end
) else (
    set "database_folder=%~1"
)

rem Get the user's Documents folder
for /f "tokens=2*" %%a in ('reg query "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders" /v "Personal"') do set "documents_folder=%%b"

rem Set the source folder using a relative path
set "source_folder=%documents_folder%\%database_folder%"

rem Create a folder if not exist
set "destination_folder=..\database\%database_folder%"
if not exist "%destination_folder%" mkdir "%destination_folder%"

echo Source folder: %source_folder%
echo Destination folder: %destination_folder%
echo.

rem Check if source folder exists
if not exist "%source_folder%" (
    echo Source folder does not exist.
    goto :end
)

rem Copy all files and folders
echo Copying files and folders...
xcopy "%source_folder%" "%destination_folder%" /E /I /Y

if !errorlevel! equ 0 (
    echo.
    echo Copy process completed successfully.
) else (
    echo.
    echo Copy process completed with errors. Error code: !errorlevel!
)

rem Count files and folders
set "file_count=0"
set "folder_count=0"
for /r "%destination_folder%" %%F in (*) do set /a "file_count+=1"
for /d /r "%destination_folder%" %%D in (*) do set /a "folder_count+=1"

echo.
echo Files copied: %file_count%
echo Folders copied: %folder_count%

rem Git operations
echo.
echo Performing Git operations...

cd ..\

rem Add files to Git
git add database
if !errorlevel! neq 0 (
    echo Failed to add files to Git.
    goto :end
)

rem Commit changes
git commit -m "Automated commit: database"
if !errorlevel! neq 0 (
    echo Failed to commit changes.
    goto :end
)

rem Push changes
git push
if !errorlevel! neq 0 (
    echo Failed to push changes.
    goto :end
)

echo.
echo Git operations completed successfully.

:end
echo.
echo Press any key to exit...
pause >nul