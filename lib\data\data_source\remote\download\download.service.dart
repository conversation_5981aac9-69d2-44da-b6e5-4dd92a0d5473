/*
 * Created Date: Sunday, 11th August 2024, 14:12:26
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Monday, 9th September 2024 20:53:32
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:async';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core_v2/base/constants/constants.dart';
import 'package:injectable/injectable.dart';

@LazySingleton(order: DiConstants.kDataServiceOrder)
@Named('kDownloadService')
class DownloadService {
  late final Dio dio = GetIt.I<Dio>(instanceName: 'kWorkPlaceDownloadDio');

  Future<bool> downloadFile(String url, String savePath) async {
    try {
      Response response = await dio.download(
        url,
        savePath,
        onReceiveProgress: (int received, int total) {
          // log('Received: $received, Total: $total');
        },
      );

      if (response.statusCode == 200) {
        log('File downloaded successfully! $savePath');
        return true;
      } else {
        log('Download failed: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      log('Download error: $e\n$url');
      return false;
    }

    // finally {
    //   dio.close(force: true);
    // }
  }
}
