/*
 * Created Date: Sunday, 11th August 2024, 14:27:18
 * Author: <PERSON>an<PERSON><PERSON>
 * -----
 * Last Modified: Sunday, 8th September 2024 20:05:51
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_fbwp_crawler/domain/entity/entity.dart';
import 'package:isar/isar.dart';

part 'workplace_sticker.entity.g.dart';

@Collection()
class WorkPlaceStickerEntity extends BaseCrawlEntity {
  WorkPlaceStickerEntity({
    super.id = '',
    super.crawlType = GPBaseCrawlType.sticker,
    this.url,
  }) {
    super.id = '$dbId';
  }
  final String? url;

  String? gpLink;

  String? localFilePath;

  UploadResponseEntity? uploadResponse;

  late final Id? dbId = url.hashCode;
}
