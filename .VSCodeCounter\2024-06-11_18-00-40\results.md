# Summary

Date : 2024-06-11 18:00:40

Directory /Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler

Total : 248 files,  30186 codes, 1277 comments, 3456 blanks, all 34919 lines

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| Dart | 199 | 28,617 | 1,081 | 3,206 | 32,904 |
| C++ | 12 | 515 | 103 | 160 | 778 |
| XML | 9 | 469 | 44 | 9 | 522 |
| JSON | 11 | 274 | 0 | 3 | 277 |
| Groovy | 3 | 94 | 5 | 22 | 121 |
| YAML | 3 | 81 | 24 | 4 | 109 |
| Swift | 5 | 46 | 4 | 16 | 66 |
| Markdown | 2 | 43 | 0 | 14 | 57 |
| HTML | 1 | 35 | 16 | 17 | 68 |
| Java Properties | 2 | 8 | 0 | 2 | 10 |
| Kotlin | 1 | 4 | 0 | 3 | 7 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 248 | 30,186 | 1,277 | 3,456 | 34,919 |
| . (Files) | 4 | 121 | 24 | 16 | 161 |
| android | 12 | 171 | 47 | 33 | 251 |
| android (Files) | 3 | 46 | 0 | 11 | 57 |
| android/app | 8 | 120 | 47 | 21 | 188 |
| android/app (Files) | 1 | 51 | 5 | 12 | 68 |
| android/app/src | 7 | 69 | 42 | 9 | 120 |
| android/app/src/debug | 1 | 7 | 4 | 0 | 11 |
| android/app/src/main | 6 | 62 | 38 | 9 | 109 |
| android/app/src/main (Files) | 1 | 32 | 6 | 0 | 38 |
| android/app/src/main/kotlin | 1 | 4 | 0 | 3 | 7 |
| android/app/src/main/kotlin/vn | 1 | 4 | 0 | 3 | 7 |
| android/app/src/main/kotlin/vn/gapowork | 1 | 4 | 0 | 3 | 7 |
| android/app/src/main/kotlin/vn/gapowork/crawler | 1 | 4 | 0 | 3 | 7 |
| android/app/src/main/res | 4 | 26 | 32 | 6 | 64 |
| android/app/src/main/res/drawable | 1 | 4 | 7 | 2 | 13 |
| android/app/src/main/res/drawable-v21 | 1 | 4 | 7 | 2 | 13 |
| android/app/src/main/res/values | 1 | 9 | 9 | 1 | 19 |
| android/app/src/main/res/values-night | 1 | 9 | 9 | 1 | 19 |
| android/gradle | 1 | 5 | 0 | 1 | 6 |
| android/gradle/wrapper | 1 | 5 | 0 | 1 | 6 |
| assets | 2 | 2 | 0 | 0 | 2 |
| assets/images | 2 | 2 | 0 | 0 | 2 |
| ios | 11 | 232 | 4 | 13 | 249 |
| ios/Runner | 10 | 225 | 2 | 9 | 236 |
| ios/Runner (Files) | 2 | 13 | 0 | 3 | 16 |
| ios/Runner/Assets.xcassets | 6 | 151 | 0 | 4 | 155 |
| ios/Runner/Assets.xcassets/AppIcon-dev.appiconset | 1 | 1 | 0 | 0 | 1 |
| ios/Runner/Assets.xcassets/AppIcon-prod.appiconset | 1 | 1 | 0 | 0 | 1 |
| ios/Runner/Assets.xcassets/AppIcon-uat.appiconset | 1 | 1 | 0 | 0 | 1 |
| ios/Runner/Assets.xcassets/AppIcon.appiconset | 1 | 122 | 0 | 1 | 123 |
| ios/Runner/Assets.xcassets/LaunchImage.imageset | 2 | 26 | 0 | 3 | 29 |
| ios/Runner/Base.lproj | 2 | 61 | 2 | 2 | 65 |
| ios/RunnerTests | 1 | 7 | 2 | 4 | 13 |
| lib | 200 | 28,624 | 1,071 | 3,200 | 32,895 |
| lib (Files) | 3 | 24 | 0 | 9 | 33 |
| lib/app | 38 | 1,540 | 137 | 320 | 1,997 |
| lib/app (Files) | 4 | 291 | 5 | 68 | 364 |
| lib/app/app_config | 8 | 234 | 16 | 50 | 300 |
| lib/app/app_config (Files) | 1 | 2 | 0 | 1 | 3 |
| lib/app/app_config/bloc | 5 | 223 | 16 | 45 | 284 |
| lib/app/app_config/widgets | 2 | 9 | 0 | 4 | 13 |
| lib/app/base | 7 | 256 | 46 | 73 | 375 |
| lib/app/base (Files) | 1 | 1 | 0 | 1 | 2 |
| lib/app/base/networking | 6 | 255 | 46 | 72 | 373 |
| lib/app/base/networking (Files) | 3 | 107 | 7 | 27 | 141 |
| lib/app/base/networking/gapo | 3 | 148 | 39 | 45 | 232 |
| lib/app/constant | 4 | 39 | 24 | 13 | 76 |
| lib/app/features | 13 | 691 | 46 | 112 | 849 |
| lib/app/features (Files) | 1 | 1 | 0 | 1 | 2 |
| lib/app/features/crawler | 12 | 690 | 46 | 111 | 847 |
| lib/app/features/crawler (Files) | 3 | 113 | 2 | 7 | 122 |
| lib/app/features/crawler/bloc | 5 | 575 | 44 | 98 | 717 |
| lib/app/features/crawler/sync | 2 | 1 | 0 | 3 | 4 |
| lib/app/features/crawler/unsync | 2 | 1 | 0 | 3 | 4 |
| lib/app/splash | 2 | 29 | 0 | 4 | 33 |
| lib/config | 3 | 49 | 0 | 17 | 66 |
| lib/data | 63 | 3,370 | 196 | 479 | 4,045 |
| lib/data (Files) | 1 | 3 | 0 | 1 | 4 |
| lib/data/data_source | 13 | 800 | 74 | 113 | 987 |
| lib/data/data_source (Files) | 1 | 2 | 0 | 1 | 3 |
| lib/data/data_source/local | 2 | 46 | 9 | 14 | 69 |
| lib/data/data_source/remote | 10 | 752 | 65 | 98 | 915 |
| lib/data/data_source/remote (Files) | 5 | 422 | 30 | 53 | 505 |
| lib/data/data_source/remote/gapo | 5 | 330 | 35 | 45 | 410 |
| lib/data/model | 45 | 2,431 | 102 | 337 | 2,870 |
| lib/data/model (Files) | 2 | 15 | 0 | 4 | 19 |
| lib/data/model/facebook | 3 | 25 | 4 | 10 | 39 |
| lib/data/model/gpw | 14 | 359 | 50 | 76 | 485 |
| lib/data/model/gpw (Files) | 4 | 38 | 4 | 16 | 58 |
| lib/data/model/gpw/auth | 10 | 321 | 46 | 60 | 427 |
| lib/data/model/gpw/auth (Files) | 1 | 2 | 0 | 1 | 3 |
| lib/data/model/gpw/auth/request | 6 | 285 | 33 | 45 | 363 |
| lib/data/model/gpw/auth/response | 3 | 34 | 13 | 14 | 61 |
| lib/data/model/workplace | 26 | 2,032 | 48 | 247 | 2,327 |
| lib/data/model/workplace (Files) | 1 | 5 | 0 | 1 | 6 |
| lib/data/model/workplace/base | 8 | 1,548 | 22 | 156 | 1,726 |
| lib/data/model/workplace/conversation | 5 | 154 | 8 | 26 | 188 |
| lib/data/model/workplace/enums | 2 | 19 | 0 | 5 | 24 |
| lib/data/model/workplace/group | 5 | 156 | 10 | 27 | 193 |
| lib/data/model/workplace/post | 5 | 150 | 8 | 32 | 190 |
| lib/data/repository | 4 | 136 | 20 | 28 | 184 |
| lib/di | 11 | 485 | 78 | 63 | 626 |
| lib/di (Files) | 1 | 2 | 0 | 1 | 3 |
| lib/di/component | 3 | 322 | 17 | 18 | 357 |
| lib/di/modules | 7 | 161 | 61 | 44 | 266 |
| lib/domain | 63 | 21,542 | 379 | 2,115 | 24,036 |
| lib/domain (Files) | 1 | 3 | 0 | 1 | 4 |
| lib/domain/entity | 43 | 21,133 | 291 | 1,993 | 23,417 |
| lib/domain/entity (Files) | 1 | 4 | 0 | 1 | 5 |
| lib/domain/entity/base | 12 | 2,828 | 60 | 290 | 3,178 |
| lib/domain/entity/base (Files) | 2 | 99 | 10 | 18 | 127 |
| lib/domain/entity/base/app | 4 | 741 | 15 | 87 | 843 |
| lib/domain/entity/base/log | 3 | 1,035 | 15 | 97 | 1,147 |
| lib/domain/entity/base/status | 3 | 953 | 20 | 88 | 1,061 |
| lib/domain/entity/enums | 3 | 26 | 47 | 21 | 94 |
| lib/domain/entity/gapo | 4 | 1,387 | 9 | 160 | 1,556 |
| lib/domain/entity/workplace | 23 | 16,888 | 175 | 1,521 | 18,584 |
| lib/domain/repository | 4 | 41 | 22 | 20 | 83 |
| lib/domain/usecase | 15 | 365 | 66 | 101 | 532 |
| lib/domain/usecase (Files) | 11 | 264 | 37 | 72 | 373 |
| lib/domain/usecase/gapo | 4 | 101 | 29 | 29 | 159 |
| lib/flutter_gen | 2 | 69 | 10 | 15 | 94 |
| lib/helpers | 2 | 59 | 31 | 11 | 101 |
| lib/l10n | 6 | 100 | 85 | 34 | 219 |
| lib/mapper | 6 | 1,320 | 141 | 110 | 1,571 |
| lib/mapper (Files) | 3 | 252 | 73 | 39 | 364 |
| lib/mapper/entity | 3 | 1,068 | 68 | 71 | 1,207 |
| lib/route | 3 | 66 | 14 | 27 | 107 |
| linux | 3 | 86 | 18 | 27 | 131 |
| macos | 5 | 438 | 2 | 11 | 451 |
| macos/Runner | 4 | 431 | 0 | 7 | 438 |
| macos/Runner (Files) | 2 | 20 | 0 | 6 | 26 |
| macos/Runner/Assets.xcassets | 1 | 68 | 0 | 0 | 68 |
| macos/Runner/Assets.xcassets/AppIcon.appiconset | 1 | 68 | 0 | 0 | 68 |
| macos/Runner/Base.lproj | 1 | 343 | 0 | 1 | 344 |
| macos/RunnerTests | 1 | 7 | 2 | 4 | 13 |
| test | 1 | 14 | 10 | 6 | 30 |
| web | 2 | 70 | 16 | 18 | 104 |
| windows | 8 | 428 | 85 | 132 | 645 |
| windows/runner | 8 | 428 | 85 | 132 | 645 |

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)