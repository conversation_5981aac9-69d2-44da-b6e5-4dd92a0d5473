class WorkPlaceBaseParams {
  WorkPlaceBaseParams({
    required this.id,
    this.fields,
    this.limit = '5',
    this.nextQueries,
    this.since,
  });

  final String? fields;
  final String? limit;
  Map<String, String>? nextQueries;
  final int? since;

  String id;

  Map<String, String> get requestParams {
    if (nextQueries != null) return nextQueries!;

    final query = <String, String>{};

    if (fields != null) query['fields'] = fields!;
    if (limit != null) query['limit'] = limit!;
    if (since != null) query['since'] = since.toString();

    return query;
  }
}
