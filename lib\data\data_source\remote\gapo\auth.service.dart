/*
 * Created Date: Monday, 10th June 2024, 15:57:40
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 11th June 2024 17:59:22
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:dio/dio.dart' hide Headers;
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/app/constant/gapo_url.constants.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:injectable/injectable.dart';
import 'package:retrofit/retrofit.dart';

part 'auth.service.g.dart';

@LazySingleton(order: DiConstants.kDataServiceOrder)
@Named('kGapoWorkAuthService')
@RestApi()
abstract class AuthService {
  @FactoryMethod()
  factory AuthService(
    @Named('kGapoWorkDio') Dio dio, {
    @Named('kGapoWorkDomain') String? baseUrl,
  }) = _AuthService;

  @POST(GPConstants.kGapoWorkEmailPath)
  Future<ApiResponseV2<AuthCheckMailResponse>> checkEmail({
    @Body() required AuthCheckEmailRequest checkEmailRequest,
  });

  @POST(GPConstants.kGapoWorkAuthLogin)
  Future<ApiResponseV2<AuthResponse>> login({
    @Body() required AuthParams params,
  });

  @POST(GPConstants.kGapoWorkSignup)
  Future<ApiResponseV2<AuthResponse>> signUp({
    @Body() required GPSignupParams params,
  });

  @POST(GPConstants.kGapoWorkSetUserInfo)
  Future<ApiResponseV2WithoutData> setPassword({
    @Body() required GPSetPasswordParams params,
    @Header('Authorization') required String token,
  });
}
