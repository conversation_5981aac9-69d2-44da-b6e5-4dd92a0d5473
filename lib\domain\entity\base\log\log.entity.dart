/*
 * Created Date: Saturday, 1st June 2024, 12:43:54
 * Author: ToanNM
 * -----
 * Last Modified: Saturday, 14th September 2024 23:54:28
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:isar/isar.dart';

import '../../../../app/constant/fb_wp_url.constants.dart';
import '../../../../app/constant/gapo_url.constants.dart';

part 'log.entity.g.dart';

@Collection()
final class GPLogEntity {
  GPLogEntity({
    required this.cUrl,
    required this.path,
    required this.error,
    required this.createdAt,
    required this.logType,
    required this.data,
  });

  final Id id = Isar.autoIncrement;

  @Index(type: IndexType.value, caseSensitive: false)
  final String cUrl;

  final String path;

  final String error;

  @Index(type: IndexType.value, caseSensitive: false)
  @enumerated
  final GPLogType logType;

  final DateTime createdAt;

  final String data;
}

enum GPLogType {
  gpMembership([
    GPConstants.kGapoWorkMembershipGroup,
    GPConstants.kGapoWorkGroupAdjustMemberRole,
    GPConstants.kGapoWorkGroupLeave,
  ]),
  gpGroup([
    GPConstants.kGapoWorkGroup,
  ]),
  gpWorkSpace([
    GPConstants.kGapoWorkWorkspaceInviteEmail,
  ]),
  gpAuth([
    GPConstants.kGapoWorkEmailPath,
    GPConstants.kGapoWorkAuthLogin,
    GPConstants.kGapoWorkSignup,
    GPConstants.kGapoWorkSetUserInfo,
  ]),
  gpUpload([
    GPConstants.kGapoWorkFiles,
    GPConstants.kGapoWorkVideos,
    GPConstants.kGapoWorkImages,
    GPConstants.kGapoWorkAudios,
  ]),

  wpCommunity([
    FaceBookWorkPlaceConstants.kFaceBookCommunity,
  ]),
  wpLike([
    FaceBookWorkPlaceConstants.kWorkPlaceLike,
  ]),
  wpReactions([
    FaceBookWorkPlaceConstants.kWorkPlaceReactions,
  ]),
  wpSeen([
    FaceBookWorkPlaceConstants.kWorkPlaceSeen,
  ]),
  wpMessages([
    FaceBookWorkPlaceConstants.kWorkPlaceMessages,
  ]),
  wpMembers([
    FaceBookWorkPlaceConstants.kWorkPlaceMembers,
  ]),
  wpGroups([
    FaceBookWorkPlaceConstants.kWorkPlaceGroups,
  ]),
  wpFeed([
    FaceBookWorkPlaceConstants.kWorkPlaceFeed,
  ]),
  wpComments([
    FaceBookWorkPlaceConstants.kWorkPlaceComments,
  ]),
  wpAttachments([
    FaceBookWorkPlaceConstants.kWorkPlaceAttachments,
  ]),
  wpConversations([
    FaceBookWorkPlaceConstants.kWorkPlaceConversations,
  ]),
  other([]),
  upload([]),
  ;

  const GPLogType(this.paths);
  final List<String> paths;
}
