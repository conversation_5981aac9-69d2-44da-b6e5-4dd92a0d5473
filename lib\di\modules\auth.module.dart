/*
 * Created Date: Monday, 10th June 2024, 15:34:35
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 10th September 2024 22:53:52
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_fbwp_crawler/di/modules/modules.dart';
import 'package:injectable/injectable.dart';

@module
abstract class AuthModule {
  // --------- PROD -------- \\
  @Singleton(env: kFlavorSaasProds)
  @Named('kAdminEmail')
  String get kAdminEmailProd => '<EMAIL>';

  @Singleton(env: kFlavorSaasProds)
  @Named('kAdminPassword')
  String get kAdminPasswordProd => '123qweasdasdqweAdmin@';

  // --------- UAT -------- \\
  @Singleton(env: kFlavorSaasUat)
  @Named('kAdminEmail')
  String get kAdminEmailUat => '<EMAIL>';

  @Singleton(env: kFlavorSaasUat)
  @Named('kAdminPassword')
  String get kAdminPasswordUat => '1234567a';

  // --------- STAGING -------- \\
  @Singleton(env: kFlavorSaasDevs)
  @Named('kAdminEmail')
  String get kAdminEmailDev => '<EMAIL>';

  @Singleton(env: kFlavorSaasDevs)
  @Named('kAdminPassword')
  String get kAdminPasswordDev => '1234abcd@';
}
