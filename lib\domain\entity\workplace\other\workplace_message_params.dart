import 'package:gp_fbwp_crawler/domain/entity/workplace/workplace.dart';

class WorkPlaceMessageParams extends WorkPlaceBaseParams {
  WorkPlaceMessageParams({
    required super.id,
    super.fields,
    super.limit,
    super.nextQueries,
    required this.user,
  });
  final String user;

  @override
  Map<String, String> get requestParams {
    if (nextQueries != null) return nextQueries!;

    return {
      'user': user,
      'fields': fields ?? '',
      'limit': limit ?? '',
    };
  }
}
