import 'package:intl/intl.dart';
import 'package:json_annotation/json_annotation.dart';

class DateTimeConverter extends JsonConverter<DateTime?, String> {
  const DateTimeConverter();

  @override
  DateTime? fromJson(String json) {
    return DateFormat("yyyy-MM-ddTHH:mm:ssZ").parse(json).toLocal();
  }

  @override
  String toJson(DateTime? object) => object == null ? '' : DateFormat("yyyy-MM-ddTHH:mm:ssZ").format(object);
}