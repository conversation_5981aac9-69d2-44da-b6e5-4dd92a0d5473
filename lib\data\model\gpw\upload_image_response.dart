import 'package:gp_core/core.dart';

part 'upload_image_response.g.dart';

@JsonSerializable()
class GPUploadImageResponseModel {
  GPUploadImageResponseModel({
    this.id,
    this.userId,
    this.src,
    this.url,
    this.type,
    this.source,
    this.size,
    this.fileType,
    this.category,
    this.width,
    this.height,
    this.quality,
    this.name,
  });

  @<PERSON>son<PERSON>ey(name: 'id')
  String? id;

  @<PERSON>son<PERSON><PERSON>(name: 'user_id')
  String? userId;

  @Json<PERSON>ey(name: 'src')
  String? src;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'url')
  UploadFileURLResponseModel? url;

  @J<PERSON><PERSON><PERSON>(name: 'type')
  String? type;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'source')
  String? source;

  @Json<PERSON>ey(name: 'size')
  int? size;

  @<PERSON>son<PERSON><PERSON>(name: 'file_type')
  String? fileType;

  @Json<PERSON>ey(name: 'category')
  String? category;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'width')
  int? width;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'height')
  int? height;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'quality')
  String? quality;

  String? fileName;

  @Json<PERSON><PERSON>(name: 'file_link')
  String? fileLink;

  @Json<PERSON><PERSON>(name: 'name')
  String? name;

  factory GPUploadImageResponseModel.fromJson(Map<String, dynamic> json) =>
      _$GPUploadImageResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$GPUploadImageResponseModelToJson(this);
}
