// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';

part 'gp_group_adjust_member_role_params.freezed.dart';
part 'gp_group_adjust_member_role_params.g.dart';

@Freezed(
  fromJson: false,
  copyWith: true,
  toJson: true,
)
class GPGroupAdjustMemberRoleParams with _$GPGroupAdjustMemberRoleParams {
  const factory GPGroupAdjustMemberRoleParams({
    @JsonKey(name: 'user_id') required String userId,
    required GPGroupRole role,
  }) = _GPGroupAdjustMemberRoleParams;
}
