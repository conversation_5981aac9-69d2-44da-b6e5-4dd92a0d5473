import 'package:gp_core_v2/base/constants/constants.dart';
import 'package:gp_fbwp_crawler/data/data_source/data_source.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/repository/repository.dart';
import 'package:injectable/injectable.dart';

@LazySingleton(as: DownloadRepository, order: DiConstants.kDataRepositoryOrder)
@Named('kDownloadRepository')
final class DownloadRepositoryImpl implements DownloadRepository {
  const DownloadRepositoryImpl(
    @Named('kDownloadService') this.downloadService,
  );

  final DownloadService downloadService;

  @override
  Future<bool> downloadFile(GPDownloadParams params) async {
    return downloadService.downloadFile(params.downloadUrl, params.savedFilePath);
  }
}