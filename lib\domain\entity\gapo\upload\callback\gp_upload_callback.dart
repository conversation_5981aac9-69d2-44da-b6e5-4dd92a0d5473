import 'dart:io';

import 'package:gp_fbwp_crawler/data/model/gpw/upload_file_response.dart';
import 'package:gp_fbwp_crawler/domain/entity/gapo/upload/callback/gp_upload_progress.entity.dart';

/// callback khi upload toàn bộ files thành công
typedef OnUploadProcessCompleted = void Function();

/// callback khi progress có sự thay đổi
typedef OnFileUploadProgress = void Function(
  GPUploadProgressEntity progressModel,
);

/// callback khi bắt đầu quá trình upload files
typedef OnFileStartUpload = void Function(
  // total duration
  Duration estimate,
  int totalFiles,
);

// callback khi upload 1 file thành công
typedef OnAFileUploaded = void Function(
  int totalUploadedFiles,
  int totalFiles,
  UploadFileResponseModelWrapper uploadResponse,
);

/// callback khi upload 1 file, progress có sự thay đổi
typedef OnAFileUploadProgress = void Function(
  GPUploadProgressEntity progressModel,
  File uploadFile,
);

/// callback khi bắt đầu quá trình upload 1 file
typedef OnAFileStartUpload = void Function(
  Duration? estimate,
  File uploadFile,
);

class GPUploadCallback {
  GPUploadCallback({
    this.onAFileUploaded,
    this.onAFileStartUpload,
    this.onAFileUploadProgress,
  });

  final OnAFileStartUpload? onAFileStartUpload;
  final OnAFileUploadProgress? onAFileUploadProgress;
  final OnAFileUploaded? onAFileUploaded;
}

/// chứa các phần upload callback khi upload
class GPUploadDashBoardCallback {
  GPUploadDashBoardCallback({
    this.onUploadProcessCompleted,
    this.onAFileUploaded,
    this.onFileStartUpload,
    this.onFileProgress,
  });

  final OnFileStartUpload? onFileStartUpload;
  final OnFileUploadProgress? onFileProgress;
  final OnAFileUploaded? onAFileUploaded;

  final OnUploadProcessCompleted? onUploadProcessCompleted;
}
