// ignore_for_file: public_member_api_docs, invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'gp_auth_params.freezed.dart';
part 'gp_auth_params.g.dart';

@Freezed(
  fromJson: false,
  copyWith: true,
  toJson: true,
)
class AuthParams with _$AuthParams {
  const factory AuthParams({
    @Json<PERSON>ey(name: 'client_id') required String clientId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'device_model') required String deviceModel,
    required String password,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'trusted_device') @Default(true) bool trustedDevice,
    required String email,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'device_id') @Default('') String deviceId,
  }) = _AuthParams;
}
