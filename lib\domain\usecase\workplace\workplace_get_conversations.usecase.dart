/*
 * Created Date: Friday, 21st June 2024, 09:59:32
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 5th September 2024 15:38:51
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:developer';

import 'package:gp_core/core.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';
import 'package:retry/retry.dart';

// ignore_for_file: public_member_api_docs

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class WorkPlaceUserConversationsUseCase extends GPBaseFutureUseCase<
        WorkPlaceUserConversationInput,
        WorkPlaceListReponse<WorkPlaceUserConversationsResponse>>
    with WorkPlaceFetchAllDataMixin {
  WorkPlaceUserConversationsUseCase(
    @Named('kWorkPlaceRepository') this._worplaceRepository,
  );

  final WorkPlaceRepository _worplaceRepository;

  @override
  Future<WorkPlaceListReponse<WorkPlaceUserConversationsResponse>> buildUseCase(
    WorkPlaceUserConversationInput input,
  ) async {
    return retry(
      () async {
        final params = WorkPlaceBaseParams(
          id: input.userId,
          fields: input.fields,
          nextQueries: input.nextQuery,
          limit: '30',
        );

        return await fetchAllData<WorkPlaceUserConversationsResponse>(
          params: params,
          loadFunction: _worplaceRepository.conversations,
          saveData: input.saveData,
        );
      },
      maxAttempts: 2,
      retryIf: (e) {
        if (e is DioException && e.response?.statusCode == 400) {
          return false;
        }

        return true;
      },
      onRetry: (e) {
        log('ERROR: WorkPlaceUserConversationsUseCase error -> $e');
      },
    );
  }
}

final class WorkPlaceUserConversationInput extends GPBaseInput {
  const WorkPlaceUserConversationInput({
    required this.userId,
    this.fields = 'id,name,link,participants',
    required this.saveData,
    this.nextQuery,
  });

  final String userId;
  final String? fields;
  final Future Function(
      List<WorkPlaceUserConversationsResponse>, Map<String, String>?) saveData;
      final Map<String, String>? nextQuery;
}
