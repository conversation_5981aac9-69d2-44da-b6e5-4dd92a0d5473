/*
 * Created Date: Tuesday, 11th June 2024, 10:28:34
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 13th June 2024 08:52:27
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

// ignore_for_file: inference_failure_on_function_return_type
// ignore_for_file: public_member_api_docs

import 'dart:io';

import 'package:gp_core/core.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/app/constant/gapo_url.constants.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:injectable/injectable.dart';
import 'package:retrofit/retrofit.dart';

part 'upload.service.g.dart';

@LazySingleton(order: DiConstants.kDataServiceOrder)
@Named('kGapoWorkUploadService')
@RestApi()
abstract class UploadService {
  @FactoryMethod()
  factory UploadService(
    @Named('kGapoWorkUploadDio') Dio dio, {
    @Named('kGapoWorkUploadDomain') String? baseUrl,
  }) = _UploadService;

  /// # GapoWork chỉ hỗ trợ upload 1 file
  @POST(GPConstants.kGapoWorkFiles)
  @MultiPart()
  Future<GPUploadFileResponseModel> uploadFiles({
    @Part(name: 'file') required List<File> files,
    // @Path('file-size') int? fileSize,
    @SendProgress() void Function(int, int)? sendProgress,
    @ReceiveProgress() void Function(int, int)? receiveProgress,
    @CancelRequest() CancelToken? cancelToken,
  });

  /// # GapoWork chỉ hỗ trợ upload 1 file
  @POST(GPConstants.kGapoWorkVideos)
  @MultiPart()
  Future<GPUploadFileResponseModel> uploadVideos({
    @Query('format') String format = 'mp4',
    // String? fileName,
    @Part(name: 'video') required List<File> files,
    @SendProgress() void Function(int, int)? sendProgress,
    @ReceiveProgress() void Function(int, int)? receiveProgress,
    @CancelRequest() CancelToken? cancelToken,
  });

  /// # GapoWork chỉ hỗ trợ upload 1 file
  @POST(GPConstants.kGapoWorkImages)
  @MultiPart()
  Future<GPUploadImageResponseModel> uploadImages({
    @Query('quality') String quality = 'hd',
    @Part(name: 'image') required List<File> files,
    @SendProgress() void Function(int, int)? sendProgress,
    @ReceiveProgress() void Function(int, int)? receiveProgress,
    @CancelRequest() CancelToken? cancelToken,
  });

  @POST(GPConstants.kGapoWorkAudios)
  @MultiPart()
  Future<GPUploadFileResponseModel> uploadAudios({
    @Part(name: 'audio') required List<File> files,
    @SendProgress() void Function(int, int)? sendProgress,
    @ReceiveProgress() void Function(int, int)? receiveProgress,
    @CancelRequest() CancelToken? cancelToken,
  });
}
