# Diff Summary

Date : 2024-06-24 00:32:02

Directory c:\\Softwares\\Gapo\\gapoflutter-crawler

Total : 51 files,  -775 codes, -809 comments, -269 blanks, all -1853 lines

[Summary](results.md) / [Details](details.md) / Diff Summary / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| YAML | 1 | 1 | 0 | 0 | 1 |
| JSON | 1 | 1 | 0 | 0 | 1 |
| Dart | 49 | -777 | -809 | -269 | -1,855 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 51 | -775 | -809 | -269 | -1,853 |
| . (Files) | 1 | 1 | 0 | 0 | 1 |
| assets | 1 | 1 | 0 | 0 | 1 |
| assets\\images | 1 | 1 | 0 | 0 | 1 |
| lib | 49 | -777 | -809 | -269 | -1,855 |
| lib\\app | 48 | -780 | -810 | -270 | -1,860 |
| lib\\app\\features | 48 | -780 | -810 | -270 | -1,860 |
| lib\\app\\features\\crawler | 45 | -667 | -791 | -246 | -1,704 |
| lib\\app\\features\\crawler (Files) | 2 | 80 | 55 | 9 | 144 |
| lib\\app\\features\\crawler\\base_crawl_bloc | 8 | -84 | -2 | -22 | -108 |
| lib\\app\\features\\crawler\\base_crawl_bloc (Files) | 2 | -23 | 0 | -5 | -28 |
| lib\\app\\features\\crawler\\base_crawl_bloc\\bloc | 4 | -52 | -2 | -13 | -67 |
| lib\\app\\features\\crawler\\base_crawl_bloc\\widgets | 2 | -9 | 0 | -4 | -13 |
| lib\\app\\features\\crawler\\bloc | 6 | -345 | -759 | -123 | -1,227 |
| lib\\app\\features\\crawler\\crawl_feed | 6 | -272 | -31 | -92 | -395 |
| lib\\app\\features\\crawler\\crawl_feed (Files) | 1 | -1 | 0 | -1 | -2 |
| lib\\app\\features\\crawler\\crawl_feed\\bloc | 5 | -271 | -31 | -91 | -393 |
| lib\\app\\features\\crawler\\crawl_group | 6 | -188 | -21 | -57 | -266 |
| lib\\app\\features\\crawler\\crawl_group (Files) | 1 | -1 | 0 | -1 | -2 |
| lib\\app\\features\\crawler\\crawl_group\\bloc | 5 | -187 | -21 | -56 | -264 |
| lib\\app\\features\\crawler\\crawl_member | 6 | -183 | -23 | -55 | -261 |
| lib\\app\\features\\crawler\\crawl_member (Files) | 1 | -1 | 0 | -1 | -2 |
| lib\\app\\features\\crawler\\crawl_member\\bloc | 5 | -182 | -23 | -54 | -259 |
| lib\\app\\features\\crawler\\crawl_thread | 6 | -198 | -31 | -61 | -290 |
| lib\\app\\features\\crawler\\crawl_thread (Files) | 1 | -1 | 0 | -1 | -2 |
| lib\\app\\features\\crawler\\crawl_thread\\bloc | 5 | -197 | -31 | -60 | -288 |
| lib\\app\\features\\crawler\\queues | 5 | 523 | 21 | 155 | 699 |
| lib\\app\\features\\home | 3 | -113 | -19 | -24 | -156 |
| lib\\app\\features\\home (Files) | 1 | -6 | -4 | 0 | -10 |
| lib\\app\\features\\home\\bloc | 2 | -107 | -15 | -24 | -146 |
| lib\\flutter_gen | 1 | 3 | 1 | 1 | 5 |

[Summary](results.md) / [Details](details.md) / Diff Summary / [Diff Details](diff-details.md)