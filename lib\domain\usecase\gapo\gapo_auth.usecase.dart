/*
 * Created Date: Tuesday, 11th June 2024, 10:51:06
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 11th June 2024 17:12:17
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:convert';
import 'dart:math';

import 'package:get_it/get_it.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';

// ignore_for_file: public_member_api_docs

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class GPAuthUseCase
    extends GPBaseFutureUseCase<GPAuthInput, ApiResponseV2<AuthResponse>> {
  const GPAuthUseCase(
    @Named('kGapoRepository') this._gpRepository,
  );

  final GapoRepository _gpRepository;

  @override
  Future<ApiResponseV2<AuthResponse>> buildUseCase(GPAuthInput input) async {
    final checkEmailResponse = await GetIt.I<AuthCheckMailUseCase>().execute(
      AuthCheckEmailRequest(
        input.params.email,
        "",
      ),
    );

    final pass = input.params.password;
    final salt = checkEmailResponse.data.salt;

    if (salt is String) {
      var step1 = pass + salt;
      var step2 = sha256.convert(utf8.encode(step1)).toString();
      var step3 = step2 + salt;
      var password =
          sha256.convert(utf8.encode(step3)).toString().toLowerCase();

      var deviceId =
          'ab596683-3806-4e9c-9c2e-e0500002cacc${Random().nextInt(100)}';

      return _gpRepository.login(input.params.copyWith(
        password: password,
        deviceId: deviceId,
      ));
    }

    return ApiResponseV2(data: AuthResponse());
  }
}

class GPAuthInput extends GPBaseInput {
  const GPAuthInput({
    required this.params,
  });

  final AuthParams params;
}
