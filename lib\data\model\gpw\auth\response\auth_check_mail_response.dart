/*
 * Created Date: 5/12/2023 16:05:26
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Friday, 5th January 2024 14:53:31
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:json_annotation/json_annotation.dart';

part 'auth_check_mail_response.g.dart';

@JsonSerializable()
class AuthCheckMailResponse {
  AuthCheckMailResponse({
    required this.userId,
    this.newDomain,
    this.salt,
  });

  @Json<PERSON><PERSON>(name: 'user_id')
  final int userId;

  @Json<PERSON>ey(name: 'new_domain')
  final bool? newDomain;

  final String? salt;

  factory AuthCheckMailResponse.fromJson(Map<String, dynamic> json) =>
      _$AuthCheckMailResponseFromJson(json);
  Map<String, dynamic> toJson() => _$AuthCheckMailResponseToJson(this);
}
