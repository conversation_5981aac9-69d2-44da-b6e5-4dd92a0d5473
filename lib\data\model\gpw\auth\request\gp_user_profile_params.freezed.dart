// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'gp_user_profile_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$GPUserProfileParams {
  @JsonKey(name: 'display_name')
  String get displayName => throw _privateConstructorUsedError;
  String? get avatar => throw _privateConstructorUsedError;
  @JsonKey(name: 'avatar_data')
  GPUserProfilePictureData? get avatarData =>
      throw _privateConstructorUsedError;
  @JsonKey(name: 'cover_data')
  GPUserProfilePictureData? get coverData => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GPUserProfileParamsCopyWith<GPUserProfileParams> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GPUserProfileParamsCopyWith<$Res> {
  factory $GPUserProfileParamsCopyWith(
          GPUserProfileParams value, $Res Function(GPUserProfileParams) then) =
      _$GPUserProfileParamsCopyWithImpl<$Res, GPUserProfileParams>;
  @useResult
  $Res call(
      {@JsonKey(name: 'display_name') String displayName,
      String? avatar,
      @JsonKey(name: 'avatar_data') GPUserProfilePictureData? avatarData,
      @JsonKey(name: 'cover_data') GPUserProfilePictureData? coverData});

  $GPUserProfilePictureDataCopyWith<$Res>? get avatarData;
  $GPUserProfilePictureDataCopyWith<$Res>? get coverData;
}

/// @nodoc
class _$GPUserProfileParamsCopyWithImpl<$Res, $Val extends GPUserProfileParams>
    implements $GPUserProfileParamsCopyWith<$Res> {
  _$GPUserProfileParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? displayName = null,
    Object? avatar = freezed,
    Object? avatarData = freezed,
    Object? coverData = freezed,
  }) {
    return _then(_value.copyWith(
      displayName: null == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String,
      avatar: freezed == avatar
          ? _value.avatar
          : avatar // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarData: freezed == avatarData
          ? _value.avatarData
          : avatarData // ignore: cast_nullable_to_non_nullable
              as GPUserProfilePictureData?,
      coverData: freezed == coverData
          ? _value.coverData
          : coverData // ignore: cast_nullable_to_non_nullable
              as GPUserProfilePictureData?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $GPUserProfilePictureDataCopyWith<$Res>? get avatarData {
    if (_value.avatarData == null) {
      return null;
    }

    return $GPUserProfilePictureDataCopyWith<$Res>(_value.avatarData!, (value) {
      return _then(_value.copyWith(avatarData: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $GPUserProfilePictureDataCopyWith<$Res>? get coverData {
    if (_value.coverData == null) {
      return null;
    }

    return $GPUserProfilePictureDataCopyWith<$Res>(_value.coverData!, (value) {
      return _then(_value.copyWith(coverData: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$GPUserProfileParamsImplCopyWith<$Res>
    implements $GPUserProfileParamsCopyWith<$Res> {
  factory _$$GPUserProfileParamsImplCopyWith(_$GPUserProfileParamsImpl value,
          $Res Function(_$GPUserProfileParamsImpl) then) =
      __$$GPUserProfileParamsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'display_name') String displayName,
      String? avatar,
      @JsonKey(name: 'avatar_data') GPUserProfilePictureData? avatarData,
      @JsonKey(name: 'cover_data') GPUserProfilePictureData? coverData});

  @override
  $GPUserProfilePictureDataCopyWith<$Res>? get avatarData;
  @override
  $GPUserProfilePictureDataCopyWith<$Res>? get coverData;
}

/// @nodoc
class __$$GPUserProfileParamsImplCopyWithImpl<$Res>
    extends _$GPUserProfileParamsCopyWithImpl<$Res, _$GPUserProfileParamsImpl>
    implements _$$GPUserProfileParamsImplCopyWith<$Res> {
  __$$GPUserProfileParamsImplCopyWithImpl(_$GPUserProfileParamsImpl _value,
      $Res Function(_$GPUserProfileParamsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? displayName = null,
    Object? avatar = freezed,
    Object? avatarData = freezed,
    Object? coverData = freezed,
  }) {
    return _then(_$GPUserProfileParamsImpl(
      displayName: null == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String,
      avatar: freezed == avatar
          ? _value.avatar
          : avatar // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarData: freezed == avatarData
          ? _value.avatarData
          : avatarData // ignore: cast_nullable_to_non_nullable
              as GPUserProfilePictureData?,
      coverData: freezed == coverData
          ? _value.coverData
          : coverData // ignore: cast_nullable_to_non_nullable
              as GPUserProfilePictureData?,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _$GPUserProfileParamsImpl implements _GPUserProfileParams {
  const _$GPUserProfileParamsImpl(
      {@JsonKey(name: 'display_name') required this.displayName,
      this.avatar,
      @JsonKey(name: 'avatar_data') this.avatarData,
      @JsonKey(name: 'cover_data') this.coverData});

  @override
  @JsonKey(name: 'display_name')
  final String displayName;
  @override
  final String? avatar;
  @override
  @JsonKey(name: 'avatar_data')
  final GPUserProfilePictureData? avatarData;
  @override
  @JsonKey(name: 'cover_data')
  final GPUserProfilePictureData? coverData;

  @override
  String toString() {
    return 'GPUserProfileParams(displayName: $displayName, avatar: $avatar, avatarData: $avatarData, coverData: $coverData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GPUserProfileParamsImpl &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.avatarData, avatarData) ||
                other.avatarData == avatarData) &&
            (identical(other.coverData, coverData) ||
                other.coverData == coverData));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, displayName, avatar, avatarData, coverData);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GPUserProfileParamsImplCopyWith<_$GPUserProfileParamsImpl> get copyWith =>
      __$$GPUserProfileParamsImplCopyWithImpl<_$GPUserProfileParamsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GPUserProfileParamsImplToJson(
      this,
    );
  }
}

abstract class _GPUserProfileParams implements GPUserProfileParams {
  const factory _GPUserProfileParams(
      {@JsonKey(name: 'display_name') required final String displayName,
      final String? avatar,
      @JsonKey(name: 'avatar_data') final GPUserProfilePictureData? avatarData,
      @JsonKey(name: 'cover_data')
      final GPUserProfilePictureData? coverData}) = _$GPUserProfileParamsImpl;

  @override
  @JsonKey(name: 'display_name')
  String get displayName;
  @override
  String? get avatar;
  @override
  @JsonKey(name: 'avatar_data')
  GPUserProfilePictureData? get avatarData;
  @override
  @JsonKey(name: 'cover_data')
  GPUserProfilePictureData? get coverData;
  @override
  @JsonKey(ignore: true)
  _$$GPUserProfileParamsImplCopyWith<_$GPUserProfileParamsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$GPUserProfilePictureData {
  String? get id => throw _privateConstructorUsedError;
  String? get source => throw _privateConstructorUsedError;
  String? get src => throw _privateConstructorUsedError;
  int? get width => throw _privateConstructorUsedError;
  int? get height => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GPUserProfilePictureDataCopyWith<GPUserProfilePictureData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GPUserProfilePictureDataCopyWith<$Res> {
  factory $GPUserProfilePictureDataCopyWith(GPUserProfilePictureData value,
          $Res Function(GPUserProfilePictureData) then) =
      _$GPUserProfilePictureDataCopyWithImpl<$Res, GPUserProfilePictureData>;
  @useResult
  $Res call({String? id, String? source, String? src, int? width, int? height});
}

/// @nodoc
class _$GPUserProfilePictureDataCopyWithImpl<$Res,
        $Val extends GPUserProfilePictureData>
    implements $GPUserProfilePictureDataCopyWith<$Res> {
  _$GPUserProfilePictureDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? source = freezed,
    Object? src = freezed,
    Object? width = freezed,
    Object? height = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      source: freezed == source
          ? _value.source
          : source // ignore: cast_nullable_to_non_nullable
              as String?,
      src: freezed == src
          ? _value.src
          : src // ignore: cast_nullable_to_non_nullable
              as String?,
      width: freezed == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as int?,
      height: freezed == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GPUserProfilePictureDataImplCopyWith<$Res>
    implements $GPUserProfilePictureDataCopyWith<$Res> {
  factory _$$GPUserProfilePictureDataImplCopyWith(
          _$GPUserProfilePictureDataImpl value,
          $Res Function(_$GPUserProfilePictureDataImpl) then) =
      __$$GPUserProfilePictureDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? id, String? source, String? src, int? width, int? height});
}

/// @nodoc
class __$$GPUserProfilePictureDataImplCopyWithImpl<$Res>
    extends _$GPUserProfilePictureDataCopyWithImpl<$Res,
        _$GPUserProfilePictureDataImpl>
    implements _$$GPUserProfilePictureDataImplCopyWith<$Res> {
  __$$GPUserProfilePictureDataImplCopyWithImpl(
      _$GPUserProfilePictureDataImpl _value,
      $Res Function(_$GPUserProfilePictureDataImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? source = freezed,
    Object? src = freezed,
    Object? width = freezed,
    Object? height = freezed,
  }) {
    return _then(_$GPUserProfilePictureDataImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      source: freezed == source
          ? _value.source
          : source // ignore: cast_nullable_to_non_nullable
              as String?,
      src: freezed == src
          ? _value.src
          : src // ignore: cast_nullable_to_non_nullable
              as String?,
      width: freezed == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as int?,
      height: freezed == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _$GPUserProfilePictureDataImpl implements _GPUserProfilePictureData {
  const _$GPUserProfilePictureDataImpl(
      {this.id, this.source, this.src, this.width, this.height});

  @override
  final String? id;
  @override
  final String? source;
  @override
  final String? src;
  @override
  final int? width;
  @override
  final int? height;

  @override
  String toString() {
    return 'GPUserProfilePictureData(id: $id, source: $source, src: $src, width: $width, height: $height)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GPUserProfilePictureDataImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.source, source) || other.source == source) &&
            (identical(other.src, src) || other.src == src) &&
            (identical(other.width, width) || other.width == width) &&
            (identical(other.height, height) || other.height == height));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, source, src, width, height);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GPUserProfilePictureDataImplCopyWith<_$GPUserProfilePictureDataImpl>
      get copyWith => __$$GPUserProfilePictureDataImplCopyWithImpl<
          _$GPUserProfilePictureDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GPUserProfilePictureDataImplToJson(
      this,
    );
  }
}

abstract class _GPUserProfilePictureData implements GPUserProfilePictureData {
  const factory _GPUserProfilePictureData(
      {final String? id,
      final String? source,
      final String? src,
      final int? width,
      final int? height}) = _$GPUserProfilePictureDataImpl;

  @override
  String? get id;
  @override
  String? get source;
  @override
  String? get src;
  @override
  int? get width;
  @override
  int? get height;
  @override
  @JsonKey(ignore: true)
  _$$GPUserProfilePictureDataImplCopyWith<_$GPUserProfilePictureDataImpl>
      get copyWith => throw _privateConstructorUsedError;
}
