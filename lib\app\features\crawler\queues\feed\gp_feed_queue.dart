import 'dart:async';
import 'dart:developer';

import 'package:async_task/async_task_extension.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_fbwp_crawler/app/app.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:isar/isar.dart';

final class GPFeedQueue extends BaseFeedQueue {
  GPFeedQueue({required super.crawlQueueBloc, required super.commonBloc});

  bool isUploadingAttachments = false;
  bool isUploadingCommentAttachments = false;

  Future<List<WorkPlaceFeedEntity>> uploadFeedAttachments(
      {List<WorkPlaceFeedEntity>? feeds}) async {
    // final checkPoint = await getCheckpoint(GPBaseCrawlType.feed);

    // // Chỉ action khi đã kéo hết data
    // if (checkPoint != null && !checkPoint.isDone) return [];

    feeds ??= await getAllFeeds();

    // if (isUploadingAttachments) return feeds;

    // isUploadingAttachments = true;

    Future actionPerThread(List<WorkPlaceFeedEntity> feeds) async {
      await Future.forEach(feeds, (feed) async {
        await addToQueue(
          level: 2,
          message: l10n.crawl_feed_upload_attachments(feed.id),
          job: () async {
            final attachments = await _uploadAttachments(feed);

            feed.attachments.addAll(attachments);
          },
        );

        // await updateFeed(feed);
        // await reloadFeed(feed);
      });
    }

    await addToQueue(
      message: l10n.crawl_feed_get_attachments(feeds.length),
      job: () async {
        await runInThreads<WorkPlaceFeedEntity>(
          inputs: feeds ?? [],
          actionPerThread: (inputs, _) async {
            await actionPerThread(inputs);
          },
          numberOfThreads: 25,
        );
      },
    );

    isUploadingAttachments = false;

    return feeds;
  }

  Future<List<WorkPlaceCommentEntity>> uploadCommentAttachments() async {
    // final checkPoint = await getCheckpoint(GPBaseCrawlType.comment);

    // // Chỉ action khi đã kéo hết data
    // if (checkPoint != null && !checkPoint.isDone) return [];

    final comments = await getAllComments();

    if (isUploadingCommentAttachments) return comments;

    isUploadingCommentAttachments = true;

    Future actionPerThread(List<WorkPlaceCommentEntity> comments) async {
      await Future.forEach(comments, (comment) async {
        await _uploadCommentAttachment(comment);

        await updateComment(comment);
        await reloadComment(comment);
      });
    }

    await addToQueue(
      message: l10n.crawl_gp_upload_comment_attachments(comments.length),
      job: () async {
        await runInThreads<WorkPlaceCommentEntity>(
          inputs: comments ?? [],
          actionPerThread: (inputs, _) async {
            await actionPerThread(inputs);
          },
          numberOfThreads: 15,
        );
      },
    );

    isUploadingCommentAttachments = false;

    return comments;
  }

  Future uploadMissingAttachments() async {
    final attachments = await localService.missingAttachments();
    print('preparing uploadMissingAttachments ${attachments.length}');

    Future actionPerThread(
        List<WorkPlaceAttachmentEntity> attachments, int index) async {
      int itemIndex = 1;
      for (var attachmentEntity in attachments) {
        print(
            '$index || ${(itemIndex / attachments.length * 100).toStringAsFixed(1)}%');
        if (attachmentEntity.gpLink?.isNotEmpty == true ||
            attachmentEntity.invalidAttachmentType) {
          itemIndex++;
          continue;
        }

        log('uploadMissingAttachments ${attachmentEntity.id}');

        try {
          await _uploadAnAttachment(attachmentEntity);
        } catch (e) {
          print('Error upload attachment: ${attachmentEntity.id}');
          localService.saveLog("${attachmentEntity.id} || ${e.toString()}",
              GPLogType.wpAttachments);
        }

        log('uploadMissingAttachments ${attachmentEntity.id} done');

        localService.saveAttachment(attachmentEntity);
        itemIndex++;
      }
    }

    await runInThreads<WorkPlaceAttachmentEntity>(
      inputs: attachments.toList(),
      actionPerThread: (inputs, index) async {
        await actionPerThread(inputs, index);
      },
      numberOfThreads: 25,
    );
    print('Done missing attachment');
  }
}

extension _AttachmentExt on GPFeedQueue {
  Future<List<WorkPlaceAttachmentEntity>> _uploadAttachments(
    WorkPlaceFeedEntity feed,
  ) async {
    final List<WorkPlaceAttachmentEntity> flatAttachments =
        await flatternAttachments(feed);

    if (flatAttachments.isEmpty) return [];

    for (var attachmentEntity in flatAttachments) {
      if (attachmentEntity.gpLink?.isNotEmpty == true ||
          attachmentEntity.invalidAttachmentType) continue;

      // if (attachmentEntity.localFilePath == null) {
      //   continue;
      // }
      await _uploadAnAttachment(attachmentEntity, feed: feed);
    }

    await saveAttachments(flatAttachments);

    return flatAttachments;
  }

  Future _uploadAnAttachment(
    WorkPlaceAttachmentEntity attachmentEntity, {
    WorkPlaceFeedEntity? feed,
  }) async {
    final wpUrl = attachmentEntity.urlDownload();

    if (wpUrl.isEmpty) {
      log('DEBUG: upload: type=${attachmentEntity.type} $wpUrl, ${attachmentEntity.id} ${feed?.id}');
      await _saveLog(
        "Upload with wpUrl is empty, data: ${attachmentEntity.type} $wpUrl, ${attachmentEntity.id} ${feed?.id}",
      );
    }

    final response = await uploadAttachment(
      wpUrl: wpUrl,
      entity: attachmentEntity,
      downloadOutput: DownloadFileOutput(
        isFileDownloaded: true,
        localFilePath: attachmentEntity.localFilePath ?? '',
      ),
      onUploaded: (uploadUrl, uploadId) {
        attachmentEntity.gpLink = uploadUrl;
        attachmentEntity.gpId = uploadId;
      },
      onDownloadFileSuccess: (path) {
        attachmentEntity.localFilePath = path;
      },
    );

    if (response != null) {
      uploadResponseHandler.updateUploadResponse(attachmentEntity, response);
    }
  }

  Future _saveLog(String data) async {
    final Isar _isar = GetIt.I<Isar>(instanceName: 'kIsar');

    await _isar.writeTxn(() async {
      await _isar.gPLogEntitys.put(GPLogEntity(
        cUrl: '',
        path: 'upload',
        error: 'wpUrl is empty',
        createdAt: DateTime.now(),
        logType: GPLogType.upload,
        data: data,
      ));
    });
  }
}

extension _CommentExt on GPFeedQueue {
  Future<WorkPlaceAttachmentEntity?> _uploadCommentAttachment(
    WorkPlaceCommentEntity commentEntity,
  ) async {
    final WorkPlaceAttachmentEntity? attachmentEntity =
        commentEntity.attachment.value;

    if (attachmentEntity == null) return null;

    if (attachmentEntity.gpLink?.isNotEmpty == true) return attachmentEntity;

    if (attachmentEntity.localFilePath == null) {
      return attachmentEntity;
    }

    final wpUrl = attachmentEntity.urlDownload();

    if (wpUrl.isEmpty) return null;

    final response = await uploadAttachment(
      wpUrl: wpUrl,
      entity: commentEntity,
      downloadOutput: DownloadFileOutput(
        isFileDownloaded: true,
        localFilePath: attachmentEntity.localFilePath ?? '',
      ),
      onUploaded: (uploadUrl, uploadId) async {
        attachmentEntity.gpLink = uploadUrl;
        attachmentEntity.gpId = uploadId;
      },
      onDownloadFileSuccess: (path) {
        attachmentEntity.localFilePath = path;
      },
    );

    if (response != null) {
      uploadResponseHandler.updateUploadResponse(attachmentEntity, response);
    }

    await saveAttachment(attachmentEntity);

    return attachmentEntity;
  }
}
