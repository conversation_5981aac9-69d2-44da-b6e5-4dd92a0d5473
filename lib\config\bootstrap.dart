import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core/core.dart';
import 'package:gp_fbwp_crawler/config/app_configs.dart';
import 'package:gp_fbwp_crawler/di/component/app.component.dart' as app;
import 'package:talker/talker.dart';
import 'package:talker_bloc_logger/talker_bloc_logger.dart';

Future initApp(AppConfig appConfig) async {
  WidgetsFlutterBinding.ensureInitialized();

  await GetStorage.init();

  await dotenv.load(fileName: ".env");

  await app.configureInjection(appConfig);

  if (GetIt.I.isRegistered<Talker>()) {
    Bloc.observer = TalkerBlocObserver(talker: GetIt.instance<Talker>());
  } else {
    logDebug("Talker is not registered");
  }
}
