// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workplace_group_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkPlaceGroupResponse _$WorkPlaceGroupResponseFromJson(
        Map<String, dynamic> json) =>
    WorkPlaceGroupResponse(
      id: json['id'] as String,
      name: json['name'] as String?,
      privacy:
          $enumDecodeNullable(_$WorkPlaceGroupPrivacyEnumMap, json['privacy']),
      createdTime: _$JsonConverterFromJson<String, DateTime?>(
          json['created_time'], const DateTimeConverter().fromJson),
      updatedTime: _$JsonConverterFromJson<String, DateTime?>(
          json['updated_time'], const DateTimeConverter().fromJson),
      archived: json['archived'] as bool?,
      postRequiresAdminApproval: json['post_requires_admin_approval'] as bool?,
      cover: json['cover'] == null
          ? null
          : GroupCover.fromJson(json['cover'] as Map<String, dynamic>),
      icon: json['icon'] as String?,
      owner: json['owner'] == null
          ? null
          : WorkPlaceUser.fromJson(json['owner'] as Map<String, dynamic>),
      admins: json['admins'] == null
          ? null
          : WorkPlaceListReponse<WorkPlaceUser>.fromJson(
              json['admins'] as Map<String, dynamic>),
      members: json['members'] == null
          ? null
          : WorkPlaceListReponse<WorkPlaceUser>.fromJson(
              json['members'] as Map<String, dynamic>),
      description: json['description'] as String?,
    );

const _$WorkPlaceGroupPrivacyEnumMap = {
  WorkPlaceGroupPrivacy.open: 'OPEN',
  WorkPlaceGroupPrivacy.closed: 'CLOSED',
  WorkPlaceGroupPrivacy.secret: 'SECRET',
};

Value? _$JsonConverterFromJson<Json, Value>(
  Object? json,
  Value? Function(Json json) fromJson,
) =>
    json == null ? null : fromJson(json as Json);

GroupCover _$GroupCoverFromJson(Map<String, dynamic> json) => GroupCover(
      id: json['id'] as String?,
      source: json['source'] as String?,
      coverId: json['cover_id'] as String?,
      offsetX: (json['offset_x'] as num?)?.toInt(),
      offsetY: (json['offset_y'] as num?)?.toInt(),
    );
