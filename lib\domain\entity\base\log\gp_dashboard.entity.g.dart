// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'gp_dashboard.entity.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetGPDashboardEntityCollection on Isar {
  IsarCollection<GPDashboardEntity> get gPDashboardEntitys => this.collection();
}

const GPDashboardEntitySchema = CollectionSchema(
  name: r'GPDashboardEntity',
  id: 3143599622990731383,
  properties: {
    r'totalDownloadFile': PropertySchema(
      id: 0,
      name: r'totalDownloadFile',
      type: IsarType.long,
    ),
    r'totalDownloadSize': PropertySchema(
      id: 1,
      name: r'totalDownloadSize',
      type: IsarType.long,
    ),
    r'totalUploadFile': PropertySchema(
      id: 2,
      name: r'totalUploadFile',
      type: IsarType.long,
    ),
    r'totalUploadSize': PropertySchema(
      id: 3,
      name: r'totalUploadSize',
      type: IsarType.long,
    )
  },
  estimateSize: _gPDashboardEntityEstimateSize,
  serialize: _gPDashboardEntitySerialize,
  deserialize: _gPDashboardEntityDeserialize,
  deserializeProp: _gPDashboardEntityDeserializeProp,
  idName: r'id',
  indexes: {},
  links: {},
  embeddedSchemas: {},
  getId: _gPDashboardEntityGetId,
  getLinks: _gPDashboardEntityGetLinks,
  attach: _gPDashboardEntityAttach,
  version: '3.1.0+1',
);

int _gPDashboardEntityEstimateSize(
  GPDashboardEntity object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  return bytesCount;
}

void _gPDashboardEntitySerialize(
  GPDashboardEntity object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeLong(offsets[0], object.totalDownloadFile);
  writer.writeLong(offsets[1], object.totalDownloadSize);
  writer.writeLong(offsets[2], object.totalUploadFile);
  writer.writeLong(offsets[3], object.totalUploadSize);
}

GPDashboardEntity _gPDashboardEntityDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = GPDashboardEntity(
    totalDownloadFile: reader.readLongOrNull(offsets[0]) ?? 0,
    totalDownloadSize: reader.readLongOrNull(offsets[1]) ?? 0,
    totalUploadFile: reader.readLongOrNull(offsets[2]) ?? 0,
    totalUploadSize: reader.readLongOrNull(offsets[3]) ?? 0,
  );
  return object;
}

P _gPDashboardEntityDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readLongOrNull(offset) ?? 0) as P;
    case 1:
      return (reader.readLongOrNull(offset) ?? 0) as P;
    case 2:
      return (reader.readLongOrNull(offset) ?? 0) as P;
    case 3:
      return (reader.readLongOrNull(offset) ?? 0) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _gPDashboardEntityGetId(GPDashboardEntity object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _gPDashboardEntityGetLinks(
    GPDashboardEntity object) {
  return [];
}

void _gPDashboardEntityAttach(
    IsarCollection<dynamic> col, Id id, GPDashboardEntity object) {}

extension GPDashboardEntityQueryWhereSort
    on QueryBuilder<GPDashboardEntity, GPDashboardEntity, QWhere> {
  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension GPDashboardEntityQueryWhere
    on QueryBuilder<GPDashboardEntity, GPDashboardEntity, QWhereClause> {
  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterWhereClause>
      idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterWhereClause>
      idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterWhereClause>
      idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension GPDashboardEntityQueryFilter
    on QueryBuilder<GPDashboardEntity, GPDashboardEntity, QFilterCondition> {
  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterFilterCondition>
      idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterFilterCondition>
      idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterFilterCondition>
      totalDownloadFileEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'totalDownloadFile',
        value: value,
      ));
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterFilterCondition>
      totalDownloadFileGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'totalDownloadFile',
        value: value,
      ));
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterFilterCondition>
      totalDownloadFileLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'totalDownloadFile',
        value: value,
      ));
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterFilterCondition>
      totalDownloadFileBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'totalDownloadFile',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterFilterCondition>
      totalDownloadSizeEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'totalDownloadSize',
        value: value,
      ));
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterFilterCondition>
      totalDownloadSizeGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'totalDownloadSize',
        value: value,
      ));
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterFilterCondition>
      totalDownloadSizeLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'totalDownloadSize',
        value: value,
      ));
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterFilterCondition>
      totalDownloadSizeBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'totalDownloadSize',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterFilterCondition>
      totalUploadFileEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'totalUploadFile',
        value: value,
      ));
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterFilterCondition>
      totalUploadFileGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'totalUploadFile',
        value: value,
      ));
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterFilterCondition>
      totalUploadFileLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'totalUploadFile',
        value: value,
      ));
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterFilterCondition>
      totalUploadFileBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'totalUploadFile',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterFilterCondition>
      totalUploadSizeEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'totalUploadSize',
        value: value,
      ));
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterFilterCondition>
      totalUploadSizeGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'totalUploadSize',
        value: value,
      ));
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterFilterCondition>
      totalUploadSizeLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'totalUploadSize',
        value: value,
      ));
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterFilterCondition>
      totalUploadSizeBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'totalUploadSize',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension GPDashboardEntityQueryObject
    on QueryBuilder<GPDashboardEntity, GPDashboardEntity, QFilterCondition> {}

extension GPDashboardEntityQueryLinks
    on QueryBuilder<GPDashboardEntity, GPDashboardEntity, QFilterCondition> {}

extension GPDashboardEntityQuerySortBy
    on QueryBuilder<GPDashboardEntity, GPDashboardEntity, QSortBy> {
  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterSortBy>
      sortByTotalDownloadFile() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalDownloadFile', Sort.asc);
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterSortBy>
      sortByTotalDownloadFileDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalDownloadFile', Sort.desc);
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterSortBy>
      sortByTotalDownloadSize() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalDownloadSize', Sort.asc);
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterSortBy>
      sortByTotalDownloadSizeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalDownloadSize', Sort.desc);
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterSortBy>
      sortByTotalUploadFile() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalUploadFile', Sort.asc);
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterSortBy>
      sortByTotalUploadFileDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalUploadFile', Sort.desc);
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterSortBy>
      sortByTotalUploadSize() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalUploadSize', Sort.asc);
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterSortBy>
      sortByTotalUploadSizeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalUploadSize', Sort.desc);
    });
  }
}

extension GPDashboardEntityQuerySortThenBy
    on QueryBuilder<GPDashboardEntity, GPDashboardEntity, QSortThenBy> {
  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterSortBy>
      thenByTotalDownloadFile() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalDownloadFile', Sort.asc);
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterSortBy>
      thenByTotalDownloadFileDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalDownloadFile', Sort.desc);
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterSortBy>
      thenByTotalDownloadSize() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalDownloadSize', Sort.asc);
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterSortBy>
      thenByTotalDownloadSizeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalDownloadSize', Sort.desc);
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterSortBy>
      thenByTotalUploadFile() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalUploadFile', Sort.asc);
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterSortBy>
      thenByTotalUploadFileDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalUploadFile', Sort.desc);
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterSortBy>
      thenByTotalUploadSize() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalUploadSize', Sort.asc);
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QAfterSortBy>
      thenByTotalUploadSizeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalUploadSize', Sort.desc);
    });
  }
}

extension GPDashboardEntityQueryWhereDistinct
    on QueryBuilder<GPDashboardEntity, GPDashboardEntity, QDistinct> {
  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QDistinct>
      distinctByTotalDownloadFile() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'totalDownloadFile');
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QDistinct>
      distinctByTotalDownloadSize() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'totalDownloadSize');
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QDistinct>
      distinctByTotalUploadFile() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'totalUploadFile');
    });
  }

  QueryBuilder<GPDashboardEntity, GPDashboardEntity, QDistinct>
      distinctByTotalUploadSize() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'totalUploadSize');
    });
  }
}

extension GPDashboardEntityQueryProperty
    on QueryBuilder<GPDashboardEntity, GPDashboardEntity, QQueryProperty> {
  QueryBuilder<GPDashboardEntity, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<GPDashboardEntity, int, QQueryOperations>
      totalDownloadFileProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'totalDownloadFile');
    });
  }

  QueryBuilder<GPDashboardEntity, int, QQueryOperations>
      totalDownloadSizeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'totalDownloadSize');
    });
  }

  QueryBuilder<GPDashboardEntity, int, QQueryOperations>
      totalUploadFileProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'totalUploadFile');
    });
  }

  QueryBuilder<GPDashboardEntity, int, QQueryOperations>
      totalUploadSizeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'totalUploadSize');
    });
  }
}
