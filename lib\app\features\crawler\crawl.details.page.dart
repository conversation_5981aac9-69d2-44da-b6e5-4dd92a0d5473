import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_fbwp_crawler/app/app.dart';
import 'package:gp_fbwp_crawler/flutter_gen/assets.gen.dart';
import 'package:gp_fbwp_crawler/helpers/file_helper.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

// class MyWidget extends StatelessWidget {
//   const MyWidget({
//     super.key,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return BlocBuilder<CrawlBloc, CrawlState>(
//       builder: (context, crawlState) {
//         return SingleChildScrollView(
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: crawlState.queueBlocs.map((e) {
//               if (e is CrawlCommunityBloc) {
//                 return _CommunityInfoWidget(
//                   bloc: e,
//                 );
//               }
//               return _CrawlHeader(
//                 bloc: e,
//                 headerStr: e.displayName,
//               );
//             }).toList(),
//           ),
//         );
//       },
//     );
//   }
// }

class CrawlHeader extends StatelessWidget {
  CrawlHeader({
    super.key,
    required this.bloc,
    required this.headerStr,
  });

  final String headerStr;

  final ValueNotifier<bool> isExpanded = ValueNotifier(true);

  final CrawlQueueBloc bloc;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: SingleChildScrollView(
        child: Column(
          children: [
            ValueListenableBuilder(
                valueListenable: isExpanded,
                builder: (context, value, widget) {
                  return ExpansionTile(
                    initiallyExpanded: true,
                    title: Text(
                      headerStr,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    trailing: Icon(
                      value ? Icons.arrow_drop_down_circle : Icons.arrow_drop_down,
                    ),
                    children: [
                      BlocBuilder<CrawlQueueBloc, CrawlQueueState>(
                        bloc: bloc,
                        builder: (context, crawlState) {
                          return ListView.separated(
                            shrinkWrap: true,
                            itemBuilder: (context, index) {
                              return Padding(
                                padding: const EdgeInsets.all(10),
                                child: _QueueInfoItemWidget(
                                  filename: bloc.displayName.toLowerCase(),
                                  item: crawlState.exportData.queuesInfo[index],
                                ),
                              );
                            },
                            itemCount: crawlState.exportData.queuesInfo.length,
                            separatorBuilder: (BuildContext context, int index) {
                              return const Divider(thickness: 0.5);
                            },
                          );
                        },
                      ),
                    ],
                    onExpansionChanged: (bool expanded) {
                      isExpanded.value = !isExpanded.value;
                    },
                  );
                }),
            const SizedBox(height: 2),
          ],
        ),
      ),
    );
  }
}

class _QueueInfoItemWidget extends StatelessWidget {
  const _QueueInfoItemWidget({
    required this.filename,
    required this.item,
  });

  final String filename;

  final WorkPlaceQueueInfo item;

  Future downloadCSV(String csv) async {
    return FileHelper.saveCSV('$filename.csv', csv);
  }

  @override
  Widget build(BuildContext context) {
    if (item.taskStatus == WPTaskStatus.todo) {
      return _ItemMessageWidget(message: item.message, level: item.level);
    } else if (item.taskStatus == WPTaskStatus.inProgress) {
      final level = item.level;

      final Widget lottieWidget;
      if (level > 1) {
        lottieWidget = Padding(
          padding: EdgeInsets.only(left: (10 * item.level).toDouble()),
          child: Assets.images.horizontalLoading.lottie(),
        );
      } else {
        lottieWidget = Assets.images.horizontalLoading.lottie();
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _ItemMessageWidget(message: item.message, level: level),
          const SizedBox(height: 4),
          lottieWidget,
        ],
      );
    } else if (item.taskStatus == WPTaskStatus.done) {
      final String? csv = item.csv;
      if (csv != null && csv.isNotEmpty == true) {
        return Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _ItemMessageWidget(message: item.message, level: item.level),
                  _RunningDurationWidget(
                    duration: item.runningDuration,
                    level: item.level,
                  ),
                  TextButton(
                    child: Text(l10n.download_csv),
                    onPressed: () => downloadCSV(csv),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.check_circle_outlined,
              color: Colors.green,
              size: 20,
            ),
          ],
        );
      }
      return Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _ItemMessageWidget(message: item.message, level: item.level),
              _RunningDurationWidget(
                duration: item.runningDuration,
                level: item.level,
              )
            ],
          )),
          const Icon(
            Icons.check_circle_outlined,
            color: Colors.green,
            size: 20,
          ),
        ],
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _ItemMessageWidget(message: item.message, level: item.level),
          Text(
            item.error ?? '',
            style: const TextStyle(
              color: Colors.red,
            ),
          )
        ],
      );
    }
  }
}

class _RunningDurationWidget extends StatelessWidget {
  const _RunningDurationWidget({
    this.duration,
    required this.level,
  });

  final Duration? duration;
  final int level;

  String runningDuration(Duration? duration) {
    if (duration == null) return '';

    int days = duration.inDays;
    int hours = duration.inHours % 24;
    int minutes = duration.inMinutes % 60;
    int seconds = duration.inSeconds % 60;
    int milliseconds = duration.inMilliseconds % 1000;

    return '''${days != 0 ? '$days ngày ' : ''}'''
        '''${hours != 0 ? ' $hours giờ ' : ''}'''
        '''${minutes != 0 ? '$minutes phút ' : ''}'''
        '''${seconds != 0 ? '$seconds giây ' : ''}'''
        '''${milliseconds != 0 ? '$milliseconds ms ' : ''}''';
  }

  @override
  Widget build(BuildContext context) {
    if (runningDuration(duration) == '') return const SizedBox();

    return Padding(
      padding: EdgeInsets.only(left: (10 * level).toDouble()),
      child: Text(
        '${l10n.handle_duration}${runningDuration(duration)}',
        style: const TextStyle(color: Colors.black54),
      ),
    );
  }
}

class _ItemMessageWidget extends StatelessWidget {
  const _ItemMessageWidget({
    required this.message,
    this.level = 1,
  });

  final String message;
  final int level;

  @override
  Widget build(BuildContext context) {
    if (level > 1) {
      return Padding(
        padding: EdgeInsets.only(left: (10 * level).toDouble()),
        child: Text(
          message,
          maxLines: 3,
          style: const TextStyle(color: Color(0xFF5d5d5d)),
        ),
      );
    }

    return Text(message, maxLines: 3);
  }
}

class _CommunityInfoWidget extends StatelessWidget {
  const _CommunityInfoWidget({
    required this.bloc,
  });

  final CrawlQueueBloc bloc;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CrawlQueueBloc, CrawlQueueState>(
        bloc: bloc,
        builder: (context, crawlState) {
          final item = crawlState.exportData.queuesInfo.last;
          if (item.taskStatus == WPTaskStatus.todo) {
            return Text(item.message);
          } else if (item.taskStatus == WPTaskStatus.inProgress) {
            return Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(item.message),
                  const SizedBox(height: 6),
                  Assets.images.horizontalLoading.lottie(),
                ],
              ),
            );
          } else if (item.taskStatus == WPTaskStatus.done) {
            return Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                item.message,
                textAlign: TextAlign.left,
              ),
            );
          } else {
            return Text(
              item.message,
              style: const TextStyle(
                color: Colors.red,
              ),
            );
          }
        });
  }
}

// class CrawlDetail extends StatelessWidget {
//   const CrawlDetail({
//     super.key,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return BlocBuilder<CrawlBloc, CrawlState>(
//       builder: (context, crawlState) {
//         final user = crawlState.data.users;
//         final group = crawlState.data.groups;
//         final groupFeed = crawlState.data.groupFeeds;
//         final thread = crawlState.data.threads;
//         final message = crawlState.data.messages;
//         final userFeed = crawlState.data.userFeeds;
//         final comment = crawlState.data.comments;

//         return Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             _buildCrawlStatusWidget('User', user, context),
//             _buildCrawlStatusWidget('Group', group, context),
//             _buildCrawlStatusWidget('Group Feeds', groupFeed, context),
//             _buildCrawlStatusWidget('Thread', thread, context),
//             _buildCrawlStatusWidget('Message', message, context),
//             _buildCrawlStatusWidget('User Feed', userFeed, context),
//             _buildCrawlStatusWidget('Comment', comment, context),
//           ],
//         );
//       },
//     );
//   }

//   Widget _buildCrawlStatusWidget(
//       String label, WorkPlaceItemExport? item, BuildContext context) {
//     if (item?.status == null) return const SizedBox();
//     if (item?.status?.isDownloading == true) {
//       return Text('$label: Crawling');
//     } else if (item?.status?.isDownloaded == true) {
//       return Row(
//         children: [
//           Text('$label: Downloaded'),
//           TextButton(
//               onPressed: () {
//                 context.read<CrawlBloc>().add(CrawlExportEvent(
//                     filename: '$label.csv', csv: item?.csv ?? ""));
//               },
//               child: const Text('save csv')),
//         ],
//       );
//     } else {
//       return Text('$label: Error');
//     }
//   }
// }