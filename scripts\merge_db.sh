#!/bin/bash

DATA_DIR="data/"
DESTINATION_DIR="$HOME/Library/Containers/vn.gapowork.crawler/Data/Documents/multi-client"

if [[ "$OSTYPE" == "msys" ]]; then
    DESTINATION_DIR="$HOME/Documents/multi-client"
fi

copy_directory() {
    local source_dir=$1
    local destination_dir=$2

    if [ -d "$source_dir" ]; then
        cp -r -f "$source_dir" "$destination_dir"
        echo "Directory '$source_dir' copied to '$destination_dir'."
    else
        echo "Source directory '$source_dir' does not exist."
    fi
}

rename_file() {
    local old_dir=$1
    local new_dir=$2

    if [[ -f "$old_dir" || -d "$old_dir" ]]; then
        mv "$old_dir" "$new_dir"
        echo "renamed from '$old_dir' to '$new_dir'."
    else
        echo "'$old_dir' does not exist."
    fi
}

copy_directory "$DATA_DIR" "$DESTINATION_DIR"
echo "Copied directory '$DATA_DIR' to '$DESTINATION_DIR'"

rename_file "$DESTINATION_DIR/base" "$DESTINATION_DIR/merged"

rename_file "$DESTINATION_DIR/merged/default.isar" "$DESTINATION_DIR/merged/merge-db.isar"
rename_file "$DESTINATION_DIR/merged/default.isar.lock" "$DESTINATION_DIR/merged/merge-db.isar.lock"

read_ids_from_file() {
    local file=$1
    local ids=()

    if [ ! -f "$file" ]; then
        echo "File '$file' does not exist."
        return 1
    fi

    # Read the file content, remove any newlines, and split by comma
    IFS=',' read -r -a ids <<< "$(cat "$file" | tr -d '\n')"

    # Return the array
    echo "${ids[@]}"
}

# Example usage: pass the file containing the IDs
file_path="data/computer_ids.txt"
ids_array=($(read_ids_from_file "$file_path"))

# Loop through the array
for id in "${ids_array[@]}"; do
    echo "Processing ID: $id"
    rename_file "$DESTINATION_DIR/$id/default.isar" "$DESTINATION_DIR/$id/$id.isar"
    rename_file "$DESTINATION_DIR/$id/default.isar.lock" "$DESTINATION_DIR/$id/$id.isar.lock"
done
