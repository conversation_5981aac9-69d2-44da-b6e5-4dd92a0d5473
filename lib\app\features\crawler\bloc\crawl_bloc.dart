import 'dart:async';

import 'package:async_queue/async_queue.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/app/constant/constant.dart';
import 'package:gp_fbwp_crawler/app/features/crawler/crawler.dart';

import '../../../../domain/entity/entity.dart';
import '../../../../domain/usecase/gapo/gapo_login.usecase.dart';

typedef JobFn = Future Function(dynamic);

final class CrawlBloc extends CoreV2BaseBloc<CoreV2BaseEvent, CrawlState> {
  CrawlBloc() : super(CrawlState(queueBlocs: [])) {
    on<CrawlInitialEvent>(_onCrawlInitialEvent);
  }

  final AsyncQueue queue = AsyncQueue();

  FutureOr _onCrawlInitialEvent(
    CrawlInitialEvent event,
    Emitter<CrawlState> emit,
  ) async {
    emit(
      CrawlState(queueBlocs: event.queueBlocs),
    );

    for (var bloc in event.queueBlocs) {
      for (var job in bloc.jobs) {
        queue.addJob(
          (previousResult) async {
            try {
              return await job.call(previousResult);
            } catch (e) {
              queue.retry();
            }
          },
          description: 'Job from ${bloc.displayName}',
          retryTime: 1,
        );

        // chạy lần lượt từng job
        await queue.start();
      }
    }
  }
}

abstract class CrawlQueueBloc extends Cubit<CrawlQueueState> {
  CrawlQueueBloc({required this.commonBloc})
      : super(
          const CrawlQueueState(
            exportData: WorkPlaceExport(queuesInfo: []),
          ),
        );

  final CommonBloc commonBloc;

  String? csv;
  final List<WorkPlaceQueueInfo> queuesInfo = [];

  List<JobFn> get jobs;
  String get displayName;

  void emitInfo(WorkPlaceQueueInfo info) {
    var oldQueueInfo = queuesInfo.firstWhereOrNull(
      (element) => element.message == info.message,
    );

    if (oldQueueInfo == null) {
      queuesInfo.add(info);
    } else {
      final index = queuesInfo.indexOf(oldQueueInfo);
      if (index != -1) {
        final now = DateTime.now();
        final runningDuration = now.difference(oldQueueInfo.time);

        queuesInfo[index] = oldQueueInfo.copyWith(
          csv: info.csv,
          error: info.error,
          taskStatus: info.taskStatus,
          level: info.level,
          time: DateTime.now(),
          runningDuration: runningDuration,
        );
      }
    }

    emit(CrawlQueueState(
      exportData: WorkPlaceExport(queuesInfo: queuesInfo),
    ));
  }
}

final class CrawlCommunityBloc extends CrawlQueueBloc {
  CrawlCommunityBloc({
    required super.commonBloc,
  });

  late final CommunityQueue queue = CommunityQueue(
    crawlQueueBloc: this,
    commonBloc: commonBloc,
  );

  @override
  List<JobFn> get jobs => [
        (previousResult) async {
          return await queue.getWorkPlaceCommunity();
        },
      ];

  @override
  String get displayName => "Community";
}

final class CrawlMemberBloc extends CrawlQueueBloc {
  CrawlMemberBloc({
    required super.commonBloc,
  });

  late final MemberQueue queue = MemberQueue(
    crawlQueueBloc: this,
    commonBloc: commonBloc,
  );

  late final GPMemberQueue gpQueue = GPMemberQueue(
    crawlQueueBloc: this,
    commonBloc: commonBloc,
  );

  @override
  List<JobFn> get jobs => [
        (previousResult) async {
          return await queue.initialGPAdminAccount();
        },
        (previousResult) async {
          if (previousResult is InitGPAdminOutput) {
            return await queue.getWorkPlaceMembers();
          }
        },
        // gapowork
        (previousResult) async {
          if (previousResult is List<WorkPlaceCommunityMemberEntity>) {
            if (AppConstants.needRunOnGapoWork) {
              await gpQueue
                  .signupUsers(previousResult)
                  .then((memberEntities) async {
                await gpQueue.inviteToWorkspace(memberEntities);
              });
            }
          }

          return previousResult;
        },
      ];

  @override
  String get displayName => "Member";
}

final class CrawlGroupBloc extends CrawlQueueBloc {
  CrawlGroupBloc({
    required super.commonBloc,
  });

  late final GroupQueue queue = GroupQueue(
    crawlQueueBloc: this,
    commonBloc: commonBloc,
  );

  late final GPGroupQueue gpQueue = GPGroupQueue(
    crawlQueueBloc: this,
    commonBloc: commonBloc,
  );

  @override
  List<JobFn> get jobs => [
        (previousResult) async {
          return await queue.getWorkPlaceGroups();
        },
        (previousResult) async {
          if (previousResult is List<WorkPlaceGroupEntity>) {
            return await queue.getWorkPlaceMembers(previousResult);
          }
        },
        (previousResult) async {
          if (previousResult is List<WorkPlaceGroupEntity>) {
            return await queue.downloadWorkPlaceCover(previousResult);
          }
        },
        // gapowork
        (previousResult) async {
          if (previousResult is List<WorkPlaceGroupEntity>) {
            if (AppConstants.needRunOnGapoWork) {
              // unwaited
              gpQueue
                  .uploadCoverToGapoWork(previousResult)
                  .then((groupEntitys) {
                gpQueue.createGapoWorkGroups(groupEntitys);
              });
            }
          }

          return previousResult;
        },
      ];

  @override
  String get displayName => "Group";
}

final class CrawlFeedBloc extends CrawlQueueBloc {
  CrawlFeedBloc({
    required super.commonBloc,
  });

  late final FeedQueue queue = FeedQueue(
    crawlQueueBloc: this,
    commonBloc: commonBloc,
  );

  late final GPFeedQueue gpQueue = GPFeedQueue(
    crawlQueueBloc: this,
    commonBloc: commonBloc,
  );

  @override
  List<JobFn> get jobs => [
        (previousResult) async {
          return await queue.getWorkPlaceFeeds();
        },
        (previousResult) async {
          if (previousResult is List<WorkPlaceFeedEntity>) {
            return await queue.getFeedComments(previousResult);
          }
        },
        (previousResult) async {
          if (previousResult is List<WorkPlaceFeedEntity>) {
            return await queue.getFeedReactions(previousResult);
          }
        },
        // // upload missing attachments: run độc lập
        // (previousResult) async {
        //   return await gpQueue.uploadMissingAttachments();
        // },
        (previousResult) async {
          if (previousResult is List<WorkPlaceFeedEntity>) {
            // return await queue.getSeens(previousResult);
          }
        },
        (previousResult) async {
          if (previousResult is List<WorkPlaceFeedEntity>) {
            // save lần cuối
            return await queue.saveAllFeeds(previousResult);
          }
        },
      ];

  @override
  String get displayName => "Feed";
}

final class CrawlThreadBloc extends CrawlQueueBloc {
  CrawlThreadBloc({
    required super.commonBloc,
  });

  late final ThreadQueue queue = ThreadQueue(
    crawlQueueBloc: this,
    commonBloc: commonBloc,
  );

  late final GPThreadQueue gpQueue = GPThreadQueue(
    crawlQueueBloc: this,
    commonBloc: commonBloc,
  );

  @override
  List<JobFn> get jobs => [
        if (AppConstants.isMainComputer)
          (previousResult) async {
            return await queue.getWorkPlaceUserConversations();
          },
        if (AppConstants.isMainComputer == false)
          (previousResult) async {
            if (previousResult is List<WorkPlaceConversationEntity>) {
              return await queue
                  .getWorkPlaceConversationMessages(previousResult);
            }
          },
        // // upload missing attachments: run độc lập
        // (previousResult) async {
        //   final threads =
        //       await gpQueue.localService.missingConversationAttachments();
        //   return await queue.getWorkPlaceConversationMessages(threads.toList());
        // },
        // // upload missing attachments: run độc lập
        // (previousResult) async {
        //   final messages =
        //       await gpQueue.localService.missingMessageAttachments();
        //   return await queue.downloadMessageAttachments(
        //       messages: messages.toList().reversed.toList());
        // },
        (previousResult) async {
          return await queue.done();
        },
      ];

  @override
  String get displayName => "Thread";
}
