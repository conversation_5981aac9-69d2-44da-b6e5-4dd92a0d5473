import 'dart:async';

import 'package:get_it/get_it.dart';
import 'package:gp_fbwp_crawler/app/features/crawler/mixin/mixin.dart';
import 'package:gp_fbwp_crawler/app/features/crawler/queues/base_queue.dart';
import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:gp_fbwp_crawler/mapper/gp_mapper.dart';
import 'package:isar/isar.dart';

class BaseThreadQueue extends GPBaseQueue
    with
        GPMapperMixin,
        AttachmentHandlerMixin,
        CheckpointMixin,
        _VariablesMixin,
        _DbHandlerMixin,
        RunMultiClients {
  BaseThreadQueue({required super.crawlQueueBloc, required super.commonBloc});
}

mixin _VariablesMixin {
  late final Isar isar = GetIt.I<Isar>(instanceName: 'kIsar');

  final wpUserConversationsUseCase =
      GetIt.I<WorkPlaceUserConversationsUseCase>();

  final wpMessageUseCase = GetIt.I<WorkPlaceUserMessagesUseCase>();
}

mixin _DbHandlerMixin {
  final Isar isar = GetIt.I<Isar>(instanceName: 'kIsar');
  final localService =
      GetIt.I<WorkPlaceLocalService>(instanceName: 'kWorkPlaceLocalService');
  Future<List<WorkPlaceCommunityMemberEntity>> getAllMembers() async {
    return localService.getAllMembers();
  }

  Future<List<WorkPlaceConversationEntity>> getAllThreads() {
    return localService.getAllThreads();
  }

  Future<List<WorkPlaceMessagesEntity>> getAllMessages() {
    return localService.getAllMessages();
  }

  Future saveThread(List<WorkPlaceConversationEntity> threads) {
    return localService.saveThreads(threads);
  }

  Future saveMessages(List<WorkPlaceMessagesEntity> messages) {
    return localService.saveMessages(messages);
  }

  Future saveMessageAttachments(
      List<WorkPlaceConversationAttachmentsEntity> attachments) {
    return localService.saveMessageAttachments(attachments);
  }

  Future saveSticker(WorkPlaceStickerEntity sticker) {
    return localService.saveSticker(sticker);
  }

  Future updateThread(WorkPlaceConversationEntity conversationEntity) async {
    await localService.updateThread(conversationEntity);
    await localService.reloadThread(conversationEntity);
  }

  Future reloadThread(WorkPlaceConversationEntity conversationEntity) async {
    await localService.reloadThread(conversationEntity);
  }

  Future updateMessage(WorkPlaceMessagesEntity messagesEntity) async {
    await localService.updateMessage(messagesEntity);
    await localService.reloadMessage(messagesEntity);
  }

  Future reloadMessage(WorkPlaceMessagesEntity messagesEntity) async {
    await localService.reloadMessage(messagesEntity);
  }

  Future<WorkPlaceCommunityMemberEntity?> getWpUser(int? gpUserId) async {
    return localService.getWpUser(gpUserId);
  }

  Future<WorkPlaceCommunityMemberEntity?> getUserById(String? id) async {
    return localService.getUserById(id);
  }

  Future saveLog(String message, GPLogType type) async {
    return localService.saveLog(message, type);
  }
}
