// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'gp_dashboard_upload.entity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$GPUploadDashboardEntity {
  /// progress tổng
  GPUploadProgressEntity? get progressEntity =>
      throw _privateConstructorUsedError;

  /// status tổng
  GPUploadStatus? get status => throw _privateConstructorUsedError;

  /// tổng số files đã upload
  int? get totalUploadedFiles => throw _privateConstructorUsedError;

  /// tổng số files upload theo tất cả các status
  int? get totalUploadFiles => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $GPUploadDashboardEntityCopyWith<GPUploadDashboardEntity> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GPUploadDashboardEntityCopyWith<$Res> {
  factory $GPUploadDashboardEntityCopyWith(GPUploadDashboardEntity value,
          $Res Function(GPUploadDashboardEntity) then) =
      _$GPUploadDashboardEntityCopyWithImpl<$Res, GPUploadDashboardEntity>;
  @useResult
  $Res call(
      {GPUploadProgressEntity? progressEntity,
      GPUploadStatus? status,
      int? totalUploadedFiles,
      int? totalUploadFiles});

  $GPUploadProgressEntityCopyWith<$Res>? get progressEntity;
}

/// @nodoc
class _$GPUploadDashboardEntityCopyWithImpl<$Res,
        $Val extends GPUploadDashboardEntity>
    implements $GPUploadDashboardEntityCopyWith<$Res> {
  _$GPUploadDashboardEntityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? progressEntity = freezed,
    Object? status = freezed,
    Object? totalUploadedFiles = freezed,
    Object? totalUploadFiles = freezed,
  }) {
    return _then(_value.copyWith(
      progressEntity: freezed == progressEntity
          ? _value.progressEntity
          : progressEntity // ignore: cast_nullable_to_non_nullable
              as GPUploadProgressEntity?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as GPUploadStatus?,
      totalUploadedFiles: freezed == totalUploadedFiles
          ? _value.totalUploadedFiles
          : totalUploadedFiles // ignore: cast_nullable_to_non_nullable
              as int?,
      totalUploadFiles: freezed == totalUploadFiles
          ? _value.totalUploadFiles
          : totalUploadFiles // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $GPUploadProgressEntityCopyWith<$Res>? get progressEntity {
    if (_value.progressEntity == null) {
      return null;
    }

    return $GPUploadProgressEntityCopyWith<$Res>(_value.progressEntity!,
        (value) {
      return _then(_value.copyWith(progressEntity: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$GPUploadDashboardEntityImplCopyWith<$Res>
    implements $GPUploadDashboardEntityCopyWith<$Res> {
  factory _$$GPUploadDashboardEntityImplCopyWith(
          _$GPUploadDashboardEntityImpl value,
          $Res Function(_$GPUploadDashboardEntityImpl) then) =
      __$$GPUploadDashboardEntityImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {GPUploadProgressEntity? progressEntity,
      GPUploadStatus? status,
      int? totalUploadedFiles,
      int? totalUploadFiles});

  @override
  $GPUploadProgressEntityCopyWith<$Res>? get progressEntity;
}

/// @nodoc
class __$$GPUploadDashboardEntityImplCopyWithImpl<$Res>
    extends _$GPUploadDashboardEntityCopyWithImpl<$Res,
        _$GPUploadDashboardEntityImpl>
    implements _$$GPUploadDashboardEntityImplCopyWith<$Res> {
  __$$GPUploadDashboardEntityImplCopyWithImpl(
      _$GPUploadDashboardEntityImpl _value,
      $Res Function(_$GPUploadDashboardEntityImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? progressEntity = freezed,
    Object? status = freezed,
    Object? totalUploadedFiles = freezed,
    Object? totalUploadFiles = freezed,
  }) {
    return _then(_$GPUploadDashboardEntityImpl(
      progressEntity: freezed == progressEntity
          ? _value.progressEntity
          : progressEntity // ignore: cast_nullable_to_non_nullable
              as GPUploadProgressEntity?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as GPUploadStatus?,
      totalUploadedFiles: freezed == totalUploadedFiles
          ? _value.totalUploadedFiles
          : totalUploadedFiles // ignore: cast_nullable_to_non_nullable
              as int?,
      totalUploadFiles: freezed == totalUploadFiles
          ? _value.totalUploadFiles
          : totalUploadFiles // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

class _$GPUploadDashboardEntityImpl implements _GPUploadDashboardEntity {
  _$GPUploadDashboardEntityImpl(
      {this.progressEntity,
      this.status,
      this.totalUploadedFiles,
      this.totalUploadFiles});

  /// progress tổng
  @override
  final GPUploadProgressEntity? progressEntity;

  /// status tổng
  @override
  final GPUploadStatus? status;

  /// tổng số files đã upload
  @override
  final int? totalUploadedFiles;

  /// tổng số files upload theo tất cả các status
  @override
  final int? totalUploadFiles;

  @override
  String toString() {
    return 'GPUploadDashboardEntity(progressEntity: $progressEntity, status: $status, totalUploadedFiles: $totalUploadedFiles, totalUploadFiles: $totalUploadFiles)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GPUploadDashboardEntityImpl &&
            (identical(other.progressEntity, progressEntity) ||
                other.progressEntity == progressEntity) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.totalUploadedFiles, totalUploadedFiles) ||
                other.totalUploadedFiles == totalUploadedFiles) &&
            (identical(other.totalUploadFiles, totalUploadFiles) ||
                other.totalUploadFiles == totalUploadFiles));
  }

  @override
  int get hashCode => Object.hash(runtimeType, progressEntity, status,
      totalUploadedFiles, totalUploadFiles);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GPUploadDashboardEntityImplCopyWith<_$GPUploadDashboardEntityImpl>
      get copyWith => __$$GPUploadDashboardEntityImplCopyWithImpl<
          _$GPUploadDashboardEntityImpl>(this, _$identity);
}

abstract class _GPUploadDashboardEntity implements GPUploadDashboardEntity {
  factory _GPUploadDashboardEntity(
      {final GPUploadProgressEntity? progressEntity,
      final GPUploadStatus? status,
      final int? totalUploadedFiles,
      final int? totalUploadFiles}) = _$GPUploadDashboardEntityImpl;

  @override

  /// progress tổng
  GPUploadProgressEntity? get progressEntity;
  @override

  /// status tổng
  GPUploadStatus? get status;
  @override

  /// tổng số files đã upload
  int? get totalUploadedFiles;
  @override

  /// tổng số files upload theo tất cả các status
  int? get totalUploadFiles;
  @override
  @JsonKey(ignore: true)
  _$$GPUploadDashboardEntityImplCopyWith<_$GPUploadDashboardEntityImpl>
      get copyWith => throw _privateConstructorUsedError;
}
