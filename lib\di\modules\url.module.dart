/*
 * Created Date: 2/01/2024 11:21:11
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 11th June 2024 16:00:49
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_fbwp_crawler/app/constant/fb_wp_url.constants.dart';
import 'package:gp_fbwp_crawler/app/constant/gapo_url.constants.dart';
import 'package:injectable/injectable.dart';

const _saasDevelop = 'SaasDevelop';
const _saasQa = 'SaasQa';
const _saasUat = 'SaasUat';
const _saasProd = 'SaasProd';

const List<String> kFlavorSaasDevs = [_saasDevelop, _saasQa];
const List<String> kFlavorSaasProds = [_saasProd];
const List<String> kFlavorSaasUat = [_saasUat];

@module
abstract class UrlModule {
  // --------- domain ---------- \\
  @Singleton(env: kFlavorSaasProds)
  @Named('kGapoWorkDomain')
  String get kGapoWorkDomainProd => GPConstants.kGapoWorkDomainProd;

  @Singleton(env: kFlavorSaasUat)
  @Named('kGapoWorkDomain')
  String get kGapoWorkDomainUat => GPConstants.kGapoWorkDomainUat;

  @Singleton(env: kFlavorSaasDevs)
  @Named('kGapoWorkDomain')
  String get kGapoWorkDomainStaging => GPConstants.kGapoWorkDomainStag;

  @Singleton(env: kFlavorSaasProds)
  @Named('kGapoWorkUploadDomain')
  String get kGapoWorkUploadDomainProd => GPConstants.kGapoWorkUploadDomainProd;

  @Singleton(env: kFlavorSaasUat)
  @Named('kGapoWorkUploadDomain')
  String get kGapoWorkUploadDomainUat => GPConstants.kGapoWorkUploadDomainUat;

  // --------- upload domain ---------- \\
  @Singleton(env: kFlavorSaasDevs)
  @Named('kGapoWorkUploadDomain')
  String get kGapoWorkUploadDomainStaging =>
      GPConstants.kGapoWorkUploadDomainStag;

  @singleton
  @Named('kWorkPlaceUrl')
  String get kWorkPlaceUrl => FaceBookWorkPlaceConstants.kWorkPlaceDomain;

  // --------- facebook ---------- \\
  @singleton
  @Named('kFaceBookUrl')
  String get kFaceBookUrl => FaceBookWorkPlaceConstants.kFaceBookDomain;
}
