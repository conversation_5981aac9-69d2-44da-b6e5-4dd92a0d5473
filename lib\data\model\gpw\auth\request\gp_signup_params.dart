// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'gp_signup_params.freezed.dart';
part 'gp_signup_params.g.dart';

@Freezed(
  fromJson: false,
  copyWith: true,
  toJson: true,
)
class GPSignupParams with _$GPSignupParams {
  const factory GPSignupParams({
    required String email,
    @Default('777777') String otp,
    @Json<PERSON>ey(name: 'client_id')
    @Default('cuxlp0ugglm3krp1ab81')
    String clientId,
    @JsonKey(name: 'trusted_device') @Default(false) bool trustedDevice,
    @<PERSON>son<PERSON>ey(name: 'device_id')
    @Default('e108c54e-a6d5-4ad5-80e8-7c556f49991e')
    String deviceId,
  }) = _GPSignupParams;
}
