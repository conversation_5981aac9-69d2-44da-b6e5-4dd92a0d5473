import 'package:gp_fbwp_crawler/domain/entity/entity.dart';
import 'package:isar/isar.dart';

mixin MergeDataBase {
  Future<void> mergeAttachment(Isar db, Isar mergeDb) async {
    final collection = [
      db.workPlaceStickerEntitys,
      db.workPlaceConversationAttachmentsEntitys,
      db.workPlaceAttachmentEntitys,
    ];
    final mergeCollection = [
      mergeDb.workPlaceStickerEntitys,
      mergeDb.workPlaceConversationAttachmentsEntitys,
      mergeDb.workPlaceAttachmentEntitys,
    ];

    for (int j = 0; j < collection.length; j++) {
      final data = await collection[j].where().findAll();
      await mergeDb.writeTxn(() async {
        for (var ele in data) {
          await mergeCollection[j].put(ele);
        }
      });
    }
  }

  Future<void> mergeConversation(Isar db, Isar mergeDb) async {
    final conversations =
        await db.workPlaceConversationEntitys.where().findAll();
    for (var conversation in conversations) {
      final participants = conversation.participants
          .map((element) => mergeDb.workPlaceCommunityMemberEntitys
              .filter()
              .idEqualTo(element.id)
              .findFirstSync())
          .toList();
      participants.removeWhere((element) => element == null);
      final messages = conversation.messages
          .map((element) => mergeDb.workPlaceMessagesEntitys
              .filter()
              .idEqualTo(element.id)
              .findFirstSync())
          .toList();
      messages.removeWhere((element) => element == null);
      final newConversation = conversation.copyWith(
          participants: List<WorkPlaceCommunityMemberEntity>.from(participants),
          messages: List<WorkPlaceMessagesEntity>.from(messages));
      await mergeDb.writeTxn(() async {
        await mergeDb.workPlaceConversationEntitys.put(newConversation);
        await newConversation.participants.save();
        await newConversation.messages.save();
        await newConversation.participants.load();
        await newConversation.messages.load();
      });
    }
  }

  Future<void> mergeMessage(Isar db, Isar mergeDb) async {
    final messages = await db.workPlaceMessagesEntitys.where().findAll();
    for (var message in messages) {
      final from = mergeDb.workPlaceCommunityMemberEntitys
          .filter()
          .idEqualTo(message.from.value?.id ?? '')
          .findFirstSync();
      final to = message.to
          .map((e) => mergeDb.workPlaceCommunityMemberEntitys
              .filter()
              .idEqualTo(e.id)
              .findFirstSync())
          .toList();
      to.removeWhere((element) => element == null);
      final attachments = message.attachments
          .map((e) => mergeDb.workPlaceConversationAttachmentsEntitys
              .filter()
              .idEqualTo(e.id)
              .findFirstSync())
          .toList();
      attachments.removeWhere((element) => element == null);
      final sticker = mergeDb.workPlaceStickerEntitys
          .filter()
          .idEqualTo(message.sticker.value?.id ?? '')
          .findFirstSync();
      final newMessage = message.copyMergeDb(
          from: from,
          attachments:
              List<WorkPlaceConversationAttachmentsEntity>.from(attachments),
          sticker: sticker,
          to: List<WorkPlaceCommunityMemberEntity>.from(to));
      await mergeDb.writeTxn(() async {
        await mergeDb.workPlaceMessagesEntitys.put(newMessage);
        await newMessage.from.save();
        await newMessage.to.save();
        await newMessage.attachments.save();
        await newMessage.sticker.save();
        await newMessage.from.load();
        await newMessage.to.load();
        await newMessage.attachments.load();
        await newMessage.sticker.load();
      });
    }
  }

  Future<void> mergeFeed(Isar db, Isar mergeDb) async {
    final feeds = await db.workPlaceFeedEntitys.where().findAll();
    for (var feed in feeds) {
      final from = mergeDb.workPlaceCommunityMemberEntitys
          .filter()
          .idEqualTo(feed.from.value?.id ?? '')
          .findFirstSync();
      final to = feed.to
          .map((e) => mergeDb.workPlaceCommunityMemberEntitys
              .filter()
              .idEqualTo(e.id)
              .findFirstSync())
          .toList();
      to.removeWhere((element) => element == null);
      final comments = feed.comments
          .map((e) => mergeDb.workPlaceCommentEntitys
              .filter()
              .idEqualTo(e.id)
              .findFirstSync())
          .toList();

      comments.removeWhere((element) => element == null);
      final attachments = feed.attachments
          .map((e) => mergeDb.workPlaceAttachmentEntitys
              .filter()
              .idEqualTo(e.id)
              .findFirstSync())
          .toList();
      attachments.removeWhere((element) => element == null);
      final newFeed = feed.copyWith(
          newFrom: from,
          newTo: List<WorkPlaceCommunityMemberEntity>.from(to),
          newAttachments: List<WorkPlaceAttachmentEntity>.from(attachments),
          newComments: List<WorkPlaceCommentEntity>.from(comments));
      await mergeDb.writeTxn(() async {
        await mergeDb.workPlaceFeedEntitys.put(newFeed);
        await newFeed.from.save();
        await newFeed.to.save();
        await newFeed.attachments.save();
        await newFeed.comments.save();
        await newFeed.from.load();
        await newFeed.to.load();
        await newFeed.attachments.load();
        await newFeed.comments.load();
      });
    }
  }

  Future<void> mergeComment(Isar db, Isar mergeDb) async {
    final comments = await db.workPlaceCommentEntitys.where().findAll();
    for (var comment in comments) {
      // add reply first
      final replies = comment.replies
          .map((e) => db.workPlaceCommentEntitys
              .filter()
              .idEqualTo(e.id)
              .findFirstSync())
          .toList();
      replies.removeWhere((element) => element == null);
      final List<WorkPlaceCommentEntity> newReplies = [];
      for (var reply in replies) {
        final replyFrom = mergeDb.workPlaceCommunityMemberEntitys
            .filter()
            .idEqualTo(reply?.from.value?.id ?? '')
            .findFirstSync();
        final replyAttachment = mergeDb.workPlaceAttachmentEntitys
            .filter()
            .idEqualTo(reply?.attachment.value?.id ?? '')
            .findFirstSync();
        if (reply != null) {
          newReplies.add(
              reply.copyWith(from: replyFrom, attachment: replyAttachment));
        }
      }
      await mergeDb.writeTxn(() async {
        await mergeDb.workPlaceCommentEntitys.putAll(newReplies);
        for (var reply in newReplies) {
          await reply.from.save();
          await reply.attachment.save();
          await reply.from.load();
          await reply.attachment.load();
        }
      });

      final from = mergeDb.workPlaceCommunityMemberEntitys
          .filter()
          .idEqualTo(comment.from.value?.id ?? '')
          .findFirstSync();
      final attachment = mergeDb.workPlaceAttachmentEntitys
          .filter()
          .idEqualTo(comment.attachment.value?.id ?? '')
          .findFirstSync();
      final newComment = comment.copyWith(
          from: from,
          attachment: attachment,
          reply: List<WorkPlaceCommentEntity>.from(replies));
      await mergeDb.writeTxn(() async {
        await mergeDb.workPlaceCommentEntitys.put(newComment);
        await newComment.attachment.save();
        await newComment.from.save();
        await newComment.replies.save();
        await newComment.attachment.load();
        await newComment.from.load();
        await newComment.replies.load();
      });
    }
  }
}
