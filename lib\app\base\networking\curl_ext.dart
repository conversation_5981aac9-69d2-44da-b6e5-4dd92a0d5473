import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:gp_core/utils/log.dart';

extension CUrlStrExt on RequestOptions {
  String cURLRepresentation() {
    List<String> components = ["\$ curl -i"];
    components.add("-X $method");

    headers.forEach((k, v) {
      if (k != "Cookie") {
        components.add("-H \"$k: $v\"");
      }
    });

    if (method.toUpperCase() != "GET") {
      try {
        var data = json.encode(this.data);
        data = data.replaceAll('"', '\\"');
        components.add("-d \"$data\"");
      } catch (e, s) {
        logDebug("error: $e, stackTrace: $s");
      }
    }

    components.add("\"${uri.toString()}\"");

    return components.join('\\\n\t');
  }

    String cURLRepresentationRaw() {
    List<String> components = ["curl -i"];
    components.add("-X $method");

    headers.forEach((k, v) {
      if (k != "Cookie") {
        components.add("-H '$k: $v'");
      }
    });

    if (method.toUpperCase() != "GET") {
      try {
        var data = json.encode(this.data);
        // data = data.replaceAll('"', '\\"');
        components.add("-d '$data'");
      } catch (e, s) {
        logDebug("error: $e, stackTrace: $s");
      }
    }

    components.add("'${uri.toString()}'");

    return components.join(' ');
  }
}
