/*
 * Created Date: Tuesday, 4th June 2024, 14:29:39
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 4th June 2024 15:42:36
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:dio/dio.dart' hide Headers;
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/app/app.dart';
import 'package:gp_fbwp_crawler/data/model/facebook/community_response.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:injectable/injectable.dart';
import 'package:retrofit/retrofit.dart';

part 'facebook.service.g.dart';

@LazySingleton(order: DiConstants.kDataServiceOrder)
@Named('kFaceBookService')
@RestApi()
abstract class FaceBookService {
  @FactoryMethod()
  factory FaceBookService(
    @Named('kWorkPlaceDio') Dio dio, {
    @Named('kFaceBookUrl') String? baseUrl,
  }) = _FaceBookService;

  @GET(FaceBookWorkPlaceConstants.kFaceBookCommunity)
  Future<FaceBookCommunityResponse> community();
}
