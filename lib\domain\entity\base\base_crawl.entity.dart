/*
 * Created Date: Saturday, 1st June 2024, 10:52:04
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Wednesday, 28th August 2024 17:43:34
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:isar/isar.dart';

class BaseCrawlEntity {
  BaseCrawlEntity({
    required this.id,
    required this.crawlType,
    // BaseCrawlStatus? crawlStatus = const BaseCrawlDownloadStatus(),
  }) {
    // if (_status?.isDownloadStatus == true) {
    //   if ((_status as BaseCrawlDownloadStatus).status ==
    //       BaseCrawlDownloadStatusEnum.none) {
    //     return;
    //   }
    // }
    // _status = crawlStatus;
  }

  String id;

  @enumerated
  final GPBaseCrawlType crawlType;

  // BaseCrawlStatus? get _status => _crawlStatus;

  // set _status(BaseCrawlStatus? status) {
  //   if (status == null) return;

  //   _crawlStatus = status;

  //   // _saveLog();
  // }

  // BaseCrawlStatus? _crawlStatus;
}

// extension _BaseCrawlEntityLogExt on BaseCrawlEntity {
//   /// save log to database every status changed
//   void _saveLog() {
//     try {
//       final isar = GetIt.I<Isar>(instanceName: 'kIsar');

//       final oldEntities =
//           isar.gPBaseCrawlLogEntitys.filter().objectIdEqualTo(id).findAllSync();
//       if (oldEntities.isNotEmpty) {
//         for (var element in oldEntities) {
//           if (_status?.isDownloadStatus == true) {
//             if (element.crawlDownloadStatus?.status ==
//                 (_status as BaseCrawlDownloadStatus).status) {
//               return;
//             }
//           } else {
//             if (element.crawlSyncStatus?.status ==
//                 (_status as BaseCrawlSyncStatus).status) {
//               return;
//             }
//           }
//         }
//       }

//       GPBaseCrawlLogEntity? entity;

//       if (_status?.isDownloadStatus == true) {
//         entity = GPBaseCrawlLogEntity(
//           crawlType: crawlType,
//           objectId: id,
//           lastUpdatedAt: DateTime.now(),
//           crawlDownloadStatus: _status as BaseCrawlDownloadStatus,
//         );
//       } else if (_status?.isSyncStatus == true) {
//         entity = GPBaseCrawlLogEntity(
//           crawlType: crawlType,
//           objectId: id,
//           lastUpdatedAt: DateTime.now(),
//           crawlSyncStatus: _status as BaseCrawlSyncStatus,
//         );
//       }

//       if (entity != null) {
//         isar.writeTxnSync(() {
//           isar.gPBaseCrawlLogEntitys.putSync(entity!);
//         });
//       }
//     } catch (ex) {
//       log('Debug: saveLog error: $ex');
//     }
//   }
// }

extension BaseCrawEntityExt on BaseCrawlEntity {
  // BaseCrawlDownloadStatus? get downloadStatus =>
  //     _status?.isDownloadStatus == true
  //         ? _status as BaseCrawlDownloadStatus
  //         : null;
  // BaseCrawlSyncStatus? get syncStatus =>
  //     _status?.isSyncStatus == true ? _status as BaseCrawlSyncStatus : null;

  void changeToDownloadStatus({
    required BaseCrawlDownloadStatusEnum status,
    String? code,
    String? message,
  }) {
    // if (downloadStatus == null) {
    //   _status = BaseCrawlDownloadStatus(
    //     code: code,
    //     message: message,
    //     status: status,
    //   );
    // } else {
    //   _status = downloadStatus?.copyWith(
    //     status: status,
    //     code: code,
    //     message: message,
    //   );
    // }

    return;
  }

  void changeToSyncStatus({
    required BaseCrawlSyncStatusEnum status,
    String? code,
    String? message,
  }) {
    // if (syncStatus == null) {
    //   _status = BaseCrawlSyncStatus(
    //     code: code,
    //     message: message,
    //     status: status,
    //   );
    // } else {
    //   _status = syncStatus?.copyWith(
    //     status: status,
    //     code: code,
    //     message: message,
    //   );
    // }
    return;
  }
}

extension BaseCrawEntityCollectionExt on List<BaseCrawlEntity> {
  void changeToDownloadStatus({
    required BaseCrawlDownloadStatusEnum status,
    String? code,
    String? message,
  }) {
    // for (var element in this) {
    //   element.changeToDownloadStatus(
    //     status: status,
    //     code: code,
    //     message: message,
    //   );
    // }
    return;
  }

  void changeToSyncStatus({
    required BaseCrawlSyncStatusEnum status,
    String? code,
    String? message,
  }) {
    // for (var element in this) {
    //   element.changeToSyncStatus(
    //     status: status,
    //     code: code,
    //     message: message,
    //   );
    // }
    return;
  }
}
