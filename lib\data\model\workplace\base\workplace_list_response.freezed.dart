// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'workplace_list_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

WorkPlaceListReponse<T> _$WorkPlaceListReponseFromJson<T>(
    Map<String, dynamic> json) {
  return _WorkPlaceListReponse<T>.fromJson(json);
}

/// @nodoc
mixin _$WorkPlaceListReponse<T> {
  @WorkPlaceModelConverter()
  List<T> get data => throw _privateConstructorUsedError;
  WorkPlacePagingResponse? get paging => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $WorkPlaceListReponseCopyWith<T, WorkPlaceListReponse<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorkPlaceListReponseCopyWith<T, $Res> {
  factory $WorkPlaceListReponseCopyWith(WorkPlaceListReponse<T> value,
          $Res Function(WorkPlaceListReponse<T>) then) =
      _$WorkPlaceListReponseCopyWithImpl<T, $Res, WorkPlaceListReponse<T>>;
  @useResult
  $Res call(
      {@WorkPlaceModelConverter() List<T> data,
      WorkPlacePagingResponse? paging});

  $WorkPlacePagingResponseCopyWith<$Res>? get paging;
}

/// @nodoc
class _$WorkPlaceListReponseCopyWithImpl<T, $Res,
        $Val extends WorkPlaceListReponse<T>>
    implements $WorkPlaceListReponseCopyWith<T, $Res> {
  _$WorkPlaceListReponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? paging = freezed,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<T>,
      paging: freezed == paging
          ? _value.paging
          : paging // ignore: cast_nullable_to_non_nullable
              as WorkPlacePagingResponse?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $WorkPlacePagingResponseCopyWith<$Res>? get paging {
    if (_value.paging == null) {
      return null;
    }

    return $WorkPlacePagingResponseCopyWith<$Res>(_value.paging!, (value) {
      return _then(_value.copyWith(paging: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$WorkPlaceListReponseImplCopyWith<T, $Res>
    implements $WorkPlaceListReponseCopyWith<T, $Res> {
  factory _$$WorkPlaceListReponseImplCopyWith(
          _$WorkPlaceListReponseImpl<T> value,
          $Res Function(_$WorkPlaceListReponseImpl<T>) then) =
      __$$WorkPlaceListReponseImplCopyWithImpl<T, $Res>;
  @override
  @useResult
  $Res call(
      {@WorkPlaceModelConverter() List<T> data,
      WorkPlacePagingResponse? paging});

  @override
  $WorkPlacePagingResponseCopyWith<$Res>? get paging;
}

/// @nodoc
class __$$WorkPlaceListReponseImplCopyWithImpl<T, $Res>
    extends _$WorkPlaceListReponseCopyWithImpl<T, $Res,
        _$WorkPlaceListReponseImpl<T>>
    implements _$$WorkPlaceListReponseImplCopyWith<T, $Res> {
  __$$WorkPlaceListReponseImplCopyWithImpl(_$WorkPlaceListReponseImpl<T> _value,
      $Res Function(_$WorkPlaceListReponseImpl<T>) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? paging = freezed,
  }) {
    return _then(_$WorkPlaceListReponseImpl<T>(
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<T>,
      paging: freezed == paging
          ? _value.paging
          : paging // ignore: cast_nullable_to_non_nullable
              as WorkPlacePagingResponse?,
    ));
  }
}

/// @nodoc
@JsonSerializable(createToJson: false)
class _$WorkPlaceListReponseImpl<T> implements _WorkPlaceListReponse<T> {
  _$WorkPlaceListReponseImpl(
      {@WorkPlaceModelConverter() required final List<T> data, this.paging})
      : _data = data;

  factory _$WorkPlaceListReponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorkPlaceListReponseImplFromJson(json);

  final List<T> _data;
  @override
  @WorkPlaceModelConverter()
  List<T> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  final WorkPlacePagingResponse? paging;

  @override
  String toString() {
    return 'WorkPlaceListReponse<$T>(data: $data, paging: $paging)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorkPlaceListReponseImpl<T> &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.paging, paging) || other.paging == paging));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_data), paging);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WorkPlaceListReponseImplCopyWith<T, _$WorkPlaceListReponseImpl<T>>
      get copyWith => __$$WorkPlaceListReponseImplCopyWithImpl<T,
          _$WorkPlaceListReponseImpl<T>>(this, _$identity);
}

abstract class _WorkPlaceListReponse<T> implements WorkPlaceListReponse<T> {
  factory _WorkPlaceListReponse(
      {@WorkPlaceModelConverter() required final List<T> data,
      final WorkPlacePagingResponse? paging}) = _$WorkPlaceListReponseImpl<T>;

  factory _WorkPlaceListReponse.fromJson(Map<String, dynamic> json) =
      _$WorkPlaceListReponseImpl<T>.fromJson;

  @override
  @WorkPlaceModelConverter()
  List<T> get data;
  @override
  WorkPlacePagingResponse? get paging;
  @override
  @JsonKey(ignore: true)
  _$$WorkPlaceListReponseImplCopyWith<T, _$WorkPlaceListReponseImpl<T>>
      get copyWith => throw _privateConstructorUsedError;
}
