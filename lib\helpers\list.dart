extension ListExtension<T> on List<T> {
  List<List<T>> splitList(int maxLength) {
    final originalList = this;
    return List.generate(
      (originalList.length / maxLength).ceil(),
      (index) => originalList.skip(index * maxLength).take(maxLength).toList(),
    );
  }

  List<List<T>> splitToNumList(int numList) {
    final input = this;
    List<List<T>> output = [];

    int start = 0;
    final int end = input.length;

    if (numList > end) {
      numList = end;
    }

    final int itemPerList = ((end - start) / numList).ceil();

    int totalPages = (end / itemPerList).ceil();
    int maxLoops = numList;

    if (totalPages < numList) {
      maxLoops = totalPages;
    }

    for (var i = 1; i <= maxLoops; i++) {
      if (i != 1) {
        start = itemPerList * (i - 1);
      }

      int takeCount = itemPerList;
      if ((end - start) < itemPerList) {
        takeCount = end - start;
      }

      final newInputs = input.skip(start).take(takeCount).toList();
      output.add(newInputs);
    }
    return output;
  }
}
