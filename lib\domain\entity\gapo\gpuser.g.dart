// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'gpuser.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GPUser _$GPUserFromJson(Map<String, dynamic> json) => GPUser(
      name: json['name'] as String?,
      avatar: json['avatar'] as String?,
      type: json['type'] as String?,
      displayName: json['displayName'] as String?,
      id: (json['id'] as num?)?.toInt(),
      avatarThumbPattern: json['avatarThumbPattern'] as String?,
      statusVerify: (json['statusVerify'] as num?)?.toInt(),
    );

Map<String, dynamic> _$GPUserToJson(GPUser instance) => <String, dynamic>{
      'name': instance.name,
      'avatar': instance.avatar,
      'type': instance.type,
      'id': instance.id,
      'displayName': instance.displayName,
      'avatarThumbPattern': instance.avatarThumbPattern,
      'statusVerify': instance.statusVerify,
    };
