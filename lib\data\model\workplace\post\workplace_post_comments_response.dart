import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'workplace_post_comments_response.g.dart';

@JsonSerializable(createToJson: false, fieldRename: FieldRename.snake)
@DateTimeConverter()
class WorkPlaceCommentsResponse {
  WorkPlaceCommentsResponse({
    required this.id,
    this.commentCount,
    this.likeCount,
    this.message,
    this.createdTime,
    this.from,
    this.attachment,
    this.messageTags,
    this.reactions,
    this.likes,
  });
  final String id;

  final String? message;
  final DateTime? createdTime;
  final WorkPlaceUser? from;

  final int? commentCount;
  final int? likeCount;

  final WorkPlacePostAttachmentsResponse? attachment;

  final List<WorkPlaceMessageTags>? messageTags;

  final WorkPlaceListReponse<WorkPlaceReaction>? reactions;
  final WorkPlaceListReponse<WorkPlaceReaction>? likes;

  factory WorkPlaceCommentsResponse.fromJson(Map<String, dynamic> json) =>
      _$WorkPlaceCommentsResponseFromJson(json);
}
