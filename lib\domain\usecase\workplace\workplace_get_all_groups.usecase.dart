/*
 * Created Date: Sunday, 2nd June 2024, 16:26:36
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Wednesday, 28th August 2024 11:25:31
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:developer';

import 'package:gp_core/core.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';
import 'package:retry/retry.dart';

// ignore_for_file: public_member_api_docs

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class WorkPlaceGetAllGroupsUseCase extends GPBaseFutureUseCase<
        WorkPlaceGroupsInput, WorkPlaceListReponse<WorkPlaceGroupResponse>>
    with WorkPlaceFetchAllDataMixin {
  WorkPlaceGetAllGroupsUseCase(
    @Named('kWorkPlaceRepository') this._worplaceRepository,
  );

  final WorkPlaceRepository _worplaceRepository;

  @override
  Future<WorkPlaceListReponse<WorkPlaceGroupResponse>> buildUseCase(
    WorkPlaceGroupsInput input,
  ) async {
    return await retry(
      () async {
        final params = WorkPlaceBaseParams(
          id: input.communityId,
          fields: input.fields,
        );

        return await fetchAllData<WorkPlaceGroupResponse>(
          params: params,
          loadFunction: _worplaceRepository.groups,
          saveData: input.saveData,
        );
      },
      maxAttempts: 3,
      retryIf: (e) {
        if (e is DioException && e.response?.statusCode == 400) {
          return false;
        }

        return true;
      },
      onRetry: (e) {
        log('ERROR: WorkPlaceGetAllGroupsUseCase error -> $e');
      },
    );
  }
}

class WorkPlaceGroupsInput extends GPBaseInput {
  const WorkPlaceGroupsInput({
    this.fields =
        'name,privacy,created_time,updated_time,archived,post_requires_admin_approval,cover,icon,owner,admins,description',
    required this.communityId,
    this.groupIds,
    required this.saveData,
  });

  final String? fields;
  final String communityId;
  final List<String>? groupIds;
  final Future Function(List<WorkPlaceGroupResponse>, Map<String, String>?)
      saveData;
}

class WorkPlaceGroupParams {
  const WorkPlaceGroupParams({
    this.fields,
    this.next,
    required this.communityId,
  });

  final String? fields;
  final String? next;
  final String communityId;
}
