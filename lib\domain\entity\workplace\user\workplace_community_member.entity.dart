/*
 * Created Date: Tuesday, 11th June 2024, 08:46:28
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Wednesday, 28th August 2024 17:51:15
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_fbwp_crawler/domain/entity/entity.dart';
import 'package:isar/isar.dart';

part 'workplace_community_member.entity.g.dart';

@Collection()
class WorkPlaceCommunityMemberEntity extends BaseCrawlEntity {
  WorkPlaceCommunityMemberEntity({
    required super.id,
    super.crawlType = GPBaseCrawlType.user,
    this.name,
    this.administrator,
    this.email,
    this.organization,
    this.division,
    this.department,
    this.primaryPhone,
    this.picture,
    this.active,
    this.gpUserId,
    this.cover,
    this.insertedAt,
  });

  final String? name;
  final bool? administrator;
  final String? email;
  int? gpUserId;

  /// Tổ chức của người này trên Workplace nếu được đặt thông qua API Quản lý tà<PERSON> khoản.
  final String? organization;

  /// Bộ phận của người này trên Workplace nếu được đặt thông qua API Quản lý tài khoản.
  final String? division;

  /// Tên phòng ban của người này trên Workplace nếu được đặt thông qua API Quản lý tài khoản.
  final String? department;

  final String? primaryPhone;
  final String? picture;
  final String? cover;
  final bool? active;

  DateTime? insertedAt;

  // final groups = IsarLinks<WorkPlaceGroupEntity>();

  late final Id? dbId = id.hashCode;
}
