import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_core_v2/base/base.dart';
import 'package:injectable/injectable.dart';
import 'package:gp_shared/mapper/gp_mapper.dart';

import '{{feature_name.snakeCase()}}_event.dart';
import '{{feature_name.snakeCase()}}_state.dart';

/// created by gp_brick
/// remember to change this description
/// and remove `{{feature_name.pascalCase()}}DefaultEvent`
@LazySingleton()
final class {{feature_name.pascalCase()}}Bloc
    extends CoreV2BaseBloc<CoreV2BaseEvent, {{feature_name.pascalCase()}}State>
    with GPMapperMixin {
  {{feature_name.pascalCase()}}Bloc() : super(const {{feature_name.pascalCase()}}State()) {
    on<{{feature_name.pascalCase()}}DefaultEvent>(
      _on{{feature_name.pascalCase()}}DefaultEvent,
    );
  }

  FutureOr _on{{feature_name.pascalCase()}}DefaultEvent(
    {{feature_name.pascalCase()}}DefaultEvent event,
    Emitter<{{feature_name.pascalCase()}}State> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      action: () async {
        // TODO:
      },
    );
  }
}
