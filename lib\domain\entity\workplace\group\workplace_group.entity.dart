/*
 * Created Date: Tuesday, 11th June 2024, 08:46:28
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Sunday, 8th September 2024 13:44:25
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_fbwp_crawler/data/model/workplace/enums/workplace_enums.dart';
import 'package:gp_fbwp_crawler/domain/entity/entity.dart';
import 'package:isar/isar.dart';

part 'workplace_group.entity.g.dart';

@Collection()
class WorkPlaceGroupEntity extends BaseCrawlEntity {
  WorkPlaceGroupEntity({
    required super.id,
    super.crawlType = GPBaseCrawlType.group,
    this.name,
    this.privacy,
    this.createdTime,
    this.updatedTime,
    this.archived,
    this.postRequiresAdminApproval,
    this.cover,
    this.icon,
    this.description,
    WorkPlaceCommunityMemberEntity? owner,
    List<WorkPlaceCommunityMemberEntity>? admins,
    List<WorkPlaceCommunityMemberEntity>? members,
    this.insertedAt,
  }) {
    if (owner != null) {
      this.owner.value = owner;
    }

    if (admins != null && admins.isNotEmpty) {
      this.admins.addAll(admins);
    }

    if (members != null && members.isNotEmpty) {
      this.members.addAll(members);
    }
  }

  final String? name;
  @Enumerated(EnumType.name)
  final WorkPlaceGroupPrivacy? privacy;
  final DateTime? createdTime;
  final DateTime? updatedTime;
  final bool? archived;
  final bool? postRequiresAdminApproval;
  final GroupCoverEntity? cover;
  final String? icon;
  String? gpCoverLink;
  String? gpGroupId;
  final String? description;

  final owner = IsarLink<WorkPlaceCommunityMemberEntity>();
  final admins = IsarLinks<WorkPlaceCommunityMemberEntity>();
  final members = IsarLinks<WorkPlaceCommunityMemberEntity>();

  DateTime? insertedAt;

  late final Id? dbId = id.hashCode;

  @ignore
  GPGroupPrivacy get gpPrivacy => privacy == WorkPlaceGroupPrivacy.open
      ? GPGroupPrivacy.public
      : GPGroupPrivacy.closed;

  @ignore
  GPGroupDiscoverability get gpDiscoverability =>
      privacy == WorkPlaceGroupPrivacy.secret
          ? GPGroupDiscoverability.hidden
          : GPGroupDiscoverability.visible;
}

@embedded
class GroupCoverEntity {
  GroupCoverEntity({this.id, this.source, this.coverId});
  String? id;
  String? source;
  String? coverId;
  String? gpCoverLink;

  String? localFilePath;
}
