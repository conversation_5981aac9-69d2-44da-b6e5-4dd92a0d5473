// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workplace_conversations_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkPlaceUserConversationsResponse _$WorkPlaceUserConversationsResponseFromJson(
        Map<String, dynamic> json) =>
    WorkPlaceUserConversationsResponse(
      id: json['id'] as String,
      messages: json['messages'] == null
          ? null
          : WorkPlaceListReponse<Messages>.fromJson(
              json['messages'] as Map<String, dynamic>),
      participants: json['participants'] == null
          ? null
          : WorkPlaceListReponse<WorkPlaceUser>.from<PERSON>son(
              json['participants'] as Map<String, dynamic>),
      userId: json['userId'] as String?,
      name: json['name'] as String?,
      link: json['link'] as String?,
    );

Messages _$MessagesFromJson(Map<String, dynamic> json) => Messages(
      id: json['id'] as String,
      attachments: json['attachments'] == null
          ? null
          : WorkPlaceListReponse<
                  WorkPlaceConversationAttachmentsResponse>.fromJson(
              json['attachments'] as Map<String, dynamic>),
      message: json['message'] as String?,
      from: json['from'] == null
          ? null
          : WorkPlaceUser.fromJson(json['from'] as Map<String, dynamic>),
      to: json['to'] == null
          ? null
          : WorkPlaceListReponse<WorkPlaceUser>.fromJson(
              json['to'] as Map<String, dynamic>),
      createdTime: _$JsonConverterFromJson<String, DateTime?>(
          json['created_time'], const DateTimeConverter().fromJson),
      sticker: json['sticker'] as String?,
    );

Value? _$JsonConverterFromJson<Json, Value>(
  Object? json,
  Value? Function(Json json) fromJson,
) =>
    json == null ? null : fromJson(json as Json);
