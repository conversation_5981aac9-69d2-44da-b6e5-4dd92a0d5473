/*
 * Created Date: Friday, 21st June 2024, 09:59:32
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Friday, 13th September 2024 00:13:49
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:async';
import 'dart:developer';

import 'package:gp_core/core.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_fbwp_crawler/data/model/model.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:injectable/injectable.dart';
import 'package:retry/retry.dart';

// ignore_for_file: public_member_api_docs

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class WorkPlaceUserMessagesUseCase extends GPBaseFutureUseCase<
    WorkPlaceUserMessageInput,
    WorkPlaceListReponse<Messages>> with WorkPlaceFetchAllDataMixin {
  WorkPlaceUserMessagesUseCase(
    @Named('kWorkPlaceRepository') this._worplaceRepository,
  );

  final WorkPlaceRepository _worplaceRepository;

  @override
  Future<WorkPlaceListReponse<Messages>> buildUseCase(
    WorkPlaceUserMessageInput input,
  ) async {
    final completer = Completer<WorkPlaceListReponse<Messages>>();
    await retry(
      () async {
        final params = WorkPlaceMessageParams(
          id: input.conversationId,
          fields: input.fields,
          user: input.user,
          limit: '5',
        );

        final results = await fetchAllData<Messages>(
          params: params,
          loadFunction: _worplaceRepository.messages,
          saveData: input.saveData,
        );

        completer.complete(results);

        return results;
      },
      maxAttempts: 2,
      retryIf: (e) async {
        if (e is DioException && e.response?.statusCode == 400) {
          try {
            if (e.response?.data['error']['code'] == 100) {
              return true;
            }
          } catch (ex) {
            return false;
          }

          return false;
        }

        return true;
      },
      onRetry: (e) async {
        log('ERROR: WorkPlaceUserConversationsUseCase error -> $e');

        if (input.secondaryUsers.isNotEmpty) {
          if (input.secondaryUsers.contains(input.user)) {
            input.secondaryUsers.remove(input.user);
          }

          for (var userId in input.secondaryUsers) {
            final params = WorkPlaceMessageParams(
              id: input.conversationId,
              fields: input.fields,
              user: userId,
              limit: '5',
            );

            final results = await fetchAllData<Messages>(
              params: params,
              loadFunction: _worplaceRepository.messages,
              saveData: input.saveData,
            );

            completer.complete(results);
          }
        }
      },
    );

    return completer.future;
  }
}

class WorkPlaceUserMessageInput extends GPBaseInput {
  const WorkPlaceUserMessageInput({
    required this.conversationId,
    required this.user,
    required this.secondaryUsers,
    this.fields = 'id,message,attachments,created_time,from,sticker',
    required this.saveData,
  });

  final String conversationId;
  final String user;
  final List<String> secondaryUsers;
  final String? fields;
  final Future Function(List<Messages>, Map<String, String>?) saveData;
}
