import 'package:json_annotation/json_annotation.dart';

part 'workplace_user.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class WorkPlaceUser {
  WorkPlaceUser({
    required this.id,
    this.name,
    this.administrator,
    this.email,
    this.organization,
    this.division,
    this.department,
    this.primaryPhone,
    this.picture,
    this.active,
    this.cover,
  });

  final String? name;
  final String id;
  final bool? administrator;
  final String? email;

  /// Tổ chức của người này trên Workplace nếu được đặt thông qua API Quản lý tài khoản.
  final String? organization;

  /// Bộ phận của người này trên Workplace nếu được đặt thông qua API Quản lý tài khoản.
  final String? division;

  /// Tên phòng ban của người này trên Workplace nếu được đặt thông qua API Quản lý tài khoản.
  final String? department;

  final String? primaryPhone;
  final UserPicture? picture;
  final bool? active;
  final UserCover? cover;

  factory WorkPlaceUser.fromJson(Map<String, dynamic> json) =>
      _$WorkPlaceUserFromJson(json);

  Map<String, dynamic> toJson() => _$WorkPlaceUserToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.snake)
class UserPicture {
  UserPicture({required this.data});
  final PictureData data;

  factory UserPicture.fromJson(Map<String, dynamic> json) =>
      _$UserPictureFromJson(json);

  Map<String, dynamic> toJson() => _$UserPictureToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.snake)
class PictureData {
  final String? url;
  final int? width;
  final int? height;

  PictureData({this.url, this.width, this.height});

  factory PictureData.fromJson(Map<String, dynamic> json) =>
      _$PictureDataFromJson(json);

  Map<String, dynamic> toJson() => _$PictureDataToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.snake)
class UserCover {
  final String? source;
  final int? offsetX;
  final int? offsetY;

  UserCover({this.source, this.offsetX, this.offsetY});

  factory UserCover.fromJson(Map<String, dynamic> json) =>
      _$UserCoverFromJson(json);

  Map<String, dynamic> toJson() => _$UserCoverToJson(this);
}
