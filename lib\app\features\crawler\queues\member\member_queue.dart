import 'dart:async';

import 'package:get_it/get_it.dart';
import 'package:gp_fbwp_crawler/app/app.dart';
import 'package:gp_fbwp_crawler/data/data.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:gp_fbwp_crawler/helpers/helpers.dart';

final class MemberQueue extends BaseMemberQueue with CheckpointMixin {
  MemberQueue({
    required super.crawlQueueBloc,
    required super.commonBloc,
  });

  ///  prepare admin account
  Future<InitGPAdminOutput?> initialGPAdminAccount() async {
    final adminOutput = await addToQueue<InitGPAdminOutput>(
      message: l10n.crawl_user_get_wp_users,
      job: () async {
        return await GetIt.I<InitGPAdminUseCase>().execute(
          const InitGPAdminInput(),
        );
      },
    );

    if (adminOutput == null || adminOutput.isSuccess == false) {
      // ignore: invalid_use_of_protected_member
      commonBloc.addError(Exception(kExceptionAdminAccountNotFound));
    }

    return adminOutput;
  }

  Future<List<WorkPlaceCommunityMemberEntity>> getWorkPlaceMembers() async {
    await _getMembers();

    return await localService.getAllMembers();

    // if (AppConstants.enableEmailFilter) {
    //   _filterUserByEmail(userEntitys);
    // }

    // await _signupUsers(userEntitys);

    // await _inviteToWorkspace(userEntitys);

    // _saveCSV(userEntitys);
  }

  // void _filterUserByEmail(List<WorkPlaceCommunityMemberEntity> userEntitys) {
  //   userEntitys.removeWhere((element) =>
  //       AppConstants.filterEmails.contains(element.email) == false);
  // }

  Future _getMembers() async {
    final checkpoint =
        await localService.latestCheckpoint(GPBaseCrawlType.user);
    if (checkpoint?.isDone == true) return;
    emitInfo(
      l10n.crawl_user_get_wp_users,
      WPTaskStatus.inProgress,
    );
    final WorkPlaceCommunityEntity community =
        await localService.getCommunity();

    if (AppConstants.userIds.isNotEmpty) {
      await wpCommunityMembersByIdsUseCase.execute(
        WorkPlaceCommunityMembersInput(
            communityId: community.id,
            memberIds: AppConstants.userIds,
            saveData: (data, _) async {
              await _saveMembersToLocal(data);
            }),
      );
    } else {
      await wpCommunityMembersUseCase.execute(
        WorkPlaceCommunityMembersInput(
            communityId: community.id,
            saveData: (data, _) async {
              await _saveMembersToLocal(data);
            }),
      );
    }

    await setDoneCheckpoint(GPBaseCrawlType.user);

    emitInfo(
      l10n.crawl_user_get_wp_users,
      WPTaskStatus.done,
    );
  }

  Future _saveMembersToLocal(List<WorkPlaceUser> members) async {
    final userEntitys =
        convertList<WorkPlaceUser, WorkPlaceCommunityMemberEntity>(members);
    await saveMembers(userEntitys);
  }

  String saveCSV(List<WorkPlaceCommunityMemberEntity> members) {
    return addToQueueSync<String>(
          exportCsv: true,
          message: l10n.crawl_user_save_csv,
          job: () {
            final List<GPUser> gapoUsers =
                convertList<WorkPlaceCommunityMemberEntity, GPUser>(members);

            members.changeToSyncStatus(
              status: BaseCrawlSyncStatusEnum.synced,
            );

            return CsvHelper.toCSV(gapoUsers.map((e) => e.toJson()).toList());
          },
        ) ??
        '';
  }
}

