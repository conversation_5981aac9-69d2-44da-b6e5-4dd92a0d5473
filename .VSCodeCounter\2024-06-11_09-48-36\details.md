# Details

Date : 2024-06-11 09:48:36

Directory /Users/<USER>/Softwares/flutter.packages/gapoflutter-crawler

Total : 215 files,  24844 codes, 1070 comments, 2911 blanks, all 28825 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [README.md](/README.md) | Markdown | 40 | 0 | 12 | 52 |
| [analysis_options.yaml](/analysis_options.yaml) | YAML | 7 | 18 | 3 | 28 |
| [android/app/build.gradle](/android/app/build.gradle) | Groovy | 51 | 5 | 12 | 68 |
| [android/app/src/debug/AndroidManifest.xml](/android/app/src/debug/AndroidManifest.xml) | XML | 7 | 4 | 0 | 11 |
| [android/app/src/main/AndroidManifest.xml](/android/app/src/main/AndroidManifest.xml) | XML | 32 | 6 | 0 | 38 |
| [android/app/src/main/kotlin/vn/gapowork/crawler/MainActivity.kt](/android/app/src/main/kotlin/vn/gapowork/crawler/MainActivity.kt) | Kotlin | 4 | 0 | 3 | 7 |
| [android/app/src/main/res/drawable-v21/launch_background.xml](/android/app/src/main/res/drawable-v21/launch_background.xml) | XML | 4 | 7 | 2 | 13 |
| [android/app/src/main/res/drawable/launch_background.xml](/android/app/src/main/res/drawable/launch_background.xml) | XML | 4 | 7 | 2 | 13 |
| [android/app/src/main/res/values-night/styles.xml](/android/app/src/main/res/values-night/styles.xml) | XML | 9 | 9 | 1 | 19 |
| [android/app/src/main/res/values/styles.xml](/android/app/src/main/res/values/styles.xml) | XML | 9 | 9 | 1 | 19 |
| [android/build.gradle](/android/build.gradle) | Groovy | 27 | 0 | 5 | 32 |
| [android/gradle.properties](/android/gradle.properties) | Java Properties | 3 | 0 | 1 | 4 |
| [android/gradle/wrapper/gradle-wrapper.properties](/android/gradle/wrapper/gradle-wrapper.properties) | Java Properties | 5 | 0 | 1 | 6 |
| [android/settings.gradle](/android/settings.gradle) | Groovy | 16 | 0 | 5 | 21 |
| [assets/images/splash_loading_1.json](/assets/images/splash_loading_1.json) | JSON | 1 | 0 | 0 | 1 |
| [assets/images/splash_loading_2.json](/assets/images/splash_loading_2.json) | JSON | 1 | 0 | 0 | 1 |
| [ios/Runner/AppDelegate.swift](/ios/Runner/AppDelegate.swift) | Swift | 12 | 0 | 2 | 14 |
| [ios/Runner/Assets.xcassets/AppIcon-dev.appiconset/Contents.json](/ios/Runner/Assets.xcassets/AppIcon-dev.appiconset/Contents.json) | JSON | 1 | 0 | 0 | 1 |
| [ios/Runner/Assets.xcassets/AppIcon-prod.appiconset/Contents.json](/ios/Runner/Assets.xcassets/AppIcon-prod.appiconset/Contents.json) | JSON | 1 | 0 | 0 | 1 |
| [ios/Runner/Assets.xcassets/AppIcon-uat.appiconset/Contents.json](/ios/Runner/Assets.xcassets/AppIcon-uat.appiconset/Contents.json) | JSON | 1 | 0 | 0 | 1 |
| [ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json](/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json) | JSON | 122 | 0 | 1 | 123 |
| [ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json](/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json) | JSON | 23 | 0 | 1 | 24 |
| [ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md](/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md) | Markdown | 3 | 0 | 2 | 5 |
| [ios/Runner/Base.lproj/LaunchScreen.storyboard](/ios/Runner/Base.lproj/LaunchScreen.storyboard) | XML | 36 | 1 | 1 | 38 |
| [ios/Runner/Base.lproj/Main.storyboard](/ios/Runner/Base.lproj/Main.storyboard) | XML | 25 | 1 | 1 | 27 |
| [ios/Runner/Runner-Bridging-Header.h](/ios/Runner/Runner-Bridging-Header.h) | C++ | 1 | 0 | 1 | 2 |
| [ios/RunnerTests/RunnerTests.swift](/ios/RunnerTests/RunnerTests.swift) | Swift | 7 | 2 | 4 | 13 |
| [lib/app/app.dart](/lib/app/app.dart) | Dart | 7 | 0 | 1 | 8 |
| [lib/app/app_config/app_config.dart](/lib/app/app_config/app_config.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/app/app_config/bloc/app_config_bloc.dart](/lib/app/app_config/bloc/app_config_bloc.dart) | Dart | 73 | 0 | 12 | 85 |
| [lib/app/app_config/bloc/app_config_event.dart](/lib/app/app_config/bloc/app_config_event.dart) | Dart | 12 | 0 | 4 | 16 |
| [lib/app/app_config/bloc/app_config_state.dart](/lib/app/app_config/bloc/app_config_state.dart) | Dart | 17 | 1 | 5 | 23 |
| [lib/app/app_config/bloc/app_config_state.freezed.dart](/lib/app/app_config/bloc/app_config_state.freezed.dart) | Dart | 118 | 15 | 23 | 156 |
| [lib/app/app_config/bloc/bloc.dart](/lib/app/app_config/bloc/bloc.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/app/app_config/widgets/app_config_body_page.dart](/lib/app/app_config/widgets/app_config_body_page.dart) | Dart | 8 | 0 | 3 | 11 |
| [lib/app/app_config/widgets/widgets.dart](/lib/app/app_config/widgets/widgets.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/app/base/base.dart](/lib/app/base/base.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/app/base/networking/logger_inteceptor.dart](/lib/app/base/networking/logger_inteceptor.dart) | Dart | 75 | 7 | 18 | 100 |
| [lib/app/base/networking/networking.dart](/lib/app/base/networking/networking.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/app/base/networking/workplace_auth_inteceptor.dart](/lib/app/base/networking/workplace_auth_inteceptor.dart) | Dart | 29 | 0 | 8 | 37 |
| [lib/app/constant/app_constant.dart](/lib/app/constant/app_constant.dart) | Dart | 7 | 0 | 2 | 9 |
| [lib/app/constant/constant.dart](/lib/app/constant/constant.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/app/constant/fb_wp_url.constants.dart](/lib/app/constant/fb_wp_url.constants.dart) | Dart | 15 | 11 | 5 | 31 |
| [lib/app/constant/gapo_url.constants.dart](/lib/app/constant/gapo_url.constants.dart) | Dart | 6 | 11 | 4 | 21 |
| [lib/app/features/crawler/bloc/bloc.dart](/lib/app/features/crawler/bloc/bloc.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/app/features/crawler/bloc/crawl_bloc.dart](/lib/app/features/crawler/bloc/crawl_bloc.dart) | Dart | 220 | 14 | 33 | 267 |
| [lib/app/features/crawler/bloc/crawl_event.dart](/lib/app/features/crawler/bloc/crawl_event.dart) | Dart | 11 | 0 | 4 | 15 |
| [lib/app/features/crawler/bloc/crawl_state.dart](/lib/app/features/crawler/bloc/crawl_state.dart) | Dart | 30 | 1 | 6 | 37 |
| [lib/app/features/crawler/bloc/crawl_state.freezed.dart](/lib/app/features/crawler/bloc/crawl_state.freezed.dart) | Dart | 256 | 23 | 44 | 323 |
| [lib/app/features/crawler/crawl.dart](/lib/app/features/crawler/crawl.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/app/features/crawler/crawl.main.page.dart](/lib/app/features/crawler/crawl.main.page.dart) | Dart | 101 | 2 | 3 | 106 |
| [lib/app/features/crawler/crawler.dart](/lib/app/features/crawler/crawler.dart) | Dart | 5 | 0 | 1 | 6 |
| [lib/app/features/crawler/sync/sync.dart](/lib/app/features/crawler/sync/sync.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/app/features/crawler/sync/sync.page.dart](/lib/app/features/crawler/sync/sync.page.dart) | Dart | 0 | 0 | 2 | 2 |
| [lib/app/features/crawler/unsync/unsync.dart](/lib/app/features/crawler/unsync/unsync.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/app/features/crawler/unsync/unsync.page.dart](/lib/app/features/crawler/unsync/unsync.page.dart) | Dart | 0 | 0 | 2 | 2 |
| [lib/app/features/features.dart](/lib/app/features/features.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/app/main.app.dart](/lib/app/main.app.dart) | Dart | 55 | 0 | 4 | 59 |
| [lib/app/splash/splash.dart](/lib/app/splash/splash.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/app/splash/splash.page.dart](/lib/app/splash/splash.page.dart) | Dart | 28 | 0 | 3 | 31 |
| [lib/app/test_async.page.dart](/lib/app/test_async.page.dart) | Dart | 118 | 5 | 26 | 149 |
| [lib/config/app_configs.dart](/lib/config/app_configs.dart) | Dart | 29 | 0 | 11 | 40 |
| [lib/config/bootstrap.dart](/lib/config/bootstrap.dart) | Dart | 18 | 0 | 5 | 23 |
| [lib/config/config.dart](/lib/config/config.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/data.dart](/lib/data/data.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/data/data_source/data_source.dart](/lib/data/data_source/data_source.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/data_source/local/local.dart](/lib/data/data_source/local/local.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/data/data_source/local/workplace_local.service.dart](/lib/data/data_source/local/workplace_local.service.dart) | Dart | 45 | 9 | 13 | 67 |
| [lib/data/data_source/remote/auth.service.dart](/lib/data/data_source/remote/auth.service.dart) | Dart | 21 | 9 | 5 | 35 |
| [lib/data/data_source/remote/auth.service.g.dart](/lib/data/data_source/remote/auth.service.g.dart) | Dart | 60 | 5 | 13 | 78 |
| [lib/data/data_source/remote/facebook.service.dart](/lib/data/data_source/remote/facebook.service.dart) | Dart | 20 | 10 | 6 | 36 |
| [lib/data/data_source/remote/facebook.service.g.dart](/lib/data/data_source/remote/facebook.service.g.dart) | Dart | 60 | 5 | 13 | 78 |
| [lib/data/data_source/remote/remote.dart](/lib/data/data_source/remote/remote.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/data/data_source/remote/workplace.service.dart](/lib/data/data_source/remote/workplace.service.dart) | Dart | 48 | 10 | 11 | 69 |
| [lib/data/data_source/remote/workplace.service.g.dart](/lib/data/data_source/remote/workplace.service.g.dart) | Dart | 228 | 5 | 18 | 251 |
| [lib/data/model/datetime_converter.dart](/lib/data/model/datetime_converter.dart) | Dart | 11 | 0 | 3 | 14 |
| [lib/data/model/facebook/community_response.dart](/lib/data/model/facebook/community_response.dart) | Dart | 16 | 0 | 5 | 21 |
| [lib/data/model/facebook/community_response.g.dart](/lib/data/model/facebook/community_response.g.dart) | Dart | 8 | 4 | 4 | 16 |
| [lib/data/model/facebook/facebook.dart](/lib/data/model/facebook/facebook.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/data/model/gpw/auth_reponse.dart](/lib/data/model/gpw/auth_reponse.dart) | Dart | 15 | 0 | 7 | 22 |
| [lib/data/model/gpw/auth_reponse.g.dart](/lib/data/model/gpw/auth_reponse.g.dart) | Dart | 6 | 4 | 4 | 14 |
| [lib/data/model/gpw/gpw.dart](/lib/data/model/gpw/gpw.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/data/model/model.dart](/lib/data/model/model.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/data/model/workplace/base/base.dart](/lib/data/model/workplace/base/base.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/data/model/workplace/base/workplace_generic_data_converter.dart](/lib/data/model/workplace/base/workplace_generic_data_converter.dart) | Dart | 42 | 0 | 7 | 49 |
| [lib/data/model/workplace/base/workplace_list_response.dart](/lib/data/model/workplace/base/workplace_list_response.dart) | Dart | 16 | 0 | 6 | 22 |
| [lib/data/model/workplace/base/workplace_list_response.g.dart](/lib/data/model/workplace/base/workplace_list_response.g.dart) | Dart | 12 | 4 | 4 | 20 |
| [lib/data/model/workplace/base/workplace_paging_response.dart](/lib/data/model/workplace/base/workplace_paging_response.dart) | Dart | 26 | 0 | 7 | 33 |
| [lib/data/model/workplace/base/workplace_paging_response.g.dart](/lib/data/model/workplace/base/workplace_paging_response.g.dart) | Dart | 17 | 4 | 5 | 26 |
| [lib/data/model/workplace/base/workplace_user.dart](/lib/data/model/workplace/base/workplace_user.dart) | Dart | 31 | 5 | 11 | 47 |
| [lib/data/model/workplace/base/workplace_user.g.dart](/lib/data/model/workplace/base/workplace_user.g.dart) | Dart | 1,398 | 9 | 115 | 1,522 |
| [lib/data/model/workplace/community/community.dart](/lib/data/model/workplace/community/community.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/data/model/workplace/community/workplace_community_members_response.dart](/lib/data/model/workplace/community/workplace_community_members_response.dart) | Dart | 30 | 3 | 9 | 42 |
| [lib/data/model/workplace/community/workplace_community_members_response.g.dart](/lib/data/model/workplace/community/workplace_community_members_response.g.dart) | Dart | 15 | 4 | 4 | 23 |
| [lib/data/model/workplace/enums/enums.dart](/lib/data/model/workplace/enums/enums.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/data/model/workplace/enums/workplace_enums.dart](/lib/data/model/workplace/enums/workplace_enums.dart) | Dart | 16 | 0 | 4 | 20 |
| [lib/data/model/workplace/group/group.dart](/lib/data/model/workplace/group/group.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/data/model/workplace/group/workplace_group_feeds_response.dart](/lib/data/model/workplace/group/workplace_group_feeds_response.dart) | Dart | 29 | 2 | 7 | 38 |
| [lib/data/model/workplace/group/workplace_group_feeds_response.g.dart](/lib/data/model/workplace/group/workplace_group_feeds_response.g.dart) | Dart | 37 | 4 | 6 | 47 |
| [lib/data/model/workplace/group/workplace_group_members_response.dart](/lib/data/model/workplace/group/workplace_group_members_response.dart) | Dart | 29 | 3 | 9 | 41 |
| [lib/data/model/workplace/group/workplace_group_members_response.g.dart](/lib/data/model/workplace/group/workplace_group_members_response.g.dart) | Dart | 15 | 4 | 4 | 23 |
| [lib/data/model/workplace/group/workplace_group_response.dart](/lib/data/model/workplace/group/workplace_group_response.dart) | Dart | 48 | 0 | 7 | 55 |
| [lib/data/model/workplace/group/workplace_group_response.g.dart](/lib/data/model/workplace/group/workplace_group_response.g.dart) | Dart | 692 | 9 | 62 | 763 |
| [lib/data/model/workplace/post/post.dart](/lib/data/model/workplace/post/post.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/model/workplace/post/workplace_post_attachments_response.dart](/lib/data/model/workplace/post/workplace_post_attachments_response.dart) | Dart | 58 | 0 | 12 | 70 |
| [lib/data/model/workplace/post/workplace_post_attachments_response.g.dart](/lib/data/model/workplace/post/workplace_post_attachments_response.g.dart) | Dart | 53 | 4 | 9 | 66 |
| [lib/data/model/workplace/post/workplace_post_comments_response.dart](/lib/data/model/workplace/post/workplace_post_comments_response.dart) | Dart | 19 | 0 | 5 | 24 |
| [lib/data/model/workplace/post/workplace_post_comments_response.g.dart](/lib/data/model/workplace/post/workplace_post_comments_response.g.dart) | Dart | 17 | 4 | 5 | 26 |
| [lib/data/model/workplace/workplace.dart](/lib/data/model/workplace/workplace.dart) | Dart | 5 | 0 | 1 | 6 |
| [lib/data/repository/facebook_repo_impl.dart](/lib/data/repository/facebook_repo_impl.dart) | Dart | 16 | 0 | 4 | 20 |
| [lib/data/repository/repository.dart](/lib/data/repository/repository.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/data/repository/workplace_repo_impl.dart](/lib/data/repository/workplace_repo_impl.dart) | Dart | 54 | 10 | 12 | 76 |
| [lib/di/component/app.component.config.dart](/lib/di/component/app.component.config.dart) | Dart | 243 | 8 | 12 | 263 |
| [lib/di/component/app.component.dart](/lib/di/component/app.component.dart) | Dart | 18 | 9 | 5 | 32 |
| [lib/di/component/component.dart](/lib/di/component/component.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/di/di.dart](/lib/di/di.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/di/modules/app.module.dart](/lib/di/modules/app.module.dart) | Dart | 26 | 9 | 6 | 41 |
| [lib/di/modules/auth.module.dart](/lib/di/modules/auth.module.dart) | Dart | 17 | 11 | 6 | 34 |
| [lib/di/modules/client.module.dart](/lib/di/modules/client.module.dart) | Dart | 27 | 9 | 8 | 44 |
| [lib/di/modules/database.module.dart](/lib/di/modules/database.module.dart) | Dart | 25 | 10 | 5 | 40 |
| [lib/di/modules/modules.dart](/lib/di/modules/modules.dart) | Dart | 6 | 0 | 1 | 7 |
| [lib/di/modules/navigator.module.dart](/lib/di/modules/navigator.module.dart) | Dart | 17 | 10 | 6 | 33 |
| [lib/di/modules/url.module.dart](/lib/di/modules/url.module.dart) | Dart | 19 | 9 | 6 | 34 |
| [lib/domain/domain.dart](/lib/domain/domain.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/domain/entity/base/app/app.dart](/lib/domain/entity/base/app/app.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/domain/entity/base/app/app_config.entity.dart](/lib/domain/entity/base/app/app_config.entity.dart) | Dart | 29 | 9 | 9 | 47 |
| [lib/domain/entity/base/app/app_config.entity.g.dart](/lib/domain/entity/base/app/app_config.entity.g.dart) | Dart | 701 | 6 | 73 | 780 |
| [lib/domain/entity/base/app/locale_enum.dart](/lib/domain/entity/base/app/locale_enum.dart) | Dart | 9 | 0 | 4 | 13 |
| [lib/domain/entity/base/base.dart](/lib/domain/entity/base/base.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/domain/entity/base/base_crawl.entity.dart](/lib/domain/entity/base/base_crawl.entity.dart) | Dart | 95 | 10 | 17 | 122 |
| [lib/domain/entity/base/log/base_crawl_log.entity.dart](/lib/domain/entity/base/log/base_crawl_log.entity.dart) | Dart | 22 | 9 | 9 | 40 |
| [lib/domain/entity/base/log/base_crawl_log.entity.g.dart](/lib/domain/entity/base/log/base_crawl_log.entity.g.dart) | Dart | 1,010 | 6 | 87 | 1,103 |
| [lib/domain/entity/base/log/log.dart](/lib/domain/entity/base/log/log.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/domain/entity/base/status/base_crawl_status.entity.dart](/lib/domain/entity/base/status/base_crawl_status.entity.dart) | Dart | 57 | 12 | 12 | 81 |
| [lib/domain/entity/base/status/base_crawl_status.entity.g.dart](/lib/domain/entity/base/status/base_crawl_status.entity.g.dart) | Dart | 895 | 8 | 75 | 978 |
| [lib/domain/entity/base/status/status.dart](/lib/domain/entity/base/status/status.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/domain/entity/entity.dart](/lib/domain/entity/entity.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/domain/entity/enums/base_crawl_status_enum.dart](/lib/domain/entity/enums/base_crawl_status_enum.dart) | Dart | 14 | 30 | 11 | 55 |
| [lib/domain/entity/enums/base_crawl_type.dart](/lib/domain/entity/enums/base_crawl_type.dart) | Dart | 9 | 16 | 8 | 33 |
| [lib/domain/entity/enums/enums.dart](/lib/domain/entity/enums/enums.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/domain/entity/gapo/gapo.dart](/lib/domain/entity/gapo/gapo.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/domain/entity/gapo/gp_auth.dart](/lib/domain/entity/gapo/gp_auth.dart) | Dart | 10 | 0 | 4 | 14 |
| [lib/domain/entity/gapo/gp_auth_params.dart](/lib/domain/entity/gapo/gp_auth_params.dart) | Dart | 17 | 1 | 4 | 22 |
| [lib/domain/entity/gapo/gp_auth_params.freezed.dart](/lib/domain/entity/gapo/gp_auth_params.freezed.dart) | Dart | 202 | 15 | 23 | 240 |
| [lib/domain/entity/gapo/gp_auth_params.g.dart](/lib/domain/entity/gapo/gp_auth_params.g.dart) | Dart | 9 | 4 | 4 | 17 |
| [lib/domain/entity/gapo/gpuser.dart](/lib/domain/entity/gapo/gpuser.dart) | Dart | 25 | 0 | 8 | 33 |
| [lib/domain/entity/gapo/gpuser.g.dart](/lib/domain/entity/gapo/gpuser.g.dart) | Dart | 1,350 | 9 | 147 | 1,506 |
| [lib/domain/entity/workplace/workplace.dart](/lib/domain/entity/workplace/workplace.dart) | Dart | 7 | 0 | 1 | 8 |
| [lib/domain/entity/workplace/workplace_attachment.entity.dart](/lib/domain/entity/workplace/workplace_attachment.entity.dart) | Dart | 46 | 12 | 18 | 76 |
| [lib/domain/entity/workplace/workplace_attachment.entity.g.dart](/lib/domain/entity/workplace/workplace_attachment.entity.g.dart) | Dart | 2,060 | 14 | 165 | 2,239 |
| [lib/domain/entity/workplace/workplace_comment.entity.dart](/lib/domain/entity/workplace/workplace_comment.entity.dart) | Dart | 16 | 9 | 5 | 30 |
| [lib/domain/entity/workplace/workplace_comment.entity.g.dart](/lib/domain/entity/workplace/workplace_comment.entity.g.dart) | Dart | 501 | 6 | 42 | 549 |
| [lib/domain/entity/workplace/workplace_community_member.entity.dart](/lib/domain/entity/workplace/workplace_community_member.entity.dart) | Dart | 28 | 12 | 10 | 50 |
| [lib/domain/entity/workplace/workplace_community_member.entity.g.dart](/lib/domain/entity/workplace/workplace_community_member.entity.g.dart) | Dart | 1,960 | 6 | 192 | 2,158 |
| [lib/domain/entity/workplace/workplace_group.entity.dart](/lib/domain/entity/workplace/workplace_group.entity.dart) | Dart | 40 | 9 | 7 | 56 |
| [lib/domain/entity/workplace/workplace_group.entity.g.dart](/lib/domain/entity/workplace/workplace_group.entity.g.dart) | Dart | 2,335 | 11 | 215 | 2,561 |
| [lib/domain/entity/workplace/workplace_group_feed.entity.dart](/lib/domain/entity/workplace/workplace_group_feed.entity.dart) | Dart | 30 | 10 | 8 | 48 |
| [lib/domain/entity/workplace/workplace_group_feed.entity.g.dart](/lib/domain/entity/workplace/workplace_group_feed.entity.g.dart) | Dart | 1,784 | 6 | 160 | 1,950 |
| [lib/domain/entity/workplace/workplace_group_member.entity.dart](/lib/domain/entity/workplace/workplace_group_member.entity.dart) | Dart | 30 | 12 | 10 | 52 |
| [lib/domain/entity/workplace/workplace_group_member.entity.g.dart](/lib/domain/entity/workplace/workplace_group_member.entity.g.dart) | Dart | 2,143 | 6 | 210 | 2,359 |
| [lib/domain/entity/workplace/workplace_user.entity.dart](/lib/domain/entity/workplace/workplace_user.entity.dart) | Dart | 27 | 12 | 9 | 48 |
| [lib/domain/entity/workplace/workplace_user.entity.g.dart](/lib/domain/entity/workplace/workplace_user.entity.g.dart) | Dart | 1,376 | 6 | 112 | 1,494 |
| [lib/domain/repository/facebook_repo.dart](/lib/domain/repository/facebook_repo.dart) | Dart | 4 | 1 | 2 | 7 |
| [lib/domain/repository/gapo_repo.dart](/lib/domain/repository/gapo_repo.dart) | Dart | 17 | 11 | 10 | 38 |
| [lib/domain/repository/repository.dart](/lib/domain/repository/repository.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/domain/repository/workplace_repo.dart](/lib/domain/repository/workplace_repo.dart) | Dart | 18 | 11 | 10 | 39 |
| [lib/domain/usecase/facebook_get_community.usecase.dart](/lib/domain/usecase/facebook_get_community.usecase.dart) | Dart | 19 | 10 | 7 | 36 |
| [lib/domain/usecase/usecase.dart](/lib/domain/usecase/usecase.dart) | Dart | 8 | 0 | 1 | 9 |
| [lib/domain/usecase/workplace_get_all_groups.usecase.dart](/lib/domain/usecase/workplace_get_all_groups.usecase.dart) | Dart | 27 | 10 | 8 | 45 |
| [lib/domain/usecase/workplace_get_app_config.usecase.dart](/lib/domain/usecase/workplace_get_app_config.usecase.dart) | Dart | 20 | 10 | 7 | 37 |
| [lib/domain/usecase/workplace_get_community_members.usecase.dart](/lib/domain/usecase/workplace_get_community_members.usecase.dart) | Dart | 28 | 1 | 7 | 36 |
| [lib/domain/usecase/workplace_get_group_feeds.usecase.dart](/lib/domain/usecase/workplace_get_group_feeds.usecase.dart) | Dart | 28 | 1 | 7 | 36 |
| [lib/domain/usecase/workplace_get_group_members.usecase.dart](/lib/domain/usecase/workplace_get_group_members.usecase.dart) | Dart | 27 | 1 | 7 | 35 |
| [lib/domain/usecase/workplace_get_post_attachments.usecase.dart](/lib/domain/usecase/workplace_get_post_attachments.usecase.dart) | Dart | 27 | 1 | 7 | 35 |
| [lib/domain/usecase/workplace_get_post_comments.usecase.dart](/lib/domain/usecase/workplace_get_post_comments.usecase.dart) | Dart | 27 | 1 | 7 | 35 |
| [lib/flutter_gen/assets.gen.dart](/lib/flutter_gen/assets.gen.dart) | Dart | 68 | 10 | 14 | 92 |
| [lib/flutter_gen/flutter_gen.dart](/lib/flutter_gen/flutter_gen.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/l10n/app_en.arb](/lib/l10n/app_en.arb) | JSON | 4 | 0 | 0 | 4 |
| [lib/l10n/app_localizations.dart](/lib/l10n/app_localizations.dart) | Dart | 50 | 75 | 19 | 144 |
| [lib/l10n/app_localizations_en.dart](/lib/l10n/app_localizations_en.dart) | Dart | 13 | 5 | 7 | 25 |
| [lib/l10n/app_localizations_vi.dart](/lib/l10n/app_localizations_vi.dart) | Dart | 13 | 5 | 7 | 25 |
| [lib/l10n/app_vi.arb](/lib/l10n/app_vi.arb) | JSON | 17 | 0 | 0 | 17 |
| [lib/l10n/l10n.dart](/lib/l10n/l10n.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/main.production.dart](/lib/main.production.dart) | Dart | 8 | 0 | 3 | 11 |
| [lib/main.staging.dart](/lib/main.staging.dart) | Dart | 8 | 0 | 3 | 11 |
| [lib/main.uat.dart](/lib/main.uat.dart) | Dart | 8 | 0 | 3 | 11 |
| [lib/mapper/entity/entity.dart](/lib/mapper/entity/entity.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/mapper/entity/workplace_entity_mapper.auto_mappr.dart](/lib/mapper/entity/workplace_entity_mapper.auto_mappr.dart) | Dart | 660 | 51 | 41 | 752 |
| [lib/mapper/entity/workplace_entity_mapper.dart](/lib/mapper/entity/workplace_entity_mapper.dart) | Dart | 101 | 12 | 12 | 125 |
| [lib/mapper/gp_mapper.auto_mappr.dart](/lib/mapper/gp_mapper.auto_mappr.dart) | Dart | 188 | 60 | 28 | 276 |
| [lib/mapper/gp_mapper.dart](/lib/mapper/gp_mapper.dart) | Dart | 61 | 13 | 10 | 84 |
| [lib/mapper/mapper.dart](/lib/mapper/mapper.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/route/go_router.route.dart](/lib/route/go_router.route.dart) | Dart | 28 | 10 | 8 | 46 |
| [lib/route/go_router.route.g.dart](/lib/route/go_router.route.g.dart) | Dart | 37 | 4 | 18 | 59 |
| [lib/route/route.dart](/lib/route/route.dart) | Dart | 1 | 0 | 1 | 2 |
| [linux/main.cc](/linux/main.cc) | C++ | 5 | 0 | 2 | 7 |
| [linux/my_application.cc](/linux/my_application.cc) | C++ | 74 | 11 | 20 | 105 |
| [linux/my_application.h](/linux/my_application.h) | C++ | 7 | 7 | 5 | 19 |
| [macos/Runner/AppDelegate.swift](/macos/Runner/AppDelegate.swift) | Swift | 8 | 0 | 2 | 10 |
| [macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json](/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json) | JSON | 68 | 0 | 0 | 68 |
| [macos/Runner/Base.lproj/MainMenu.xib](/macos/Runner/Base.lproj/MainMenu.xib) | XML | 343 | 0 | 1 | 344 |
| [macos/Runner/MainFlutterWindow.swift](/macos/Runner/MainFlutterWindow.swift) | Swift | 12 | 0 | 4 | 16 |
| [macos/RunnerTests/RunnerTests.swift](/macos/RunnerTests/RunnerTests.swift) | Swift | 7 | 2 | 4 | 13 |
| [pubspec.yaml](/pubspec.yaml) | YAML | 67 | 6 | 1 | 74 |
| [test/widget_test.dart](/test/widget_test.dart) | Dart | 14 | 10 | 6 | 30 |
| [web/index.html](/web/index.html) | HTML | 35 | 16 | 17 | 68 |
| [web/manifest.json](/web/manifest.json) | JSON | 35 | 0 | 1 | 36 |
| [windows/runner/flutter_window.cpp](/windows/runner/flutter_window.cpp) | C++ | 49 | 7 | 16 | 72 |
| [windows/runner/flutter_window.h](/windows/runner/flutter_window.h) | C++ | 20 | 5 | 9 | 34 |
| [windows/runner/main.cpp](/windows/runner/main.cpp) | C++ | 30 | 4 | 10 | 44 |
| [windows/runner/resource.h](/windows/runner/resource.h) | C++ | 9 | 6 | 2 | 17 |
| [windows/runner/utils.cpp](/windows/runner/utils.cpp) | C++ | 54 | 2 | 10 | 66 |
| [windows/runner/utils.h](/windows/runner/utils.h) | C++ | 8 | 6 | 6 | 20 |
| [windows/runner/win32_window.cpp](/windows/runner/win32_window.cpp) | C++ | 210 | 24 | 55 | 289 |
| [windows/runner/win32_window.h](/windows/runner/win32_window.h) | C++ | 48 | 31 | 24 | 103 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)