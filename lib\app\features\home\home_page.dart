import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_fbwp_crawler/app/app.dart';
import 'package:gp_fbwp_crawler/domain/domain.dart';
import 'package:gp_fbwp_crawler/flutter_gen/assets.gen.dart';
import 'package:gp_fbwp_crawler/l10n/app_localizations.dart';
import 'package:gp_fbwp_crawler/route/go_router.route.dart';

late S l10n;

DateTime? startDateTime;

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final TextEditingController textEditingController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    l10n = S.of(context)!;
    return BlocProvider<HomeBloc>(
      create: (context) => GetIt.I<HomeBloc>(),
      child:
          BlocBuilder<AppConfigBloc, AppConfigState>(builder: (context, state) {
        final stateLocale = state.appConfigEntity.appLocale.locale;
        textEditingController.text = state.appConfigEntity.workPlaceToken ?? '';

        return Scaffold(
          appBar: AppBar(),
          body: Column(
            children: [
              const SizedBox(height: 10),
              Row(
                children: [
                  const Spacer(),
                  LanguageWidget(
                    locale: stateLocale,
                  )
                ],
              ),
              Expanded(
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 50),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          TextFormField(
                            controller: textEditingController,
                            maxLines: 3,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter token';
                              }
                              return null;
                            },
                            decoration: const InputDecoration(
                                labelText: 'WorkPlace Token',
                                icon: Icon(Icons.key)),
                          ),
                          const SizedBox(height: 20),
                          TextButton(
                            onPressed: () {
                              if (_formKey.currentState!.validate()) {
                                final token = textEditingController.text;

                                GetIt.I<AppConfigBloc>().add(
                                  AppConfigUpdateEvent(token: token),
                                );

                                const CrawlRouteData().go(context);

                                startDateTime = DateTime.now();
                              }
                            },
                            style: ButtonStyle(
                                padding: MaterialStateProperty.all(
                                    const EdgeInsets.all(10)),
                                backgroundColor:
                                    MaterialStateProperty.all(Colors.green)),
                            child: const Text(
                              'Sync WorkPlace data to GapoWork',
                              style: TextStyle(color: Colors.white),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              Text('Version: ${state.appConfigEntity.appVersion}'),
              const SizedBox(height: 10)
            ],
          ),
        );
      }),
    );
  }
}

class LanguageWidget extends StatelessWidget {
  const LanguageWidget({super.key, required this.locale});

  final Locale locale;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        TextButton(
            onPressed: () {
              final newLocale = locale == GPAppLocale.en.locale
                  ? GPAppLocale.vi
                  : GPAppLocale.en;
              context
                  .read<AppConfigBloc>()
                  .add(AppConfigLanguageChangedEvent(locale: newLocale));
            },
            child: locale == GPAppLocale.en.locale
                ? _lang(label: 'English', icon: Assets.images.engFlag.svg())
                : _lang(
                    label: 'Tiếng Việt', icon: Assets.images.vnFlag.svg())),
      ],
    );
  }

  Widget _lang({required String label, required Widget icon}) {
    return Row(
      children: [
        icon,
        const SizedBox(width: 10),
        Text(label),
      ],
    );
  }
}
